"""
对比校验文字版本
"""
from typing import List

def load_markdown_file(file_name) -> str:
    """ 加载Markdown文件，过滤其中所有的非汉字字符, 标题也过滤掉

    Args:
        file_name (str): 文件名

    Return:
        处理后的字符串
    """
    # 读取文件内容
    with open(file_name, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按行分割
    lines = content.split('\n')
    
    # 过滤掉标题行(以#开头的行)和空行
    filtered_lines = []
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue
        filtered_lines.append(line)
    
    # 合并所有行
    text = ''.join(filtered_lines)
    
    # 只保留汉字
    chinese_chars = ''
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            chinese_chars += char
            
    return chinese_chars

def compare(check_file_name, ref_file_name, 
            check_left_file_name, ref_left_file_naem, 
            check_num=10) -> List[str]:
    """
    逐字对比校验两个Markdown文件，返回不一致的结果。
    如果检测到check_num个不一致的汉字，则停止对比，返回这些不一致汉字，包括其上下文，上下文窗口为10。
    没有对比完的剩余内容保存在对应的left文件中。

    Args:
        check_file_name (str): 待校验文件名
        ref_file_name (str): 参考文件名
        check_file_name (str): 尚未完成对比的待校验内容文件
        ref_file_name (str): 尚未完成对比的参考内容文件名
        check_num (int, optional): 不一致的结果数. Defaults to 10.

    Returns:
        List[str]: 不一致的汉字及其上下文列表
    """
    # 加载并处理两个文件
    check_text = load_markdown_file(check_file_name)
    ref_text = load_markdown_file(ref_file_name)
    
    # 存储不一致结果
    diff_results = []
    
    # 当前处理位置
    pos = 0
    # 前一个异常字
    pre_diff_pos = 0
    
    # 逐字对比
    while pos < min(len(check_text), len(ref_text)):
        if check_text[pos] != ref_text[pos]:
            # 获取上下文
            start = max(0, pos - 10)
            end = min(len(check_text), pos + 11)
            context = check_text[start:end]
            ref_context = ref_text[start:end]
            
            # 添加到结果中
            diff_results.append(f"字 {check_text[pos]}:  {ref_text[pos]}, 当前文本：{context},  (参考文本: {ref_context})")
            
            # 连着两个字都错了，有可能是错位，先退出，暂停后续处理
            if pre_diff_pos == pos - 1:
                break
            else:
                pre_diff_pos = pos

            # 如果达到指定数量，停止对比
            if len(diff_results) >= check_num:
                break
                
        pos += 1
        
    # 保存未完成对比的内容
    with open(check_left_file_name, 'w', encoding='utf-8') as f:
        f.write(check_text[pos:])
        
    with open(ref_left_file_naem, 'w', encoding='utf-8') as f:
        f.write(ref_text[pos:])
        
    return diff_results

if __name__ == '__main__':
    data_dir = 'data/books/紫微斗数全书/tmp/'
    file1 = data_dir + 'check.md'
    file2 = data_dir + 'ref.md'
    content_1 = load_markdown_file(file1)
    content_2 = load_markdown_file(file2)
    res_file1 = file1.split('.')[0] + '_left.md'
    res_file2 = file2.split('.')[0] + '_left.md'
    diff_content = compare(file1, file2, res_file1, res_file2, 10)
    diff_file = file1.split('.')[0] + '_check.txt'
    with open(diff_file, 'w') as fw:
        for line in diff_content:
            fw.write(line + '\n')
