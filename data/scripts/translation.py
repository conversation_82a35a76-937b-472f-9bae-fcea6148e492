import json
import logging
import logging.config
import os
import sys
from llm.llm_client import LLMClient
from llm.llm_type import ModelType

# 配置日志
logging.config.fileConfig('logging.conf')
logger = logging.getLogger(__name__)

class Translation:
    def __init__(self, model_type=ModelType.QWEN) -> None:
        self.llm_client = LLMClient(model_type)
        self.prompt_template = """
# 角色：一个精通紫微斗数的命理师
# 任务：翻译《紫微斗数全书》里的内容。用最简单通俗易懂的白话解释以下内容，参考其中的注解部分：
{}
# 输出：只包括翻译内容本身，不要输出其他无关话语
        """
    
    def translate_json_file(self, input_file: str, output_file: str):
        """
        读取JSON文件，翻译其中的文言文内容，并保存到新的JSON文件
        
        Args:
            input_file: 输入JSON文件的路径
            output_file: 输出JSON文件的路径
        """
        try:
            # 避免覆盖已有文件    
            if os.path.exists(output_file):
                logger.error("output_file exists:%s", output_file)
                return False

            # 读取输入JSON文件
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 开始翻译处理
            self._process_content(data)
                
            # 保存到输出文件
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info("翻译完成，已保存到 %s", output_file)
            return True
        
        except Exception as e:
            logger.error("翻译过程出错: %s", str(e))
            return False
    
    def _process_content(self, data):
        """递归处理JSON数据中的内容进行翻译"""
        if isinstance(data, dict):
            # 处理章节
            if "chapters" in data:
                for chapter in data["chapters"]:
                    self._process_content(chapter)
            
            # 处理子章节
            if "subchapters" in data:
                for subchapter in data["subchapters"]:
                    self._process_content(subchapter)
            
            # 处理具体内容
            if "content" in data and isinstance(data["content"], list):
                for item in data["content"]:
                    self._translate_item(item)
    
    def _translate_item(self, item):
        """翻译内容项中的text和quote"""
        if not isinstance(item, dict):
            return
        
        # 检查是否已经有翻译
        if "trans" in item:
            return
        
        # 翻译文本
        if "text" in item and item.get("type") == "paragraph":
            text_to_translate = item["text"]
            if text_to_translate == "":
                return
            # 如果有注释，也加入翻译内容
            if "quote" in item and item["quote"].startswith("注解"):
                text_to_translate += "\n" + item["quote"]
            
            # 调用LLM进行翻译
            try:
                prompt = self.prompt_template.format(text_to_translate)
                translated_text = self.llm_client.get_from_llm(prompt)
                item["trans"] = translated_text
                logger.info("翻译成功: %s", text_to_translate)
            except Exception as e:
                logger.error("翻译失败: %s", str(e))
                item["trans"] = "翻译失败"


if __name__ == '__main__':
    # 创建翻译器实例
    trans = Translation(ModelType.DEEPSEEK)
    
    # 定义输入和输出文件路径
    input_file = "data/books/紫微斗数全书/pub/紫微斗数全书卷一翻译中间版.json"
    output_file = "data/books/紫微斗数全书/pub/紫微斗数全书卷一翻译结果.json"
    
    # 执行翻译
    trans.translate_json_file(input_file, output_file)
