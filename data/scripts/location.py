import argparse
import sys
import json

class LocationItem:
    def __init__(self, label: str, value: float):
        self.label = label
        self.value = value

    def to_dict(self) -> dict:
        return {
            "label": self.label,
            "value": self.value
        }

def load_json_file(filename: str) -> dict:
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def convert_location(location: dict) -> list:
    result = [] 
    for item in location:
        first_loc = item['text']
        if first_loc == '未知地区':
            continue
        if 'children' in item:
            for child in item['children']:
                second_loc = child['text']
                if len(second_loc) < 2:
                    sys.stderr.write(f"second_loc: {second_loc} is too short: {first_loc}\n")
                second_loc_value = child['gisGcj02Lng']
                result.append(LocationItem(label=f"{first_loc} {second_loc}", value=second_loc_value).to_dict())
                if 'children' in child:
                    for grandchild in child['children']:
                        third_loc = grandchild['text']
                        if len(third_loc) < 2:  
                            sys.stderr.write(f"third_loc: {third_loc} is too short: {first_loc} {second_loc}\n")
                        third_loc_value = grandchild['gisGcj02Lng']
                        result.append(LocationItem(label=f"{first_loc} {second_loc} {third_loc}", value=third_loc_value).to_dict())
    return result

if __name__ == '__main__':
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='提取json格式的location文件转成前端需要的格式')

    # 添加命令行参数
    parser.add_argument('--filename', '-f', type=str, required=True, help='location文件地址')

    args = parser.parse_args()

    location = load_json_file(args.filename)
    result = convert_location(location)
    
    # 输出为格式化的JSON
    output = json.dumps(result, ensure_ascii=False, indent=2)
    print(output)