import logging
import time
from pathlib import Path
import pandas as pd
from docling.document_converter import DocumentConverter
_log = logging.getLogger(__name__)


def main():
    logging.basicConfig(level=logging.INFO)

    data_dir = '/Users/<USER>/Project/fate_explorer/data/test_data/'
    file_name = '紫微斗数全书_s.pdf'
    source_file = data_dir + file_name
    output_dir = data_dir + 'img'

    doc_converter = DocumentConverter()

    start_time = time.time()

    conv_res = doc_converter.convert(source_file)

    #output_dir.mkdir(parents=True, exist_ok=True)

    doc_filename = conv_res.input.file.stem

    # Export tables
    for table_ix, table in enumerate(conv_res.document.tables):
        table_df: pd.DataFrame = table.export_to_dataframe()
        print(f"## Table {table_ix}")
        print(table_df.to_markdown())

        # Save the table as csv
        element_csv_filename = output_dir + \
            f"{doc_filename}-table-{table_ix+1}.csv"
        _log.info(f"Saving CSV table to {element_csv_filename}")
        table_df.to_csv(element_csv_filename)

        # Save the table as html
        element_html_filename = output_dir + \
            f"{doc_filename}-table-{table_ix+1}.html"
        _log.info(f"Saving HTML table to {element_html_filename}")
        with open(element_html_filename, "w", encoding='utf-8') as fp:
            fp.write(table.export_to_html())

    end_time = time.time() - start_time

    _log.info(
        f"Document converted and tables exported in {end_time:.2f} seconds.")

if __name__ == "__main__":
    main()
