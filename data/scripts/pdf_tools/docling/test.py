import logging
import time
from docling.datamodel.base_models import InputFormat
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling_core.types.doc import ImageRefMode, PictureItem, TableItem
from docling.datamodel.pipeline_options import PdfPipelineOptions, TableFormerMode

print('starting...')
_log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
IMAGE_RESOLUTION_SCALE = 2.0
pipeline_options = PdfPipelineOptions()
pipeline_options.images_scale = IMAGE_RESOLUTION_SCALE
pipeline_options.generate_page_images = True
#pipeline_options.do_table_structure = False
# 生成在页面上引用的图片文件
pipeline_options.generate_picture_images = True
# pipeline_options.table_structure_options.mode = TableFormerMode.ACCURATE  # use more accurate TableFormer model
# pipeline_options.table_structure_options.do_cell_matching = True # uses text cells predicted from table structure model
print('pipeling options ready')
doc_converter = DocumentConverter(
    format_options={
        InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
    }
)
print('converter created')
data_dir = '/Users/<USER>/Project/fate_explorer/data/test_data/'
file_name = '中州派紫微斗数初级讲义_5.pdf'
#file_name = '紫微斗数全书_s.pdf'
source_file = data_dir + file_name
output_file = data_dir + file_name.split('.')[0] + '.md'
start_time = time.time()
result = doc_converter.convert(source_file)
print('converted')
# Save page images
#for page_no, page in result.document.pages.items():
#    page_no = page.page_no
#    page_image_filename = data_dir + f"{file_name}-{page_no}.png"
#    with open(page_image_filename, "wb") as fp:
#        page.image.pil_image.save(fp, format="PNG")

# Save images of figures and tables
table_counter = 0
picture_counter = 0
for element, _level in result.document.iterate_items():
    if isinstance(element, TableItem):
        table_counter += 1
        element_image_filename = (
            data_dir  + f"{file_name}-table-{table_counter}.png"
        )
        with open(element_image_filename,"wb") as fp:
            element.get_image(result.document).save(fp, "PNG")

    if isinstance(element, PictureItem):
        picture_counter += 1
        element_image_filename = (
            data_dir + f"{file_name}-picture-{picture_counter}.png"
        )
        with open(element_image_filename,"wb") as fp:
            element.get_image(result.document).save(fp, "PNG")

# Save markdown with externally referenced pictures
md_filename =  data_dir +  f"{file_name}-with-image-refs.md"
result.document.save_as_markdown(md_filename, image_mode=ImageRefMode.REFERENCED)
json_filename =  data_dir +  f"{file_name}-with-image-refs.json"
result.document.save_as_json(json_filename, image_mode=ImageRefMode.REFERENCED)

end_time = time.time() - start_time
_log.info(f"Document converted and figures exported in {end_time:.2f} seconds.")
