from paddleocr import PaddleOCR, draw_ocr

# Paddleocr supports Chinese, English, French, German, Korean and Japanese
# You can set the parameter `lang` as `ch`, `en`, `french`, `german`, `korean`, `japan`
# to switch the language model in order

def run_orc(source_file):
    ocr = PaddleOCR(use_angle_cls=True, 
                    lang='ch',
                    drop_score=0.5,
                    max_text_length=30,
                    det_limit_side_len=960,
                    rec_image_shape='3,32,320',
                    det_model_dir='/Users/<USER>/.paddleocr/whl/det/ch/ch_PP-OCRv4_det_server_infer',
                    rec_char_dict_path='/Users/<USER>/miniconda3/envs/paddle/lib/python3.12/site-packages/paddleocr/ppocr/utils/ppocr_keys_v2.txt',
                    rec_model_dir='/Users/<USER>/.paddleocr/whl/rec/ch/PP-OCRv4_server_rec_doc_infer') # need to run only once to download and load model into memory
    result = ocr.ocr(source_file, cls=True)
    print("识别条数：%d", len(result[0]))
    print(result[0])
    return result[0]

def format_output(ocr_results):
    # 按从右到左排序（根据每个文本块的左上角x坐标降序排列）
    sorted_results = sorted(ocr_results, key=lambda x: -x[0][0][0])

    # 提取文本并按顺序拼接
    text_parts = [result[1][0] for result in sorted_results]
    final_text = "".join(text_parts)
    return final_text

def draw(ocr_results, img_path, save_file):
    # draw result
    from PIL import Image
    result = ocr_results
    image = Image.open(img_path).convert('RGB')
    boxes = [line[0] for line in result]
    txts = [line[1][0] for line in result]
    scores = [line[1][1] for line in result]
    im_show = draw_ocr(image, boxes, txts, scores, drop_score=0.3, font_path='/Users/<USER>/.paddleocr/simfang.ttf')
    im_show = Image.fromarray(im_show)
    im_show.save(save_file)


if __name__ == '__main__':
    data_dir = '/Users/<USER>/Project/fate_explorer/data/test_data/'
    file_name = '1.jpg'
    input_file = data_dir + file_name
    result = run_orc(input_file)
    sorted_result = format_output(result)
    output_file = input_file.split('.')[0] + '.res' 
    with open(output_file, 'w') as fw:
        fw.write(sorted_result) 
    output_img_file = output_file + '.jpg'
    draw(result, input_file, output_img_file)
