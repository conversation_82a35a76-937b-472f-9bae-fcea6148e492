from paddlex import create_pipeline
from pathlib import Path
import os

current_dir = os.path.dirname(__file__)
config_file = os.path.join(current_dir, "./OCR.yaml")
pipeline = create_pipeline(pipeline=config_file)

data_dir = '/Users/<USER>/Project/fate_explorer/data/test_data/'
file_name = '1.jpg'
#file_name = '紫微斗数全书_s.pdf'
source_file = data_dir + file_name
output_dir = data_dir  

def ocr():
    output = pipeline.predict(
        input=source_file,
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
    )
    for res in output:
        res.print()
        #res.save_to_img(save_path=output_dir)
        res.save_to_json(save_path=output_dir)

def table_recog():    

    output = pipeline.predict(
        input=source_file,
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
    )

    for res in output:
        res.print()
        res.save_to_html(save_path=output_dir)
        res.save_to_json(save_path=output_dir)

ocr()