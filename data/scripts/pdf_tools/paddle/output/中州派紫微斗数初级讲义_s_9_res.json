{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 9, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[444, 2], [493, 2], [493, 18], [444, 18]], [[1191, 0], [1409, 0], [1409, 28], [1191, 28]], [[1073, 28], [1207, 32], [1206, 73], [1071, 69]], [[226, 62], [363, 62], [363, 101], [226, 101]], [[235, 105], [277, 105], [277, 135], [235, 135]], [[554, 108], [581, 108], [581, 136], [554, 136]], [[1070, 131], [1086, 131], [1086, 144], [1070, 144]], [[1168, 144], [1189, 144], [1189, 168], [1168, 168]], [[1270, 137], [1294, 136], [1297, 196], [1272, 197]], [[1342, 140], [1365, 140], [1365, 197], [1342, 197]], [[1169, 159], [1190, 162], [1186, 189], [1165, 186]], [[1054, 177], [1086, 177], [1086, 202], [1054, 202]], [[565, 190], [577, 190], [577, 211], [565, 211]], [[1168, 184], [1186, 184], [1186, 202], [1168, 202]], [[1061, 206], [1124, 206], [1124, 815], [1061, 815]], [[1198, 216], [1226, 216], [1226, 284], [1198, 284]], [[1238, 225], [1256, 225], [1256, 266], [1238, 266]], [[1274, 220], [1291, 220], [1291, 259], [1274, 259]], [[1344, 218], [1366, 218], [1366, 261], [1344, 261]], [[440, 262], [453, 262], [453, 285], [440, 285]], [[558, 259], [577, 259], [577, 285], [558, 285]], [[247, 289], [270, 289], [270, 324], [247, 324]], [[460, 292], [488, 292], [488, 324], [460, 324]], [[1240, 284], [1252, 284], [1252, 307], [1240, 307]], [[314, 298], [328, 298], [328, 314], [314, 314]], [[216, 360], [314, 360], [314, 393], [216, 393]], [[1038, 365], [1061, 365], [1061, 431], [1038, 431]], [[163, 390], [242, 390], [242, 447], [163, 447]], [[240, 402], [274, 402], [274, 438], [240, 438]], [[296, 404], [333, 404], [333, 436], [296, 436]], [[354, 402], [389, 402], [389, 436], [354, 436]], [[416, 404], [444, 404], [444, 432], [416, 432]], [[472, 402], [509, 402], [509, 434], [472, 434]], [[533, 406], [567, 406], [567, 431], [533, 431]], [[240, 432], [275, 432], [275, 468], [240, 468]], [[296, 434], [339, 434], [339, 468], [296, 468]], [[354, 434], [393, 434], [393, 468], [354, 468]], [[414, 434], [453, 434], [453, 468], [414, 468]], [[467, 432], [509, 432], [509, 466], [467, 466]], [[528, 434], [570, 434], [570, 464], [528, 464]], [[1168, 432], [1181, 432], [1181, 448], [1168, 448]], [[239, 457], [281, 457], [281, 500], [239, 500]], [[295, 461], [340, 461], [340, 500], [295, 500]], [[354, 463], [395, 463], [395, 496], [354, 496]], [[414, 463], [453, 463], [453, 496], [414, 496]], [[468, 457], [512, 457], [512, 498], [468, 498]], [[530, 464], [570, 464], [570, 494], [530, 494]], [[240, 489], [286, 489], [286, 535], [240, 535]], [[293, 489], [344, 489], [344, 535], [293, 535]], [[470, 491], [510, 491], [510, 525], [470, 525]], [[1037, 482], [1065, 482], [1065, 553], [1037, 553]], [[351, 493], [398, 493], [398, 532], [351, 532]], [[409, 493], [454, 493], [454, 532], [409, 532]], [[532, 496], [568, 496], [568, 521], [532, 521]], [[353, 525], [400, 525], [400, 562], [353, 562]], [[412, 523], [456, 523], [456, 562], [412, 562]], [[468, 516], [514, 516], [514, 562], [468, 562]], [[184, 526], [221, 526], [221, 560], [184, 560]], [[242, 526], [284, 526], [284, 560], [242, 560]], [[298, 526], [340, 526], [340, 560], [298, 560]], [[528, 523], [570, 523], [570, 557], [528, 557]], [[188, 560], [223, 560], [223, 587], [188, 587]], [[242, 558], [284, 558], [284, 588], [242, 588]], [[298, 558], [344, 558], [344, 588], [298, 588]], [[353, 558], [400, 558], [400, 588], [353, 588]], [[410, 558], [456, 558], [456, 588], [410, 588]], [[470, 560], [510, 560], [510, 585], [470, 585]], [[532, 560], [575, 560], [575, 583], [532, 583]], [[37, 604], [753, 606], [752, 649], [37, 647]], [[0, 639], [120, 644], [119, 677], [0, 673]], [[1159, 684], [1194, 681], [1199, 748], [1164, 751]], [[1090, 738], [1102, 734], [1108, 752], [1096, 756]], [[1168, 744], [1191, 744], [1191, 801], [1168, 801]], [[1245, 769], [1263, 769], [1263, 803], [1245, 803]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "text_rec_score_thresh": 0.0, "rec_texts": ["级讲义", "中州派紫薇斗数初级讲义", "5起大限表", "安十二宫天干表", "卯", "丑", "4", "2", "财帛宫", "迁移富", "弟", "阴阳", "", "品", "敢", "=", "2", "8", "-", "n", "奖", "2", "王", "2", "丁", "定五行局表", "木三局", "", "丑", "寅卯", "展", "午未", "申晒", "成亥", "水二局", "火六局", "木三局", "土五局", "金四局", "火六局", "22", "火六局", "土五局", "金四局", "木三局", "水二局", "土五局", "土五局", "木三周", "火六局", "金四局", "水二局", "金四局", "木三局", "火六局", "水二局", "土五局", "丁王", "木三局", "金四局", "金四局", "戊奖", "金四局", "水二局", "土五局", "火六局", "木三局", "水二局", "例：乙未年生人，命宫在戊寅。查[本生年干]乙庚栏，对照[命宫地支]寅卵栏,", "即得土五局。", "", "/", "", ""], "rec_scores": [0.8051436543464661, 0.9989356398582458, 0.9995206594467163, 0.9648655652999878, 0.9173920154571533, 0.7982889413833618, 0.1391192525625229, 0.32065653800964355, 0.8104743361473083, 0.8311395049095154, 0.8013571500778198, 0.9795961380004883, 0.0, 0.2350022941827774, 0.3994383215904236, 0.42477092146873474, 0.6361832618713379, 0.8779513835906982, 0.6078248620033264, 0.41303423047065735, 0.3694060742855072, 0.9870406985282898, 0.7916316390037537, 0.7166469693183899, 0.883217453956604, 0.9986437559127808, 0.9976946711540222, 0.0, 0.9964281916618347, 0.7854510545730591, 0.36258548498153687, 0.8953193426132202, 0.7617263197898865, 0.741248607635498, 0.9958338141441345, 0.9545605778694153, 0.9822158813476562, 0.9571393132209778, 0.9953240752220154, 0.8049959540367126, 0.9962488412857056, 0.9217431545257568, 0.9953798651695251, 0.9969155192375183, 0.9929801821708679, 0.8407472968101501, 0.9830703735351562, 0.8733952641487122, 0.6514862179756165, 0.9935824871063232, 0.9991922378540039, 0.9918878674507141, 0.9867019653320312, 0.9872042536735535, 0.9895503520965576, 0.991152286529541, 0.9631158709526062, 0.6790405511856079, 0.929290771484375, 0.9939829707145691, 0.9968401789665222, 0.7883269786834717, 0.9996317028999329, 0.9994743466377258, 0.9790074229240417, 0.9802312850952148, 0.9301678538322449, 0.9993575215339661, 0.9731519818305969, 0.9992381930351257, 0.0, 0.3986750841140747, 0.0, 0.0], "rec_polys": [[[444, 2], [493, 2], [493, 18], [444, 18]], [[1191, 0], [1409, 0], [1409, 28], [1191, 28]], [[1073, 28], [1207, 32], [1206, 73], [1071, 69]], [[226, 62], [363, 62], [363, 101], [226, 101]], [[235, 105], [277, 105], [277, 135], [235, 135]], [[554, 108], [581, 108], [581, 136], [554, 136]], [[1070, 131], [1086, 131], [1086, 144], [1070, 144]], [[1168, 144], [1189, 144], [1189, 168], [1168, 168]], [[1270, 137], [1294, 136], [1297, 196], [1272, 197]], [[1342, 140], [1365, 140], [1365, 197], [1342, 197]], [[1169, 159], [1190, 162], [1186, 189], [1165, 186]], [[1054, 177], [1086, 177], [1086, 202], [1054, 202]], [[565, 190], [577, 190], [577, 211], [565, 211]], [[1168, 184], [1186, 184], [1186, 202], [1168, 202]], [[1061, 206], [1124, 206], [1124, 815], [1061, 815]], [[1198, 216], [1226, 216], [1226, 284], [1198, 284]], [[1238, 225], [1256, 225], [1256, 266], [1238, 266]], [[1274, 220], [1291, 220], [1291, 259], [1274, 259]], [[1344, 218], [1366, 218], [1366, 261], [1344, 261]], [[440, 262], [453, 262], [453, 285], [440, 285]], [[558, 259], [577, 259], [577, 285], [558, 285]], [[247, 289], [270, 289], [270, 324], [247, 324]], [[460, 292], [488, 292], [488, 324], [460, 324]], [[1240, 284], [1252, 284], [1252, 307], [1240, 307]], [[314, 298], [328, 298], [328, 314], [314, 314]], [[216, 360], [314, 360], [314, 393], [216, 393]], [[1038, 365], [1061, 365], [1061, 431], [1038, 431]], [[163, 390], [242, 390], [242, 447], [163, 447]], [[240, 402], [274, 402], [274, 438], [240, 438]], [[296, 404], [333, 404], [333, 436], [296, 436]], [[354, 402], [389, 402], [389, 436], [354, 436]], [[416, 404], [444, 404], [444, 432], [416, 432]], [[472, 402], [509, 402], [509, 434], [472, 434]], [[533, 406], [567, 406], [567, 431], [533, 431]], [[240, 432], [275, 432], [275, 468], [240, 468]], [[296, 434], [339, 434], [339, 468], [296, 468]], [[354, 434], [393, 434], [393, 468], [354, 468]], [[414, 434], [453, 434], [453, 468], [414, 468]], [[467, 432], [509, 432], [509, 466], [467, 466]], [[528, 434], [570, 434], [570, 464], [528, 464]], [[1168, 432], [1181, 432], [1181, 448], [1168, 448]], [[239, 457], [281, 457], [281, 500], [239, 500]], [[295, 461], [340, 461], [340, 500], [295, 500]], [[354, 463], [395, 463], [395, 496], [354, 496]], [[414, 463], [453, 463], [453, 496], [414, 496]], [[468, 457], [512, 457], [512, 498], [468, 498]], [[530, 464], [570, 464], [570, 494], [530, 494]], [[240, 489], [286, 489], [286, 535], [240, 535]], [[293, 489], [344, 489], [344, 535], [293, 535]], [[470, 491], [510, 491], [510, 525], [470, 525]], [[1037, 482], [1065, 482], [1065, 553], [1037, 553]], [[351, 493], [398, 493], [398, 532], [351, 532]], [[409, 493], [454, 493], [454, 532], [409, 532]], [[532, 496], [568, 496], [568, 521], [532, 521]], [[353, 525], [400, 525], [400, 562], [353, 562]], [[412, 523], [456, 523], [456, 562], [412, 562]], [[468, 516], [514, 516], [514, 562], [468, 562]], [[184, 526], [221, 526], [221, 560], [184, 560]], [[242, 526], [284, 526], [284, 560], [242, 560]], [[298, 526], [340, 526], [340, 560], [298, 560]], [[528, 523], [570, 523], [570, 557], [528, 557]], [[188, 560], [223, 560], [223, 587], [188, 587]], [[242, 558], [284, 558], [284, 588], [242, 588]], [[298, 558], [344, 558], [344, 588], [298, 588]], [[353, 558], [400, 558], [400, 588], [353, 588]], [[410, 558], [456, 558], [456, 588], [410, 588]], [[470, 560], [510, 560], [510, 585], [470, 585]], [[532, 560], [575, 560], [575, 583], [532, 583]], [[37, 604], [753, 606], [752, 649], [37, 647]], [[0, 639], [120, 644], [119, 677], [0, 673]], [[1159, 684], [1194, 681], [1199, 748], [1164, 751]], [[1090, 738], [1102, 734], [1108, 752], [1096, 756]], [[1168, 744], [1191, 744], [1191, 801], [1168, 801]], [[1245, 769], [1263, 769], [1263, 803], [1245, 803]]], "rec_boxes": [[444, 2, 493, 18], [1191, 0, 1409, 28], [1071, 28, 1207, 73], [226, 62, 363, 101], [235, 105, 277, 135], [554, 108, 581, 136], [1070, 131, 1086, 144], [1168, 144, 1189, 168], [1270, 136, 1297, 197], [1342, 140, 1365, 197], [1165, 159, 1190, 189], [1054, 177, 1086, 202], [565, 190, 577, 211], [1168, 184, 1186, 202], [1061, 206, 1124, 815], [1198, 216, 1226, 284], [1238, 225, 1256, 266], [1274, 220, 1291, 259], [1344, 218, 1366, 261], [440, 262, 453, 285], [558, 259, 577, 285], [247, 289, 270, 324], [460, 292, 488, 324], [1240, 284, 1252, 307], [314, 298, 328, 314], [216, 360, 314, 393], [1038, 365, 1061, 431], [163, 390, 242, 447], [240, 402, 274, 438], [296, 404, 333, 436], [354, 402, 389, 436], [416, 404, 444, 432], [472, 402, 509, 434], [533, 406, 567, 431], [240, 432, 275, 468], [296, 434, 339, 468], [354, 434, 393, 468], [414, 434, 453, 468], [467, 432, 509, 466], [528, 434, 570, 464], [1168, 432, 1181, 448], [239, 457, 281, 500], [295, 461, 340, 500], [354, 463, 395, 496], [414, 463, 453, 496], [468, 457, 512, 498], [530, 464, 570, 494], [240, 489, 286, 535], [293, 489, 344, 535], [470, 491, 510, 525], [1037, 482, 1065, 553], [351, 493, 398, 532], [409, 493, 454, 532], [532, 496, 568, 521], [353, 525, 400, 562], [412, 523, 456, 562], [468, 516, 514, 562], [184, 526, 221, 560], [242, 526, 284, 560], [298, 526, 340, 560], [528, 523, 570, 557], [188, 560, 223, 587], [242, 558, 284, 588], [298, 558, 344, 588], [353, 558, 400, 588], [410, 558, 456, 588], [470, 560, 510, 585], [532, 560, 575, 583], [37, 604, 753, 649], [0, 639, 120, 677], [1159, 681, 1199, 751], [1090, 734, 1108, 756], [1168, 744, 1191, 801], [1245, 769, 1263, 803]]}