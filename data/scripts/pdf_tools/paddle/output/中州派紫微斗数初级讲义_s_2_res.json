{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 2, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[1186, 0], [1414, 0], [1414, 35], [1186, 35]], [[0, 23], [758, 32], [758, 69], [0, 60]], [[1088, 39], [1128, 39], [1128, 71], [1088, 71]], [[1109, 35], [1258, 35], [1258, 74], [1109, 74]], [[0, 55], [749, 66], [749, 103], [0, 92]], [[939, 71], [1193, 73], [1193, 106], [938, 104]], [[0, 87], [746, 99], [745, 138], [0, 126]], [[932, 101], [1130, 105], [1129, 138], [931, 134]], [[46, 124], [760, 133], [759, 167], [45, 158]], [[933, 135], [1044, 135], [1044, 168], [933, 168]], [[0, 156], [560, 161], [559, 195], [0, 190]], [[933, 168], [1168, 168], [1168, 200], [933, 200]], [[44, 186], [744, 195], [744, 229], [44, 220]], [[733, 206], [753, 206], [753, 218], [733, 218]], [[956, 197], [1228, 199], [1228, 230], [956, 229]], [[0, 216], [760, 225], [759, 261], [0, 252]], [[953, 225], [1677, 229], [1677, 262], [952, 259]], [[0, 248], [591, 255], [591, 287], [0, 280]], [[911, 253], [1679, 259], [1679, 293], [910, 287]], [[44, 280], [758, 284], [758, 317], [44, 314]], [[911, 282], [1681, 287], [1680, 321], [910, 315]], [[0, 310], [723, 314], [723, 347], [0, 344]], [[911, 314], [1681, 317], [1680, 351], [910, 347]], [[39, 340], [760, 342], [760, 376], [39, 374]], [[911, 344], [1682, 347], [1682, 381], [910, 377]], [[0, 370], [760, 374], [759, 406], [0, 402]], [[912, 372], [1681, 379], [1680, 413], [912, 406]], [[0, 402], [140, 402], [140, 434], [0, 434]], [[912, 404], [1684, 409], [1684, 443], [912, 438]], [[30, 429], [758, 433], [758, 472], [30, 468]], [[911, 438], [1682, 441], [1682, 475], [910, 471]], [[11, 463], [756, 466], [756, 500], [10, 496]], [[912, 468], [1323, 470], [1323, 502], [912, 500]], [[0, 494], [756, 496], [756, 530], [0, 528]], [[954, 498], [1684, 500], [1684, 533], [954, 532]], [[0, 525], [674, 528], [673, 560], [0, 556]], [[909, 526], [1684, 528], [1684, 565], [909, 564]], [[26, 556], [754, 558], [754, 590], [26, 588]], [[911, 554], [971, 559], [968, 597], [908, 591]], [[0, 585], [754, 588], [754, 626], [0, 622]], [[953, 588], [1684, 592], [1684, 629], [952, 626]], [[0, 618], [444, 624], [444, 656], [0, 650]], [[909, 622], [1435, 626], [1435, 658], [909, 654]], [[21, 650], [753, 654], [752, 686], [21, 682]], [[0, 681], [753, 684], [752, 716], [0, 712]], [[1181, 698], [1228, 698], [1228, 728], [1181, 728]], [[0, 711], [753, 712], [752, 746], [0, 744]], [[1361, 714], [1428, 714], [1428, 748], [1361, 748]], [[20, 769], [746, 776], [745, 814], [19, 806]], [[1442, 780], [1466, 780], [1466, 815], [1442, 815]], [[0, 805], [753, 810], [752, 842], [0, 836]], [[0, 837], [747, 838], [747, 870], [0, 868]], [[0, 867], [751, 868], [751, 902], [0, 900]], [[1248, 864], [1316, 870], [1311, 921], [1244, 915]], [[0, 897], [749, 900], [749, 932], [0, 929]], [[0, 927], [749, 932], [749, 966], [0, 961]], [[1437, 938], [1468, 938], [1468, 984], [1437, 984]], [[0, 957], [423, 961], [423, 994], [0, 991]], [[28, 989], [746, 996], [745, 1028], [28, 1021]], [[0, 1019], [746, 1026], [745, 1058], [0, 1051]], [[898, 1022], [1672, 1033], [1671, 1071], [898, 1060]], [[0, 1047], [746, 1055], [745, 1092], [0, 1085]], [[919, 1058], [1682, 1065], [1682, 1099], [919, 1092]], [[0, 1079], [423, 1081], [423, 1115], [0, 1113]], [[898, 1090], [1312, 1092], [1312, 1124], [898, 1122]], [[938, 1122], [1082, 1122], [1082, 1154], [938, 1154]], [[365, 1168], [379, 1168], [379, 1186], [365, 1186]], [[1265, 1173], [1281, 1173], [1281, 1186], [1265, 1186]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫薇斗数初级讲义", "是一个重要星系，往往可以预示生病动手术，或者涉及官司诉讼，倘若不熟[安星法]", "甲", "安星法", "的话，在推算流年或流月时，见到天梁之后，满盘去找天刑或擎羊，和流运擎羊、", "(一)定出生年\\月\\日\\时", "流年擎羊、以至流月擎羊，那就往往会使精神分散，因而影响推断的精微与准确。", "(三)对命盘的基本认识", "由于熟悉[安星]是研究紫微斗数的基础，同时也是认识各星系组合必须具备", "(三)安星简表", "的知识，所以前人的斗数著作，都特别注意[安星法]的传授。", "(四)安星口诀\\图表及掌诀", "北派斗数名家张开卷先生在撰写[紫微斗数命理研究]一书时，用了差不多-", "一", "（一）定出生年\\月、日、时", "半的篇幅来谈[安星]；名家陆斌兆先生撰[紫微斗数讲义]，上册亦全部讨论[安星", "推算紫微斗数，首先要知道正确的出生年、月、曰、时，然后才可以排出正确", "法]，下册才论及十二宫的性质，由此可见[安星]传授的重要。", "的命盘。由于历史原因，紫微斗数是以古代中州洛阳地区时间作为基准。洛阳正确", "但目前由于流行用查表的方法起星盘，学者便往往贪图简便，因而忽视了学习", "时间是以相对地球公转的经线而言，不是各国自定之标准时间，因此洛阳时间与上", "[安星法]的重要，以致在实际推断时茫然不知所措，无法将星系加以灵活组合。", "海时间，在现行标准时间中没有分别，但在推算紫微斗数时，便有三十五分钟的时", "据笔者所知，张开卷先生是第一位将各星曜分年干、年支、月、日、时各系", "差。一般来说，因为我国历法一天分十二个时辰，一个时辰相等于现行历法的两个", "加以表列的人，其目的当然是为了便利后学。然而由此引起的副作用，却非张先生", "小时，除了少数特别例子外，大部分华东、华中时区出生的人都可用标准时辰来算。", "当时意料所及。", "但有很多情形下，读者推算不准命盘，却是因为出生时间在时头或时尾，加上与洛", "照中州派的观点，靠查表米安星，除了上述无法熟悉星系组合的缺点外，还有", "阳有时差，因而也就导致时辰错误。中州派有一套用来认明命盘中人形相貌的方法，", "个弊病，那就是容易因[甲级星]、［乙级星]·…·以至[戊级星]的分类，影响推断", "以确定命盘的正确与否。此法留待后章详述。", "的准确性。因为有时整个星系，往往会因为会合了一颗所谓[戊级星]而改变了星系", "根据现代历法，如果出生地点在中国以外，或只知道西历生辰等等，可以以下", "的性质，而学者常则易固星曜级数太低引起错觉，对这[戊级星]不够重视。", "述方法来加以换算－－－首先，如果读者已知道自己的阴历出生资料。可把其分列", "陆斌兆先生传授[安星]，不分级数，即属于中州派的传统，与张开卷先生截然", "如下：", "同。希望学者能打破将星曜分级的观念，潜心学习[安星法，勿因民间表列流行", "例如某女士出生于一九三八年农历十一月初六日上午八时，出生地为香港，因", "认为安星的举例多余。这是一个很重要的概念.", "其时没有夏令时间引引起的时差，所以可将资料填写如下：", "笔者所据的[安星法]，跟民间各本固有差异，即与陆先生介绍的[安星法]亦微", "不同，希学者加以注意。尤其是在[年干四化]方面，戊、庚，壬三干的[四化]都", "1985", "各派不同，此外[解神]分为[年解]与[月解]等，均属本派的传授，学者可以比", "1938年", "如前所述，术派不分甲级乙级星，起星时亦不按年系、月系等次序，看似紊乱，", "阳", "优点在于按照次序起星时，可以一面安星一面依次序观察，星盘起妥，已有大", "的印象。例如安[紫微系]及[天府系]十四颗正曜后，立即安[辅弼、昌曲、空劫]", "曜，然后立即安[四化曜]，整个星盘的架构便已成形。以后一路安星，便只是基", "皇皇", "性质的加强或削弱，如安魁、钺之后，立刻起禄存及四煞(即火星、铃星、擎羊及", "它罗)，然后起天官、天福，便可以大致对其人的[官]及[禄]有大致印象。所以，熟", "量", "本派安星的次序，对认识星盘应有相当帮助。", "为了方便初学的读者，本篇于安星诀之前，仍附检查间表以便应用，但仍希望", "者能熟读安星举例。熟悉之后，固可以随时随地起盘推算，同时在飞星推算", "注：女阴历岁数以西历岁数加一便可。食干支历可定男女属阳属阴，详见第二章。", "流年、流月时，不易出现错误。可以说，检表起星只是为方便初学，但要进步到意", "如果只知道八字干支、西历生年、月、日、时，或出生地点太远及只知道岁数及", "盘会、灵机发动的境界，则非熟安星诀不可。", "其所属生肖等等，可用下篇之换算方法计算。", "1、出生年换算", "S", "6"], "rec_scores": [0.9977474808692932, 0.9938743710517883, 0.99940025806427, 0.9993283152580261, 0.9981725215911865, 0.9171017408370972, 0.9966849088668823, 0.9753925800323486, 0.9976887702941895, 0.947601854801178, 0.9980481266975403, 0.9432655572891235, 0.9937617182731628, 0.5559177994728088, 0.9797524213790894, 0.9982976317405701, 0.9963451623916626, 0.9974599480628967, 0.9996509552001953, 0.9993840456008911, 0.9995468854904175, 0.9962144494056702, 0.9995415806770325, 0.9974168539047241, 0.999626874923706, 0.9996306896209717, 0.9992619156837463, 0.9999287724494934, 0.9996006488800049, 0.9989931583404541, 0.9918930530548096, 0.9214470982551575, 0.9994493722915649, 0.9814829230308533, 0.9998101592063904, 0.9808295965194702, 0.978675127029419, 0.9975718855857849, 0.9799356460571289, 0.9934555888175964, 0.9988436102867126, 0.9956935048103333, 0.9942852854728699, 0.998188316822052, 0.9962543845176697, 0.9930558204650879, 0.9809444546699524, 0.9901441335678101, 0.9946212768554688, 0.6762140989303589, 0.9979602694511414, 0.9951735138893127, 0.984019935131073, 0.5715335011482239, 0.9732091426849365, 0.9784052968025208, 0.19533708691596985, 0.999426543712616, 0.9990629553794861, 0.998831570148468, 0.9990532398223877, 0.9921823143959045, 0.9991940259933472, 0.999090313911438, 0.999821662902832, 0.9998524785041809, 0.552419900894165, 0.9977676868438721], "rec_polys": [[[1186, 0], [1414, 0], [1414, 35], [1186, 35]], [[0, 23], [758, 32], [758, 69], [0, 60]], [[1088, 39], [1128, 39], [1128, 71], [1088, 71]], [[1109, 35], [1258, 35], [1258, 74], [1109, 74]], [[0, 55], [749, 66], [749, 103], [0, 92]], [[939, 71], [1193, 73], [1193, 106], [938, 104]], [[0, 87], [746, 99], [745, 138], [0, 126]], [[932, 101], [1130, 105], [1129, 138], [931, 134]], [[46, 124], [760, 133], [759, 167], [45, 158]], [[933, 135], [1044, 135], [1044, 168], [933, 168]], [[0, 156], [560, 161], [559, 195], [0, 190]], [[933, 168], [1168, 168], [1168, 200], [933, 200]], [[44, 186], [744, 195], [744, 229], [44, 220]], [[733, 206], [753, 206], [753, 218], [733, 218]], [[956, 197], [1228, 199], [1228, 230], [956, 229]], [[0, 216], [760, 225], [759, 261], [0, 252]], [[953, 225], [1677, 229], [1677, 262], [952, 259]], [[0, 248], [591, 255], [591, 287], [0, 280]], [[911, 253], [1679, 259], [1679, 293], [910, 287]], [[44, 280], [758, 284], [758, 317], [44, 314]], [[911, 282], [1681, 287], [1680, 321], [910, 315]], [[0, 310], [723, 314], [723, 347], [0, 344]], [[911, 314], [1681, 317], [1680, 351], [910, 347]], [[39, 340], [760, 342], [760, 376], [39, 374]], [[911, 344], [1682, 347], [1682, 381], [910, 377]], [[0, 370], [760, 374], [759, 406], [0, 402]], [[912, 372], [1681, 379], [1680, 413], [912, 406]], [[0, 402], [140, 402], [140, 434], [0, 434]], [[912, 404], [1684, 409], [1684, 443], [912, 438]], [[30, 429], [758, 433], [758, 472], [30, 468]], [[911, 438], [1682, 441], [1682, 475], [910, 471]], [[11, 463], [756, 466], [756, 500], [10, 496]], [[912, 468], [1323, 470], [1323, 502], [912, 500]], [[0, 494], [756, 496], [756, 530], [0, 528]], [[954, 498], [1684, 500], [1684, 533], [954, 532]], [[0, 525], [674, 528], [673, 560], [0, 556]], [[909, 526], [1684, 528], [1684, 565], [909, 564]], [[26, 556], [754, 558], [754, 590], [26, 588]], [[911, 554], [971, 559], [968, 597], [908, 591]], [[0, 585], [754, 588], [754, 626], [0, 622]], [[953, 588], [1684, 592], [1684, 629], [952, 626]], [[0, 618], [444, 624], [444, 656], [0, 650]], [[909, 622], [1435, 626], [1435, 658], [909, 654]], [[21, 650], [753, 654], [752, 686], [21, 682]], [[0, 681], [753, 684], [752, 716], [0, 712]], [[1181, 698], [1228, 698], [1228, 728], [1181, 728]], [[0, 711], [753, 712], [752, 746], [0, 744]], [[1361, 714], [1428, 714], [1428, 748], [1361, 748]], [[20, 769], [746, 776], [745, 814], [19, 806]], [[1442, 780], [1466, 780], [1466, 815], [1442, 815]], [[0, 805], [753, 810], [752, 842], [0, 836]], [[0, 837], [747, 838], [747, 870], [0, 868]], [[0, 867], [751, 868], [751, 902], [0, 900]], [[1248, 864], [1316, 870], [1311, 921], [1244, 915]], [[0, 897], [749, 900], [749, 932], [0, 929]], [[0, 927], [749, 932], [749, 966], [0, 961]], [[1437, 938], [1468, 938], [1468, 984], [1437, 984]], [[0, 957], [423, 961], [423, 994], [0, 991]], [[28, 989], [746, 996], [745, 1028], [28, 1021]], [[0, 1019], [746, 1026], [745, 1058], [0, 1051]], [[898, 1022], [1672, 1033], [1671, 1071], [898, 1060]], [[0, 1047], [746, 1055], [745, 1092], [0, 1085]], [[919, 1058], [1682, 1065], [1682, 1099], [919, 1092]], [[0, 1079], [423, 1081], [423, 1115], [0, 1113]], [[898, 1090], [1312, 1092], [1312, 1124], [898, 1122]], [[938, 1122], [1082, 1122], [1082, 1154], [938, 1154]], [[365, 1168], [379, 1168], [379, 1186], [365, 1186]], [[1265, 1173], [1281, 1173], [1281, 1186], [1265, 1186]]], "rec_boxes": [[1186, 0, 1414, 35], [0, 23, 758, 69], [1088, 39, 1128, 71], [1109, 35, 1258, 74], [0, 55, 749, 103], [938, 71, 1193, 106], [0, 87, 746, 138], [931, 101, 1130, 138], [45, 124, 760, 167], [933, 135, 1044, 168], [0, 156, 560, 195], [933, 168, 1168, 200], [44, 186, 744, 229], [733, 206, 753, 218], [956, 197, 1228, 230], [0, 216, 760, 261], [952, 225, 1677, 262], [0, 248, 591, 287], [910, 253, 1679, 293], [44, 280, 758, 317], [910, 282, 1681, 321], [0, 310, 723, 347], [910, 314, 1681, 351], [39, 340, 760, 376], [910, 344, 1682, 381], [0, 370, 760, 406], [912, 372, 1681, 413], [0, 402, 140, 434], [912, 404, 1684, 443], [30, 429, 758, 472], [910, 438, 1682, 475], [10, 463, 756, 500], [912, 468, 1323, 502], [0, 494, 756, 530], [954, 498, 1684, 533], [0, 525, 674, 560], [909, 526, 1684, 565], [26, 556, 754, 590], [908, 554, 971, 597], [0, 585, 754, 626], [952, 588, 1684, 629], [0, 618, 444, 656], [909, 622, 1435, 658], [21, 650, 753, 686], [0, 681, 753, 716], [1181, 698, 1228, 728], [0, 711, 753, 746], [1361, 714, 1428, 748], [19, 769, 746, 814], [1442, 780, 1466, 815], [0, 805, 753, 842], [0, 837, 747, 870], [0, 867, 751, 902], [1244, 864, 1316, 921], [0, 897, 749, 932], [0, 927, 749, 966], [1437, 938, 1468, 984], [0, 957, 423, 994], [28, 989, 746, 1028], [0, 1019, 746, 1058], [898, 1022, 1672, 1071], [0, 1047, 746, 1092], [919, 1058, 1682, 1099], [0, 1079, 423, 1115], [898, 1090, 1312, 1124], [938, 1122, 1082, 1154], [365, 1168, 379, 1186], [1265, 1173, 1281, 1186]]}