{"input_path": "data/中州派紫微斗数初级讲义_5.pdf", "page_index": 0, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[325, 39], [526, 39], [526, 60], [325, 60]], [[1158, 39], [1356, 39], [1356, 60], [1158, 60]], [[81, 67], [772, 67], [772, 94], [81, 94]], [[912, 67], [1603, 67], [1603, 94], [912, 94]], [[82, 96], [396, 96], [396, 122], [82, 122]], [[912, 96], [1214, 96], [1214, 122], [912, 122]], [[121, 124], [335, 124], [335, 151], [121, 151]], [[89, 154], [765, 154], [765, 181], [89, 181]], [[82, 183], [719, 183], [719, 209], [82, 209]], [[1005, 202], [1044, 202], [1044, 227], [1005, 227]], [[1074, 200], [1112, 200], [1112, 225], [1074, 225]], [[1138, 198], [1179, 198], [1179, 223], [1138, 223]], [[1205, 197], [1245, 197], [1245, 222], [1205, 222]], [[1272, 197], [1312, 197], [1312, 220], [1272, 220]], [[1340, 195], [1379, 195], [1379, 220], [1340, 220]], [[1414, 195], [1437, 195], [1437, 216], [1414, 216]], [[1481, 195], [1503, 195], [1503, 216], [1481, 216]], [[119, 211], [381, 211], [381, 237], [119, 237]], [[118, 241], [770, 241], [770, 268], [118, 268]], [[1005, 248], [1047, 248], [1047, 278], [1005, 278]], [[1082, 248], [1107, 248], [1107, 273], [1082, 273]], [[1145, 245], [1174, 245], [1174, 271], [1145, 271]], [[1216, 246], [1240, 246], [1240, 271], [1216, 271]], [[1282, 245], [1307, 245], [1307, 269], [1282, 269]], [[1347, 241], [1374, 241], [1374, 269], [1347, 269]], [[1405, 239], [1447, 239], [1447, 269], [1405, 269]], [[1474, 241], [1512, 241], [1512, 266], [1474, 266]], [[82, 269], [474, 269], [474, 296], [82, 296]], [[119, 298], [770, 298], [770, 324], [119, 324]], [[949, 298], [1061, 298], [1061, 326], [949, 326]], [[84, 331], [765, 331], [765, 353], [84, 353]], [[951, 328], [1602, 328], [1602, 354], [951, 354]], [[82, 358], [510, 358], [510, 385], [82, 385]], [[912, 358], [1361, 358], [1361, 385], [912, 385]], [[118, 386], [212, 386], [212, 415], [118, 415]], [[953, 386], [1591, 386], [1591, 413], [953, 413]], [[118, 413], [221, 413], [221, 447], [118, 447]], [[916, 418], [1600, 418], [1600, 440], [916, 440]], [[914, 445], [1602, 445], [1602, 471], [914, 471]], [[267, 477], [330, 477], [330, 505], [267, 505]], [[354, 477], [417, 477], [417, 505], [354, 505]], [[442, 479], [505, 479], [505, 507], [442, 507]], [[530, 480], [593, 480], [593, 509], [530, 509]], [[614, 476], [683, 481], [681, 515], [611, 510]], [[914, 473], [1217, 473], [1217, 500], [914, 500]], [[179, 528], [242, 528], [242, 557], [179, 557]], [[284, 525], [316, 531], [311, 558], [279, 551]], [[372, 532], [400, 532], [400, 558], [372, 558]], [[458, 532], [488, 532], [488, 557], [458, 557]], [[546, 533], [575, 533], [575, 558], [546, 558]], [[633, 533], [663, 533], [663, 560], [633, 560]], [[1002, 544], [1040, 544], [1040, 569], [1002, 569]], [[1075, 541], [1117, 541], [1117, 571], [1075, 571]], [[1152, 542], [1191, 542], [1191, 567], [1152, 567]], [[1228, 541], [1267, 541], [1267, 565], [1228, 565]], [[1303, 539], [1342, 539], [1342, 564], [1303, 564]], [[1379, 537], [1417, 537], [1417, 562], [1379, 562]], [[1454, 537], [1491, 537], [1491, 560], [1454, 560]], [[82, 590], [768, 590], [768, 617], [82, 617]], [[79, 617], [344, 617], [344, 649], [79, 649]], [[1079, 629], [1174, 629], [1174, 654], [1079, 654]], [[119, 649], [756, 649], [756, 675], [119, 675]], [[81, 677], [284, 677], [284, 704], [81, 704]], [[116, 707], [230, 707], [230, 734], [116, 734]], [[118, 736], [756, 736], [756, 762], [118, 762]], [[1242, 727], [1263, 727], [1263, 750], [1242, 750]], [[1388, 727], [1409, 727], [1409, 748], [1388, 748]], [[79, 762], [772, 762], [772, 794], [79, 794]], [[1219, 757], [1244, 757], [1244, 782], [1219, 782]], [[1245, 767], [1275, 767], [1275, 821], [1245, 821]], [[1275, 760], [1293, 760], [1293, 778], [1275, 778]], [[81, 794], [114, 794], [114, 821], [81, 821]], [[1168, 799], [1193, 799], [1193, 824], [1168, 824]], [[1247, 812], [1275, 812], [1275, 888], [1247, 888]], [[1393, 805], [1407, 805], [1407, 817], [1393, 817]], [[1154, 828], [1168, 828], [1168, 842], [1154, 842]], [[1352, 828], [1366, 828], [1366, 842], [1352, 842]], [[203, 860], [225, 860], [225, 883], [203, 883]], [[298, 858], [323, 858], [323, 883], [298, 883]], [[363, 853], [498, 851], [498, 879], [363, 881]], [[529, 851], [666, 847], [667, 873], [530, 878]], [[1167, 868], [1196, 868], [1196, 895], [1167, 895]], [[1389, 870], [1410, 870], [1410, 893], [1389, 893]], [[207, 902], [233, 902], [233, 930], [207, 930]], [[253, 904], [274, 904], [274, 927], [253, 927]], [[293, 900], [319, 900], [319, 929], [293, 929]], [[363, 899], [500, 897], [500, 925], [363, 927]], [[533, 895], [668, 893], [669, 922], [534, 924]], [[209, 948], [233, 948], [233, 973], [209, 973]], [[254, 948], [274, 948], [274, 969], [254, 969]], [[296, 946], [321, 946], [321, 971], [296, 971]], [[365, 943], [501, 941], [502, 969], [365, 971]], [[535, 939], [668, 939], [668, 966], [535, 966]], [[1167, 943], [1195, 943], [1195, 969], [1167, 969]], [[1244, 945], [1267, 945], [1267, 966], [1244, 966]], [[1317, 945], [1342, 945], [1342, 969], [1317, 969]], [[1388, 943], [1416, 943], [1416, 971], [1388, 971]], [[207, 992], [232, 992], [232, 1017], [207, 1017]], [[302, 991], [326, 991], [326, 1016], [302, 1016]], [[365, 987], [501, 985], [502, 1014], [365, 1016]], [[535, 985], [670, 985], [670, 1012], [535, 1012]], [[118, 1056], [230, 1056], [230, 1083], [118, 1083]], [[119, 1085], [756, 1085], [756, 1111], [119, 1111]], [[414, 1129], [437, 1129], [437, 1150], [414, 1150]], [[1245, 1131], [1270, 1131], [1270, 1150], [1245, 1150]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫薇斗数初级讲义", "中州派紫薇斗数初级讲义", "月，闰三月一日至十五日亥时，照三月来推算，闰三月十六日子时至闰三月底，作", "只有[午]与[未]二个地支化合之后不属五行，以[午]为太阳，[未]为太阴。这样的", "四月来推算，出生日及时则保持不变。", "组合，即称为[地支六合]。 (见图1)", "(二）对命盘之基本认识", "本篇专为一些中国术数基础不足之初学者而设，内容涉及的干支五行，会局合局，", "非加以熟悉不可，否则既无法熟悉[安星]，甚至将来在研究推算时亦有麻烦。", "地支", "子丑", "寅亥", "卯戌", "辰西", "巳申", "午", "未", "1、十天干、十二地支及其阴阳", "天干地支乃术数之基本符号，如英文的基本字母，天干及地支两大系统互不相", "化合", "土", "木", "火", "金", "水", "太阳", "太阴", "同，但亦有一定的相关。每系统又有阴阳之别。", "天干有十、而地支则数十二。通常干支并提，也就是说，一个天干通常有一个", "5、地支六冲", "关连的地支。干数十而支数十二，因此一个循环的干支数便为六十了。(L.C.M.10,", "十二地支有六合，也有六冲，紫微斗数中之六冲和[子平]不同。子平术中，凡", "12=60)，中国术数中的六十甲子也就是因此而产生。", "对宫必相冲，如子午、丑未之类，六冲有破坏的意义。", "2、十天干", "斗数推查命盘的吉凶，要查看三方四正，所谓三方就是地支三会局， (后详)。", "十天干分别", "而四正便是本宮和对宫。当星曜于[三方四正]会合之时，都称之为[冲]，不限于本", "宮和对宫的相冲而已。斗数中无论吉星或凶星，于推算流运时，不冲不动，即吉星", "甲、乙", "丙、丁", "戊、己", "庚、辛", "王、癸", "不成其吉，凶星亦不成其凶。 (见图)", "五行属", "木", "火", "土", "金", "水", "六冲", "子午", "丑未", "寅申", "卯西", "辰戌", "巳亥", "甲、丙、戌、庚、壬属阳。属该天千年出生之人属阳命。故此男命为阳男、女命为", "阳女，如甲子年、庚辰年等等。", "图一六合", "乙、丁、已、辛、癸属阴。该年出生之人属阴命。男命为阴男，女命为阴女，", "如丁巳年、乙未年等等。", "3、十二地支", "地支共十二个，分属金、木、水、火、土五行，只有土占四，即辰、戌、丑、", "午", "申", "未称为四墓库，而其中辰称为天罗，戌称地网，地支又以十二生肖代表，其分别如", "一月", "水金", "日", "下：", "辰", "火木土", "西", ".", ".", "地", "支", "子丑寅卯辰巳", "阳阴阳阴阳阴", "品", "成", "生", "肖", "属", "鼠牛虎免龙蛇", "火土金金土水", "五", "行", "属", "水土木木土火", "马羊猴鸡犬猪", "寅", "出", "子", "刻", "阴", "阳", "阳阴阳阴阳阴", "午未申酉戌亥", "4、地支六合", "十二地支分成六组，每组有两个不同五行的地支，合而为一化气属另一五行，", "11", "12"], "rec_scores": [0.9997792840003967, 0.9996078610420227, 0.9989892244338989, 0.9974428415298462, 0.999939501285553, 0.9917853474617004, 0.9775240421295166, 0.9927475452423096, 0.9988851547241211, 0.9991062879562378, 0.9594281911849976, 0.9974490404129028, 0.7988977432250977, 0.9732654094696045, 0.6700412034988403, 0.995600700378418, 0.9995660185813904, 0.9995983839035034, 0.9993910193443298, 0.9983651638031006, 0.9998685121536255, 0.9936350584030151, 0.9991859793663025, 0.9999680519104004, 0.9998550415039062, 0.9999707937240601, 0.9985836744308472, 0.9998595118522644, 0.9993565678596497, 0.999938428401947, 0.9957239031791687, 0.999496340751648, 0.9997398853302002, 0.9991128444671631, 0.9984285235404968, 0.9889615178108215, 0.9895815849304199, 0.9928688406944275, 0.991642415523529, 0.9903883337974548, 0.9828951358795166, 0.9538841247558594, 0.9972474575042725, 0.865420401096344, 0.9698269367218018, 0.9995614886283875, 0.9924898743629456, 0.9994797110557556, 0.998852014541626, 0.9998026490211487, 0.9999644756317139, 0.9982950687408447, 0.9984503388404846, 0.899387776851654, 0.987479567527771, 0.855085015296936, 0.8716962933540344, 0.7458432912826538, 0.9990977048873901, 0.9999299049377441, 0.9965059161186218, 0.9968469142913818, 0.9881221652030945, 0.9996702075004578, 0.9987073540687561, 0.9978246688842773, 0.7785481810569763, 0.9994251728057861, 0.7617332935333252, 0.9912717342376709, 0.9976477026939392, 0.8847979307174683, 0.9246953725814819, 0.9840996861457825, 0.9930043816566467, 0.6586049199104309, 0.6953833699226379, 0.9996438026428223, 0.9997724890708923, 0.9431520104408264, 0.9990412592887878, 0.35451722145080566, 0.8817465901374817, 0.9998990297317505, 0.9939048886299133, 0.9990083575248718, 0.9517802596092224, 0.9983255863189697, 0.9999047517776489, 0.9999423027038574, 0.9999072551727295, 0.9958522319793701, 0.9976577162742615, 0.9991710186004639, 0.3028741180896759, 0.9755107164382935, 0.22389043867588043, 0.9983717799186707, 0.9997749924659729, 0.9986372590065002, 0.8697235584259033, 0.999837338924408, 0.9992955327033997, 0.9972418546676636, 0.9995235800743103], "rec_polys": [[[325, 39], [526, 39], [526, 60], [325, 60]], [[1158, 39], [1356, 39], [1356, 60], [1158, 60]], [[81, 67], [772, 67], [772, 94], [81, 94]], [[912, 67], [1603, 67], [1603, 94], [912, 94]], [[82, 96], [396, 96], [396, 122], [82, 122]], [[912, 96], [1214, 96], [1214, 122], [912, 122]], [[121, 124], [335, 124], [335, 151], [121, 151]], [[89, 154], [765, 154], [765, 181], [89, 181]], [[82, 183], [719, 183], [719, 209], [82, 209]], [[1005, 202], [1044, 202], [1044, 227], [1005, 227]], [[1074, 200], [1112, 200], [1112, 225], [1074, 225]], [[1138, 198], [1179, 198], [1179, 223], [1138, 223]], [[1205, 197], [1245, 197], [1245, 222], [1205, 222]], [[1272, 197], [1312, 197], [1312, 220], [1272, 220]], [[1340, 195], [1379, 195], [1379, 220], [1340, 220]], [[1414, 195], [1437, 195], [1437, 216], [1414, 216]], [[1481, 195], [1503, 195], [1503, 216], [1481, 216]], [[119, 211], [381, 211], [381, 237], [119, 237]], [[118, 241], [770, 241], [770, 268], [118, 268]], [[1005, 248], [1047, 248], [1047, 278], [1005, 278]], [[1082, 248], [1107, 248], [1107, 273], [1082, 273]], [[1145, 245], [1174, 245], [1174, 271], [1145, 271]], [[1216, 246], [1240, 246], [1240, 271], [1216, 271]], [[1282, 245], [1307, 245], [1307, 269], [1282, 269]], [[1347, 241], [1374, 241], [1374, 269], [1347, 269]], [[1405, 239], [1447, 239], [1447, 269], [1405, 269]], [[1474, 241], [1512, 241], [1512, 266], [1474, 266]], [[82, 269], [474, 269], [474, 296], [82, 296]], [[119, 298], [770, 298], [770, 324], [119, 324]], [[949, 298], [1061, 298], [1061, 326], [949, 326]], [[84, 331], [765, 331], [765, 353], [84, 353]], [[951, 328], [1602, 328], [1602, 354], [951, 354]], [[82, 358], [510, 358], [510, 385], [82, 385]], [[912, 358], [1361, 358], [1361, 385], [912, 385]], [[118, 386], [212, 386], [212, 415], [118, 415]], [[953, 386], [1591, 386], [1591, 413], [953, 413]], [[118, 413], [221, 413], [221, 447], [118, 447]], [[916, 418], [1600, 418], [1600, 440], [916, 440]], [[914, 445], [1602, 445], [1602, 471], [914, 471]], [[267, 477], [330, 477], [330, 505], [267, 505]], [[354, 477], [417, 477], [417, 505], [354, 505]], [[442, 479], [505, 479], [505, 507], [442, 507]], [[530, 480], [593, 480], [593, 509], [530, 509]], [[614, 476], [683, 481], [681, 515], [611, 510]], [[914, 473], [1217, 473], [1217, 500], [914, 500]], [[179, 528], [242, 528], [242, 557], [179, 557]], [[284, 525], [316, 531], [311, 558], [279, 551]], [[372, 532], [400, 532], [400, 558], [372, 558]], [[458, 532], [488, 532], [488, 557], [458, 557]], [[546, 533], [575, 533], [575, 558], [546, 558]], [[633, 533], [663, 533], [663, 560], [633, 560]], [[1002, 544], [1040, 544], [1040, 569], [1002, 569]], [[1075, 541], [1117, 541], [1117, 571], [1075, 571]], [[1152, 542], [1191, 542], [1191, 567], [1152, 567]], [[1228, 541], [1267, 541], [1267, 565], [1228, 565]], [[1303, 539], [1342, 539], [1342, 564], [1303, 564]], [[1379, 537], [1417, 537], [1417, 562], [1379, 562]], [[1454, 537], [1491, 537], [1491, 560], [1454, 560]], [[82, 590], [768, 590], [768, 617], [82, 617]], [[79, 617], [344, 617], [344, 649], [79, 649]], [[1079, 629], [1174, 629], [1174, 654], [1079, 654]], [[119, 649], [756, 649], [756, 675], [119, 675]], [[81, 677], [284, 677], [284, 704], [81, 704]], [[116, 707], [230, 707], [230, 734], [116, 734]], [[118, 736], [756, 736], [756, 762], [118, 762]], [[1242, 727], [1263, 727], [1263, 750], [1242, 750]], [[1388, 727], [1409, 727], [1409, 748], [1388, 748]], [[79, 762], [772, 762], [772, 794], [79, 794]], [[1219, 757], [1244, 757], [1244, 782], [1219, 782]], [[1245, 767], [1275, 767], [1275, 821], [1245, 821]], [[1275, 760], [1293, 760], [1293, 778], [1275, 778]], [[81, 794], [114, 794], [114, 821], [81, 821]], [[1168, 799], [1193, 799], [1193, 824], [1168, 824]], [[1247, 812], [1275, 812], [1275, 888], [1247, 888]], [[1393, 805], [1407, 805], [1407, 817], [1393, 817]], [[1154, 828], [1168, 828], [1168, 842], [1154, 842]], [[1352, 828], [1366, 828], [1366, 842], [1352, 842]], [[203, 860], [225, 860], [225, 883], [203, 883]], [[298, 858], [323, 858], [323, 883], [298, 883]], [[363, 853], [498, 851], [498, 879], [363, 881]], [[529, 851], [666, 847], [667, 873], [530, 878]], [[1167, 868], [1196, 868], [1196, 895], [1167, 895]], [[1389, 870], [1410, 870], [1410, 893], [1389, 893]], [[207, 902], [233, 902], [233, 930], [207, 930]], [[253, 904], [274, 904], [274, 927], [253, 927]], [[293, 900], [319, 900], [319, 929], [293, 929]], [[363, 899], [500, 897], [500, 925], [363, 927]], [[533, 895], [668, 893], [669, 922], [534, 924]], [[209, 948], [233, 948], [233, 973], [209, 973]], [[254, 948], [274, 948], [274, 969], [254, 969]], [[296, 946], [321, 946], [321, 971], [296, 971]], [[365, 943], [501, 941], [502, 969], [365, 971]], [[535, 939], [668, 939], [668, 966], [535, 966]], [[1167, 943], [1195, 943], [1195, 969], [1167, 969]], [[1244, 945], [1267, 945], [1267, 966], [1244, 966]], [[1317, 945], [1342, 945], [1342, 969], [1317, 969]], [[1388, 943], [1416, 943], [1416, 971], [1388, 971]], [[207, 992], [232, 992], [232, 1017], [207, 1017]], [[302, 991], [326, 991], [326, 1016], [302, 1016]], [[365, 987], [501, 985], [502, 1014], [365, 1016]], [[535, 985], [670, 985], [670, 1012], [535, 1012]], [[118, 1056], [230, 1056], [230, 1083], [118, 1083]], [[119, 1085], [756, 1085], [756, 1111], [119, 1111]], [[414, 1129], [437, 1129], [437, 1150], [414, 1150]], [[1245, 1131], [1270, 1131], [1270, 1150], [1245, 1150]]], "rec_boxes": [[325, 39, 526, 60], [1158, 39, 1356, 60], [81, 67, 772, 94], [912, 67, 1603, 94], [82, 96, 396, 122], [912, 96, 1214, 122], [121, 124, 335, 151], [89, 154, 765, 181], [82, 183, 719, 209], [1005, 202, 1044, 227], [1074, 200, 1112, 225], [1138, 198, 1179, 223], [1205, 197, 1245, 222], [1272, 197, 1312, 220], [1340, 195, 1379, 220], [1414, 195, 1437, 216], [1481, 195, 1503, 216], [119, 211, 381, 237], [118, 241, 770, 268], [1005, 248, 1047, 278], [1082, 248, 1107, 273], [1145, 245, 1174, 271], [1216, 246, 1240, 271], [1282, 245, 1307, 269], [1347, 241, 1374, 269], [1405, 239, 1447, 269], [1474, 241, 1512, 266], [82, 269, 474, 296], [119, 298, 770, 324], [949, 298, 1061, 326], [84, 331, 765, 353], [951, 328, 1602, 354], [82, 358, 510, 385], [912, 358, 1361, 385], [118, 386, 212, 415], [953, 386, 1591, 413], [118, 413, 221, 447], [916, 418, 1600, 440], [914, 445, 1602, 471], [267, 477, 330, 505], [354, 477, 417, 505], [442, 479, 505, 507], [530, 480, 593, 509], [611, 476, 683, 515], [914, 473, 1217, 500], [179, 528, 242, 557], [279, 525, 316, 558], [372, 532, 400, 558], [458, 532, 488, 557], [546, 533, 575, 558], [633, 533, 663, 560], [1002, 544, 1040, 569], [1075, 541, 1117, 571], [1152, 542, 1191, 567], [1228, 541, 1267, 565], [1303, 539, 1342, 564], [1379, 537, 1417, 562], [1454, 537, 1491, 560], [82, 590, 768, 617], [79, 617, 344, 649], [1079, 629, 1174, 654], [119, 649, 756, 675], [81, 677, 284, 704], [116, 707, 230, 734], [118, 736, 756, 762], [1242, 727, 1263, 750], [1388, 727, 1409, 748], [79, 762, 772, 794], [1219, 757, 1244, 782], [1245, 767, 1275, 821], [1275, 760, 1293, 778], [81, 794, 114, 821], [1168, 799, 1193, 824], [1247, 812, 1275, 888], [1393, 805, 1407, 817], [1154, 828, 1168, 842], [1352, 828, 1366, 842], [203, 860, 225, 883], [298, 858, 323, 883], [363, 851, 498, 881], [529, 847, 667, 878], [1167, 868, 1196, 895], [1389, 870, 1410, 893], [207, 902, 233, 930], [253, 904, 274, 927], [293, 900, 319, 929], [363, 897, 500, 927], [533, 893, 669, 924], [209, 948, 233, 973], [254, 948, 274, 969], [296, 946, 321, 971], [365, 941, 502, 971], [535, 939, 668, 966], [1167, 943, 1195, 969], [1244, 945, 1267, 966], [1317, 945, 1342, 969], [1388, 943, 1416, 971], [207, 992, 232, 1017], [302, 991, 326, 1016], [365, 985, 502, 1016], [535, 985, 670, 1012], [118, 1056, 230, 1083], [119, 1085, 756, 1111], [414, 1129, 437, 1150], [1245, 1131, 1270, 1150]]}