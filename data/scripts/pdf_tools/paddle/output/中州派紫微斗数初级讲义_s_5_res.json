{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 5, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[1196, 2], [1296, 2], [1296, 18], [1196, 18]], [[0, 16], [770, 25], [770, 62], [0, 53]], [[921, 27], [1670, 28], [1670, 66], [921, 64]], [[2, 48], [360, 53], [359, 89], [2, 83]], [[918, 58], [1253, 60], [1252, 99], [917, 97]], [[50, 78], [293, 84], [292, 121], [49, 115]], [[13, 110], [762, 121], [761, 160], [12, 149]], [[4, 140], [709, 153], [708, 192], [3, 179]], [[51, 173], [346, 181], [345, 218], [51, 211]], [[1017, 179], [1060, 179], [1060, 209], [1017, 209]], [[1093, 175], [1133, 175], [1133, 207], [1093, 207]], [[1165, 175], [1205, 175], [1205, 207], [1165, 207]], [[1237, 175], [1281, 175], [1281, 207], [1237, 207]], [[1310, 175], [1351, 175], [1351, 207], [1310, 207]], [[1384, 175], [1424, 175], [1424, 206], [1384, 206]], [[1465, 179], [1486, 179], [1486, 202], [1465, 202]], [[1538, 179], [1558, 179], [1558, 200], [1538, 200]], [[55, 207], [765, 213], [765, 252], [54, 246]], [[1016, 229], [1060, 229], [1060, 261], [1016, 261]], [[1098, 229], [1126, 229], [1126, 257], [1098, 257]], [[1172, 229], [1198, 229], [1198, 257], [1172, 257]], [[1247, 230], [1272, 230], [1272, 255], [1247, 255]], [[1319, 230], [1344, 230], [1344, 255], [1319, 255]], [[1393, 230], [1417, 230], [1417, 255], [1393, 255]], [[1454, 225], [1496, 225], [1496, 255], [1454, 255]], [[1528, 225], [1568, 225], [1568, 255], [1528, 255]], [[9, 239], [442, 245], [442, 282], [9, 276]], [[53, 268], [765, 269], [765, 314], [53, 312]], [[951, 276], [1081, 276], [1081, 315], [951, 315]], [[7, 299], [761, 303], [761, 347], [7, 344]], [[953, 306], [1674, 314], [1673, 351], [952, 344]], [[4, 331], [479, 335], [479, 374], [3, 370]], [[912, 338], [1410, 342], [1410, 379], [912, 376]], [[40, 367], [153, 367], [153, 401], [40, 401]], [[956, 369], [1658, 372], [1658, 409], [956, 406]], [[34, 389], [160, 394], [159, 435], [33, 430]], [[916, 400], [1672, 406], [1672, 440], [916, 434]], [[914, 431], [1677, 434], [1677, 472], [914, 468]], [[195, 461], [277, 461], [277, 507], [195, 507]], [[295, 460], [371, 465], [368, 508], [292, 502]], [[389, 464], [472, 464], [472, 510], [389, 510]], [[489, 466], [565, 466], [565, 507], [489, 507]], [[586, 468], [660, 468], [660, 509], [586, 509]], [[916, 464], [1254, 466], [1254, 500], [916, 498]], [[100, 521], [170, 521], [170, 557], [100, 557]], [[216, 526], [247, 526], [247, 558], [216, 558]], [[312, 525], [346, 525], [346, 562], [312, 562]], [[410, 526], [444, 526], [444, 562], [410, 562]], [[507, 526], [540, 526], [540, 562], [507, 562]], [[603, 528], [639, 528], [639, 562], [603, 562]], [[1012, 539], [1060, 539], [1060, 571], [1012, 571]], [[1098, 537], [1140, 537], [1140, 571], [1098, 571]], [[1179, 535], [1226, 535], [1226, 572], [1179, 572]], [[1265, 537], [1307, 537], [1307, 569], [1265, 569]], [[1347, 533], [1395, 533], [1395, 571], [1347, 571]], [[1430, 530], [1477, 530], [1477, 567], [1430, 567]], [[1516, 532], [1558, 532], [1558, 564], [1516, 564]], [[0, 579], [761, 583], [761, 626], [0, 622]], [[0, 613], [277, 619], [277, 656], [0, 650]], [[1093, 622], [1210, 622], [1210, 668], [1093, 668]], [[26, 643], [746, 645], [745, 689], [26, 688]], [[0, 679], [219, 679], [219, 716], [0, 716]], [[28, 711], [160, 711], [160, 744], [28, 744]], [[30, 737], [744, 746], [743, 784], [30, 774]], [[1293, 748], [1307, 748], [1307, 760], [1293, 760]], [[0, 767], [756, 776], [756, 815], [0, 806]], [[1267, 782], [1281, 782], [1281, 796], [1267, 796]], [[1323, 780], [1344, 780], [1344, 801], [1323, 801]], [[0, 803], [33, 803], [33, 838], [0, 838]], [[1298, 796], [1319, 796], [1319, 817], [1298, 817]], [[1203, 824], [1224, 824], [1224, 844], [1203, 844]], [[1296, 817], [1317, 817], [1317, 840], [1296, 840]], [[1307, 837], [1328, 858], [1309, 877], [1288, 856]], [[125, 868], [158, 868], [158, 906], [125, 906]], [[223, 865], [261, 865], [261, 909], [223, 909]], [[298, 865], [454, 865], [454, 909], [298, 909]], [[484, 869], [638, 865], [639, 904], [485, 908]], [[1296, 870], [1317, 870], [1317, 890], [1296, 890]], [[1209, 906], [1223, 906], [1223, 918], [1209, 918]], [[195, 916], [256, 916], [256, 959], [195, 959]], [[300, 916], [454, 916], [454, 955], [300, 955]], [[1458, 907], [1472, 907], [1472, 922], [1458, 922]], [[132, 922], [163, 922], [163, 954], [132, 954]], [[168, 922], [207, 922], [207, 954], [168, 954]], [[484, 918], [640, 918], [640, 957], [484, 957]], [[165, 930], [177, 930], [177, 945], [165, 945]], [[132, 968], [160, 968], [160, 998], [132, 998]], [[180, 960], [212, 977], [199, 1003], [167, 987]], [[198, 966], [256, 966], [256, 1001], [198, 1001]], [[301, 962], [455, 966], [454, 1002], [300, 998]], [[165, 975], [177, 975], [177, 989], [165, 989]], [[489, 968], [639, 968], [639, 1001], [489, 1001]], [[1205, 989], [1223, 989], [1223, 1003], [1205, 1003]], [[1288, 984], [1309, 984], [1309, 1005], [1288, 1005]], [[1375, 989], [1391, 989], [1391, 1003], [1375, 1003]], [[1458, 989], [1475, 989], [1475, 1007], [1458, 1007]], [[125, 1012], [158, 1012], [158, 1047], [125, 1047]], [[228, 1012], [260, 1012], [260, 1049], [228, 1049]], [[305, 1012], [456, 1012], [456, 1051], [305, 1051]], [[491, 1014], [642, 1014], [642, 1053], [491, 1053]], [[26, 1078], [154, 1078], [154, 1111], [26, 1111]], [[23, 1100], [734, 1115], [733, 1158], [22, 1143]], [[358, 1157], [384, 1157], [384, 1186], [358, 1186]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫微", "月，闰三月一日至十五日亥时，照三月来推算，闰三月十六日子时至闰三月底，作", "只有[午]与[未]二个地支化合之后不属五行，以[午]为太阳，[未]为太阴。这样的", "四月来推算，出生日及时则保持不变。", "组合，即称为[地支六合]。(见图1)", "（二）对命盘之基本认识", "本篇专为一些中国术数基础不足之初学者而设，内容涉及的干支五行，会局合局，", "非加以熟悉不可，否则既无法熟悉[安星]，甚至将来在研究推算时亦有麻烦。", "1、十天干、十二地支及其阴阳", "地支", "子丑", "寅亥", "卯成", "辰西", "已申", "午", "未", "天干地支乃术数之基本符号，如英文的基本字母，天干及地支两大系统互不相", "化合", "土", "￥", "火", "要", "X", "太阳", "太阴", "同，但亦有一定的相关。每系统又有阴阳之别。", "天干有十、而地支则数十二。通常干支并提，也就是说，一个天干通常有一个", "5、地支六冲", "关连的地支。干数十而支数十二，因此一个循环的干支数便为六十了。（L.C.M.10，", "", "12=60)，中国术数中的六十甲子也就是因此而产生。", "对宫必相冲，如子午、丑未之类，六冲有破坏的意义。", "2、十天干", "斗数推查命盘的吉凶，要查看三方四正，所谓三方就是地支三会局，(后详)。", "十天干分别", "而四正便是本宫和对宫。当星曜于[三方四正]会合之时，都称之为[冲]，不限于本", "宫和对宫的相冲而已。斗数中无论吉星或凶星，于推算流运时，不冲不动，即吉星", "2由", "丙、丁", "2长", "庚、辛", "王、癸", "不成其吉，凶星亦不成其凶。 (见图)", "五行属", "木", "￥", "土", "金", "X", "六冲", "子午", "丑未", "寅申", "显d", "辰戌", "已孩", "甲、丙、戌、庚、壬属阳。属该天千年出生之人属阳命。故此男命为阳男、女命为", "阳女，如甲子年、庚辰年等等。", "图一六合", "乙、丁、已、辛、癸属阴。该年出生之人属阴命。男命为阴男，女命为阴女，", "如丁已年、乙未年等等。", "3、十二地支", "地支共十二个，分属金、木、水、火、土五行，只有土占四，即辰、戌、丑、", "午", "未称为四墓库，而其中辰称为天罗，戊称地网，地支又以十二生肖代表，其分别如", "月", "日", "下：", "￥", "辰", "金", "", "地", "支", "子丑寅卯辰巳", "阳阴阳阴阳 阴 ", "?", "", "", "鼠牛虎免龙蛇", "戎", "生", "肖", "火土金金土水", "", "五", "行", "属", "水土木木土火", "", "马羊猴鸡犬猪", "单", "安", "子", "贝", "阴", "阳", "阳阴阳阴阳阴", "午未申西戌亥", "4、地支六合", "十二地支分成六组，每组有两个不同五行的地支，合而为一化气属另一五行，", "11"], "rec_scores": [0.9803541898727417, 0.9734700322151184, 0.9948453903198242, 0.9979738593101501, 0.9842090606689453, 0.9589335918426514, 0.9935568571090698, 0.9951350688934326, 0.9941297769546509, 0.9998345375061035, 0.9575011730194092, 0.9922143220901489, 0.6475374698638916, 0.8568792343139648, 0.9377305507659912, 0.9871760010719299, 0.9997000694274902, 0.9964528679847717, 0.9960492849349976, 0.9213063716888428, 0.6421805620193481, 0.9911292791366577, 0.5595245957374573, 0.4694536030292511, 0.999924898147583, 0.9964245557785034, 0.9993279576301575, 0.9909177422523499, 0.9997310042381287, 0.9833443760871887, 0.0, 0.9952008724212646, 0.9992656111717224, 0.9970871806144714, 0.9944896101951599, 0.9717806577682495, 0.9968705177307129, 0.9851510524749756, 0.7893299460411072, 0.8755442500114441, 0.4014509320259094, 0.9929472804069519, 0.8323290348052979, 0.9790216088294983, 0.9998978972434998, 0.9748169779777527, 0.3023940920829773, 0.6684101819992065, 0.9999300241470337, 0.5481638312339783, 0.9985398054122925, 0.9510819315910339, 0.7225779891014099, 0.9986929893493652, 0.7700647115707397, 0.7919065952301025, 0.7437535524368286, 0.994206964969635, 0.999736487865448, 0.900546669960022, 0.9845414161682129, 0.9829528331756592, 0.9994644522666931, 0.9906341433525085, 0.3096467852592468, 0.9912382960319519, 0.9998342990875244, 0.9961804151535034, 0.8198950886726379, 0.6892254948616028, 0.999055802822113, 0.999933123588562, 0.0, 0.9999858140945435, 0.9997424483299255, 0.833555281162262, 0.885521650314331, 0.4523794949054718, 0.0, 0.0, 0.9964568614959717, 0.7172015309333801, 0.9997000694274902, 0.9597296118736267, 0.9459083080291748, 0.0, 0.999936580657959, 0.9999707937240601, 0.9993523955345154, 0.9603214263916016, 0.0, 0.9302794933319092, 0.976768970489502, 0.21032026410102844, 0.989371657371521, 0.6236329674720764, 0.9921534657478333, 0.9997230172157288, 0.9958356022834778, 0.8311877250671387, 0.9998031258583069, 0.998248279094696, 0.9964209794998169], "rec_polys": [[[1196, 2], [1296, 2], [1296, 18], [1196, 18]], [[0, 16], [770, 25], [770, 62], [0, 53]], [[921, 27], [1670, 28], [1670, 66], [921, 64]], [[2, 48], [360, 53], [359, 89], [2, 83]], [[918, 58], [1253, 60], [1252, 99], [917, 97]], [[50, 78], [293, 84], [292, 121], [49, 115]], [[13, 110], [762, 121], [761, 160], [12, 149]], [[4, 140], [709, 153], [708, 192], [3, 179]], [[51, 173], [346, 181], [345, 218], [51, 211]], [[1017, 179], [1060, 179], [1060, 209], [1017, 209]], [[1093, 175], [1133, 175], [1133, 207], [1093, 207]], [[1165, 175], [1205, 175], [1205, 207], [1165, 207]], [[1237, 175], [1281, 175], [1281, 207], [1237, 207]], [[1310, 175], [1351, 175], [1351, 207], [1310, 207]], [[1384, 175], [1424, 175], [1424, 206], [1384, 206]], [[1465, 179], [1486, 179], [1486, 202], [1465, 202]], [[1538, 179], [1558, 179], [1558, 200], [1538, 200]], [[55, 207], [765, 213], [765, 252], [54, 246]], [[1016, 229], [1060, 229], [1060, 261], [1016, 261]], [[1098, 229], [1126, 229], [1126, 257], [1098, 257]], [[1172, 229], [1198, 229], [1198, 257], [1172, 257]], [[1247, 230], [1272, 230], [1272, 255], [1247, 255]], [[1319, 230], [1344, 230], [1344, 255], [1319, 255]], [[1393, 230], [1417, 230], [1417, 255], [1393, 255]], [[1454, 225], [1496, 225], [1496, 255], [1454, 255]], [[1528, 225], [1568, 225], [1568, 255], [1528, 255]], [[9, 239], [442, 245], [442, 282], [9, 276]], [[53, 268], [765, 269], [765, 314], [53, 312]], [[951, 276], [1081, 276], [1081, 315], [951, 315]], [[7, 299], [761, 303], [761, 347], [7, 344]], [[953, 306], [1674, 314], [1673, 351], [952, 344]], [[4, 331], [479, 335], [479, 374], [3, 370]], [[912, 338], [1410, 342], [1410, 379], [912, 376]], [[40, 367], [153, 367], [153, 401], [40, 401]], [[956, 369], [1658, 372], [1658, 409], [956, 406]], [[34, 389], [160, 394], [159, 435], [33, 430]], [[916, 400], [1672, 406], [1672, 440], [916, 434]], [[914, 431], [1677, 434], [1677, 472], [914, 468]], [[195, 461], [277, 461], [277, 507], [195, 507]], [[295, 460], [371, 465], [368, 508], [292, 502]], [[389, 464], [472, 464], [472, 510], [389, 510]], [[489, 466], [565, 466], [565, 507], [489, 507]], [[586, 468], [660, 468], [660, 509], [586, 509]], [[916, 464], [1254, 466], [1254, 500], [916, 498]], [[100, 521], [170, 521], [170, 557], [100, 557]], [[216, 526], [247, 526], [247, 558], [216, 558]], [[312, 525], [346, 525], [346, 562], [312, 562]], [[410, 526], [444, 526], [444, 562], [410, 562]], [[507, 526], [540, 526], [540, 562], [507, 562]], [[603, 528], [639, 528], [639, 562], [603, 562]], [[1012, 539], [1060, 539], [1060, 571], [1012, 571]], [[1098, 537], [1140, 537], [1140, 571], [1098, 571]], [[1179, 535], [1226, 535], [1226, 572], [1179, 572]], [[1265, 537], [1307, 537], [1307, 569], [1265, 569]], [[1347, 533], [1395, 533], [1395, 571], [1347, 571]], [[1430, 530], [1477, 530], [1477, 567], [1430, 567]], [[1516, 532], [1558, 532], [1558, 564], [1516, 564]], [[0, 579], [761, 583], [761, 626], [0, 622]], [[0, 613], [277, 619], [277, 656], [0, 650]], [[1093, 622], [1210, 622], [1210, 668], [1093, 668]], [[26, 643], [746, 645], [745, 689], [26, 688]], [[0, 679], [219, 679], [219, 716], [0, 716]], [[28, 711], [160, 711], [160, 744], [28, 744]], [[30, 737], [744, 746], [743, 784], [30, 774]], [[1293, 748], [1307, 748], [1307, 760], [1293, 760]], [[0, 767], [756, 776], [756, 815], [0, 806]], [[1267, 782], [1281, 782], [1281, 796], [1267, 796]], [[1323, 780], [1344, 780], [1344, 801], [1323, 801]], [[0, 803], [33, 803], [33, 838], [0, 838]], [[1298, 796], [1319, 796], [1319, 817], [1298, 817]], [[1203, 824], [1224, 824], [1224, 844], [1203, 844]], [[1296, 817], [1317, 817], [1317, 840], [1296, 840]], [[1307, 837], [1328, 858], [1309, 877], [1288, 856]], [[125, 868], [158, 868], [158, 906], [125, 906]], [[223, 865], [261, 865], [261, 909], [223, 909]], [[298, 865], [454, 865], [454, 909], [298, 909]], [[484, 869], [638, 865], [639, 904], [485, 908]], [[1296, 870], [1317, 870], [1317, 890], [1296, 890]], [[1209, 906], [1223, 906], [1223, 918], [1209, 918]], [[195, 916], [256, 916], [256, 959], [195, 959]], [[300, 916], [454, 916], [454, 955], [300, 955]], [[1458, 907], [1472, 907], [1472, 922], [1458, 922]], [[132, 922], [163, 922], [163, 954], [132, 954]], [[168, 922], [207, 922], [207, 954], [168, 954]], [[484, 918], [640, 918], [640, 957], [484, 957]], [[165, 930], [177, 930], [177, 945], [165, 945]], [[132, 968], [160, 968], [160, 998], [132, 998]], [[180, 960], [212, 977], [199, 1003], [167, 987]], [[198, 966], [256, 966], [256, 1001], [198, 1001]], [[301, 962], [455, 966], [454, 1002], [300, 998]], [[165, 975], [177, 975], [177, 989], [165, 989]], [[489, 968], [639, 968], [639, 1001], [489, 1001]], [[1205, 989], [1223, 989], [1223, 1003], [1205, 1003]], [[1288, 984], [1309, 984], [1309, 1005], [1288, 1005]], [[1375, 989], [1391, 989], [1391, 1003], [1375, 1003]], [[1458, 989], [1475, 989], [1475, 1007], [1458, 1007]], [[125, 1012], [158, 1012], [158, 1047], [125, 1047]], [[228, 1012], [260, 1012], [260, 1049], [228, 1049]], [[305, 1012], [456, 1012], [456, 1051], [305, 1051]], [[491, 1014], [642, 1014], [642, 1053], [491, 1053]], [[26, 1078], [154, 1078], [154, 1111], [26, 1111]], [[23, 1100], [734, 1115], [733, 1158], [22, 1143]], [[358, 1157], [384, 1157], [384, 1186], [358, 1186]]], "rec_boxes": [[1196, 2, 1296, 18], [0, 16, 770, 62], [921, 27, 1670, 66], [2, 48, 360, 89], [917, 58, 1253, 99], [49, 78, 293, 121], [12, 110, 762, 160], [3, 140, 709, 192], [51, 173, 346, 218], [1017, 179, 1060, 209], [1093, 175, 1133, 207], [1165, 175, 1205, 207], [1237, 175, 1281, 207], [1310, 175, 1351, 207], [1384, 175, 1424, 206], [1465, 179, 1486, 202], [1538, 179, 1558, 200], [54, 207, 765, 252], [1016, 229, 1060, 261], [1098, 229, 1126, 257], [1172, 229, 1198, 257], [1247, 230, 1272, 255], [1319, 230, 1344, 255], [1393, 230, 1417, 255], [1454, 225, 1496, 255], [1528, 225, 1568, 255], [9, 239, 442, 282], [53, 268, 765, 314], [951, 276, 1081, 315], [7, 299, 761, 347], [952, 306, 1674, 351], [3, 331, 479, 374], [912, 338, 1410, 379], [40, 367, 153, 401], [956, 369, 1658, 409], [33, 389, 160, 435], [916, 400, 1672, 440], [914, 431, 1677, 472], [195, 461, 277, 507], [292, 460, 371, 508], [389, 464, 472, 510], [489, 466, 565, 507], [586, 468, 660, 509], [916, 464, 1254, 500], [100, 521, 170, 557], [216, 526, 247, 558], [312, 525, 346, 562], [410, 526, 444, 562], [507, 526, 540, 562], [603, 528, 639, 562], [1012, 539, 1060, 571], [1098, 537, 1140, 571], [1179, 535, 1226, 572], [1265, 537, 1307, 569], [1347, 533, 1395, 571], [1430, 530, 1477, 567], [1516, 532, 1558, 564], [0, 579, 761, 626], [0, 613, 277, 656], [1093, 622, 1210, 668], [26, 643, 746, 689], [0, 679, 219, 716], [28, 711, 160, 744], [30, 737, 744, 784], [1293, 748, 1307, 760], [0, 767, 756, 815], [1267, 782, 1281, 796], [1323, 780, 1344, 801], [0, 803, 33, 838], [1298, 796, 1319, 817], [1203, 824, 1224, 844], [1296, 817, 1317, 840], [1288, 837, 1328, 877], [125, 868, 158, 906], [223, 865, 261, 909], [298, 865, 454, 909], [484, 865, 639, 908], [1296, 870, 1317, 890], [1209, 906, 1223, 918], [195, 916, 256, 959], [300, 916, 454, 955], [1458, 907, 1472, 922], [132, 922, 163, 954], [168, 922, 207, 954], [484, 918, 640, 957], [165, 930, 177, 945], [132, 968, 160, 998], [167, 960, 212, 1003], [198, 966, 256, 1001], [300, 962, 455, 1002], [165, 975, 177, 989], [489, 968, 639, 1001], [1205, 989, 1223, 1003], [1288, 984, 1309, 1005], [1375, 989, 1391, 1003], [1458, 989, 1475, 1007], [125, 1012, 158, 1047], [228, 1012, 260, 1049], [305, 1012, 456, 1051], [491, 1014, 642, 1053], [26, 1078, 154, 1111], [22, 1100, 734, 1158], [358, 1157, 384, 1186]]}