{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 3, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[54, 25], [756, 25], [756, 57], [54, 57]], [[5, 55], [767, 57], [767, 90], [5, 89]], [[972, 62], [1533, 62], [1533, 99], [972, 99]], [[5, 87], [423, 87], [423, 124], [5, 124]], [[47, 119], [261, 119], [261, 156], [47, 156]], [[1042, 135], [1121, 135], [1121, 179], [1042, 179]], [[1202, 133], [1281, 133], [1281, 181], [1202, 181]], [[1344, 136], [1461, 136], [1461, 175], [1344, 175]], [[47, 149], [765, 152], [765, 191], [47, 188]], [[2, 181], [763, 184], [763, 223], [2, 220]], [[970, 184], [1130, 184], [1130, 229], [970, 229]], [[1175, 186], [1247, 186], [1247, 229], [1175, 229]], [[1289, 179], [1452, 179], [1452, 234], [1289, 234]], [[2, 213], [591, 216], [591, 255], [2, 252]], [[632, 225], [651, 225], [651, 248], [632, 248]], [[0, 245], [763, 246], [763, 289], [0, 287]], [[968, 241], [1072, 241], [1072, 280], [968, 280]], [[1168, 237], [1274, 237], [1274, 280], [1168, 280]], [[1288, 236], [1451, 236], [1451, 285], [1288, 285]], [[46, 280], [220, 284], [219, 318], [45, 313]], [[968, 294], [1138, 294], [1138, 333], [968, 333]], [[1170, 294], [1270, 294], [1270, 335], [1170, 335]], [[1285, 288], [1448, 293], [1446, 337], [1284, 333]], [[42, 308], [763, 310], [763, 353], [42, 351]], [[0, 338], [502, 342], [501, 386], [0, 383]], [[968, 351], [1137, 351], [1137, 385], [968, 385]], [[1170, 346], [1270, 346], [1270, 386], [1170, 386]], [[1282, 347], [1442, 347], [1442, 386], [1282, 386]], [[39, 374], [540, 378], [540, 417], [38, 413]], [[969, 397], [1137, 401], [1136, 435], [968, 430]], [[1168, 399], [1242, 399], [1242, 440], [1168, 440]], [[167, 425], [586, 424], [586, 457], [167, 459]], [[1272, 439], [1441, 448], [1438, 503], [1269, 494]], [[203, 457], [589, 455], [589, 487], [204, 489]], [[972, 450], [1124, 450], [1124, 489], [972, 489]], [[1163, 450], [1238, 450], [1238, 491], [1163, 491]], [[163, 489], [349, 489], [349, 521], [163, 521]], [[969, 501], [1134, 505], [1133, 544], [968, 540]], [[1275, 497], [1376, 502], [1374, 543], [1273, 538]], [[232, 516], [293, 516], [293, 551], [232, 551]], [[353, 516], [407, 516], [407, 553], [353, 553]], [[474, 518], [524, 518], [524, 548], [474, 548]], [[393, 542], [481, 542], [481, 571], [393, 571]], [[360, 558], [398, 558], [398, 590], [360, 590]], [[477, 551], [521, 551], [521, 610], [477, 610]], [[967, 555], [1135, 555], [1135, 594], [967, 594]], [[1163, 555], [1259, 555], [1259, 594], [1163, 594]], [[1274, 555], [1430, 555], [1430, 594], [1274, 594]], [[239, 581], [271, 576], [277, 619], [245, 624]], [[370, 583], [389, 583], [389, 613], [370, 613]], [[481, 594], [519, 594], [519, 633], [481, 633]], [[258, 606], [273, 621], [260, 634], [245, 619]], [[367, 604], [391, 604], [391, 645], [367, 645]], [[965, 608], [1133, 608], [1133, 647], [965, 647]], [[1271, 604], [1374, 608], [1372, 649], [1269, 645]], [[247, 624], [270, 624], [270, 659], [247, 659]], [[481, 622], [516, 622], [516, 658], [481, 658]], [[251, 656], [267, 656], [267, 668], [251, 668]], [[965, 661], [1063, 661], [1063, 702], [965, 702]], [[1163, 663], [1258, 663], [1258, 697], [1163, 697]], [[1274, 658], [1431, 658], [1431, 702], [1274, 702]], [[251, 670], [267, 670], [267, 682], [251, 682]], [[249, 689], [258, 681], [265, 688], [256, 697]], [[960, 714], [1063, 714], [1063, 753], [960, 753]], [[1275, 711], [1375, 711], [1375, 751], [1275, 751]], [[960, 767], [1047, 767], [1047, 806], [960, 806]], [[1275, 766], [1375, 766], [1375, 805], [1275, 805]], [[898, 860], [1668, 852], [1668, 890], [898, 897]], [[898, 890], [1666, 886], [1667, 923], [898, 927]], [[900, 922], [1338, 922], [1338, 954], [900, 954]], [[402, 971], [474, 973], [473, 991], [402, 989]], [[33, 1019], [747, 1019], [747, 1056], [33, 1056]], [[0, 1053], [425, 1053], [425, 1085], [0, 1085]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["推算斗数，必须知道生年干支，这可以在万年历中找到；知道出生年干支后，", "如甲、丙、戊、庚、壬年出生人属阳，男命为阳男，女命为阳女。乙、丁、已、辛、", "台湾应用[日光节约时间]及[夏令时间]历年起迄日期", "癸年出生属阴命，男命为阴男，女命为阴女。", "2、出生月、日换算法", "年份", "名称", "起迄日期", "如果出生的资料是西历或干支的月和曰，可翻查一般民间[万年历]。民间[万年", "历]有一些是不正确的，所以最好相对两本以上来定出生年月和曰.或按中国紫金山", "民国三十四年至四十年", "夏令时间", "五朋一日至九月三十日", "天文台编的[1901—2000一百年日历表](科学出版社)二书为准。", "+", "例：某甲出生为西历一九三八年十二月二十七日万年历得知为戊寅年十一月初六日", "民国四十一年", "日光节约时间", "三月一日至十月州一日", "3、出生时换算法.", "民国四十二年至四十三年", "日光节约时间", "四月一日至十月州一日", "此微斗数起例，最重要是决定出生时间，本派起出生时是以洛阳地区作为绝对", "标准，每与洛阳相差十五度经线便有一个小时之时差。", "民国四十四年至四十五年", "日光节约时间", "四月一日至九月三十日", "香港标准时间与洛阳时间相差七分半钟，除特别命外。", "民国四十六年至四十八年", "夏令时间", "例，如时头时尾生人外，一般可以作为标准时间来推算。", "六月一日至月二十日", "香港有一段时间使用夏令时间，比标准时早一小时，", "民国四十九年至五十年", "夏令时间", "其施行时期分列于下表：", "民围五十一年至六十二年", "停止夏令时间", "年9份", "开始", "0结", "全年标准时间", "38/4 ", "", "民国六十三年至六十四年", "日光节约时间", "四月一日至九月三十日", "199", "", "38918", "056", "装", "民国六十五年至六十七年", "停止夏令时间", "", "81", "953", "民国六十八年", "日光节约时间", "七月一日至九月三十日", "56", "9", "民国六十九年", "停止夏令时间", "民国七十年", "停止夏令时间", "注：关于[子时]的分割，尚有争论。有以由下午十一时至午夜一时，统归属为当日", "之子时者。即一交入十一时便作为新一日的开始。但笔者经验，仍以表列的分割为", "宜。即交入十二时(零时)，才是一天新的开始。", "全年标准时间", "例如：一九五八年西历五月十曰下午八时(左右生人)，本来八时应为戌时，因", "有夏令时间，应改为下午七时生人，作酉时算。"], "rec_scores": [0.9906847476959229, 0.9911636114120483, 0.9636200666427612, 0.9996031522750854, 0.9991872906684875, 0.9999192953109741, 0.9994076490402222, 0.9498054385185242, 0.9926497340202332, 0.9810779690742493, 0.9988116025924683, 0.9975870847702026, 0.9665564298629761, 0.9886670708656311, 0.9434612393379211, 0.9987673163414001, 0.9975056648254395, 0.996661365032196, 0.9230138063430786, 0.99702388048172, 0.9992074370384216, 0.9901804327964783, 0.9210012555122375, 0.9968122839927673, 0.999547004699707, 0.9990190863609314, 0.9969269633293152, 0.9756046533584595, 0.9993130564689636, 0.9995561242103577, 0.9856746792793274, 0.9971877932548523, 0.9241470694541931, 0.9954995512962341, 0.9984744787216187, 0.996938943862915, 0.9992650747299194, 0.9644744992256165, 0.9855308532714844, 0.793459415435791, 0.7038763165473938, 0.4788493514060974, 0.9975525736808777, 0.6443111896514893, 0.0, 0.9997113943099976, 0.9908782839775085, 0.9794090390205383, 0.7374846935272217, 0.0, 0.47027158737182617, 0.4052559435367584, 0.16652345657348633, 0.9909631609916687, 0.9314242005348206, 0.0, 0.4261922240257263, 0.8265730738639832, 0.9576916694641113, 0.9965012073516846, 0.9467839002609253, 0.6970727443695068, 0.10914774239063263, 0.9985277056694031, 0.8994491696357727, 0.9083499908447266, 0.9934543967247009, 0.9972761273384094, 0.996734082698822, 0.9991989731788635, 0.9989058375358582, 0.9813232421875, 0.9985388517379761], "rec_polys": [[[54, 25], [756, 25], [756, 57], [54, 57]], [[5, 55], [767, 57], [767, 90], [5, 89]], [[972, 62], [1533, 62], [1533, 99], [972, 99]], [[5, 87], [423, 87], [423, 124], [5, 124]], [[47, 119], [261, 119], [261, 156], [47, 156]], [[1042, 135], [1121, 135], [1121, 179], [1042, 179]], [[1202, 133], [1281, 133], [1281, 181], [1202, 181]], [[1344, 136], [1461, 136], [1461, 175], [1344, 175]], [[47, 149], [765, 152], [765, 191], [47, 188]], [[2, 181], [763, 184], [763, 223], [2, 220]], [[970, 184], [1130, 184], [1130, 229], [970, 229]], [[1175, 186], [1247, 186], [1247, 229], [1175, 229]], [[1289, 179], [1452, 179], [1452, 234], [1289, 234]], [[2, 213], [591, 216], [591, 255], [2, 252]], [[632, 225], [651, 225], [651, 248], [632, 248]], [[0, 245], [763, 246], [763, 289], [0, 287]], [[968, 241], [1072, 241], [1072, 280], [968, 280]], [[1168, 237], [1274, 237], [1274, 280], [1168, 280]], [[1288, 236], [1451, 236], [1451, 285], [1288, 285]], [[46, 280], [220, 284], [219, 318], [45, 313]], [[968, 294], [1138, 294], [1138, 333], [968, 333]], [[1170, 294], [1270, 294], [1270, 335], [1170, 335]], [[1285, 288], [1448, 293], [1446, 337], [1284, 333]], [[42, 308], [763, 310], [763, 353], [42, 351]], [[0, 338], [502, 342], [501, 386], [0, 383]], [[968, 351], [1137, 351], [1137, 385], [968, 385]], [[1170, 346], [1270, 346], [1270, 386], [1170, 386]], [[1282, 347], [1442, 347], [1442, 386], [1282, 386]], [[39, 374], [540, 378], [540, 417], [38, 413]], [[969, 397], [1137, 401], [1136, 435], [968, 430]], [[1168, 399], [1242, 399], [1242, 440], [1168, 440]], [[167, 425], [586, 424], [586, 457], [167, 459]], [[1272, 439], [1441, 448], [1438, 503], [1269, 494]], [[203, 457], [589, 455], [589, 487], [204, 489]], [[972, 450], [1124, 450], [1124, 489], [972, 489]], [[1163, 450], [1238, 450], [1238, 491], [1163, 491]], [[163, 489], [349, 489], [349, 521], [163, 521]], [[969, 501], [1134, 505], [1133, 544], [968, 540]], [[1275, 497], [1376, 502], [1374, 543], [1273, 538]], [[232, 516], [293, 516], [293, 551], [232, 551]], [[353, 516], [407, 516], [407, 553], [353, 553]], [[474, 518], [524, 518], [524, 548], [474, 548]], [[393, 542], [481, 542], [481, 571], [393, 571]], [[360, 558], [398, 558], [398, 590], [360, 590]], [[477, 551], [521, 551], [521, 610], [477, 610]], [[967, 555], [1135, 555], [1135, 594], [967, 594]], [[1163, 555], [1259, 555], [1259, 594], [1163, 594]], [[1274, 555], [1430, 555], [1430, 594], [1274, 594]], [[239, 581], [271, 576], [277, 619], [245, 624]], [[370, 583], [389, 583], [389, 613], [370, 613]], [[481, 594], [519, 594], [519, 633], [481, 633]], [[258, 606], [273, 621], [260, 634], [245, 619]], [[367, 604], [391, 604], [391, 645], [367, 645]], [[965, 608], [1133, 608], [1133, 647], [965, 647]], [[1271, 604], [1374, 608], [1372, 649], [1269, 645]], [[247, 624], [270, 624], [270, 659], [247, 659]], [[481, 622], [516, 622], [516, 658], [481, 658]], [[251, 656], [267, 656], [267, 668], [251, 668]], [[965, 661], [1063, 661], [1063, 702], [965, 702]], [[1163, 663], [1258, 663], [1258, 697], [1163, 697]], [[1274, 658], [1431, 658], [1431, 702], [1274, 702]], [[251, 670], [267, 670], [267, 682], [251, 682]], [[249, 689], [258, 681], [265, 688], [256, 697]], [[960, 714], [1063, 714], [1063, 753], [960, 753]], [[1275, 711], [1375, 711], [1375, 751], [1275, 751]], [[960, 767], [1047, 767], [1047, 806], [960, 806]], [[1275, 766], [1375, 766], [1375, 805], [1275, 805]], [[898, 860], [1668, 852], [1668, 890], [898, 897]], [[898, 890], [1666, 886], [1667, 923], [898, 927]], [[900, 922], [1338, 922], [1338, 954], [900, 954]], [[402, 971], [474, 973], [473, 991], [402, 989]], [[33, 1019], [747, 1019], [747, 1056], [33, 1056]], [[0, 1053], [425, 1053], [425, 1085], [0, 1085]]], "rec_boxes": [[54, 25, 756, 57], [5, 55, 767, 90], [972, 62, 1533, 99], [5, 87, 423, 124], [47, 119, 261, 156], [1042, 135, 1121, 179], [1202, 133, 1281, 181], [1344, 136, 1461, 175], [47, 149, 765, 191], [2, 181, 763, 223], [970, 184, 1130, 229], [1175, 186, 1247, 229], [1289, 179, 1452, 234], [2, 213, 591, 255], [632, 225, 651, 248], [0, 245, 763, 289], [968, 241, 1072, 280], [1168, 237, 1274, 280], [1288, 236, 1451, 285], [45, 280, 220, 318], [968, 294, 1138, 333], [1170, 294, 1270, 335], [1284, 288, 1448, 337], [42, 308, 763, 353], [0, 338, 502, 386], [968, 351, 1137, 385], [1170, 346, 1270, 386], [1282, 347, 1442, 386], [38, 374, 540, 417], [968, 397, 1137, 435], [1168, 399, 1242, 440], [167, 424, 586, 459], [1269, 439, 1441, 503], [203, 455, 589, 489], [972, 450, 1124, 489], [1163, 450, 1238, 491], [163, 489, 349, 521], [968, 501, 1134, 544], [1273, 497, 1376, 543], [232, 516, 293, 551], [353, 516, 407, 553], [474, 518, 524, 548], [393, 542, 481, 571], [360, 558, 398, 590], [477, 551, 521, 610], [967, 555, 1135, 594], [1163, 555, 1259, 594], [1274, 555, 1430, 594], [239, 576, 277, 624], [370, 583, 389, 613], [481, 594, 519, 633], [245, 606, 273, 634], [367, 604, 391, 645], [965, 608, 1133, 647], [1269, 604, 1374, 649], [247, 624, 270, 659], [481, 622, 516, 658], [251, 656, 267, 668], [965, 661, 1063, 702], [1163, 663, 1258, 697], [1274, 658, 1431, 702], [251, 670, 267, 682], [249, 681, 265, 697], [960, 714, 1063, 753], [1275, 711, 1375, 751], [960, 767, 1047, 806], [1275, 766, 1375, 805], [898, 852, 1668, 897], [898, 886, 1667, 927], [900, 922, 1338, 954], [402, 971, 474, 991], [33, 1019, 747, 1056], [0, 1053, 425, 1085]]}