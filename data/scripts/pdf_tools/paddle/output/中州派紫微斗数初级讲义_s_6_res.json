{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 6, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[279, 0], [368, 0], [368, 16], [279, 16]], [[430, 2], [489, 2], [489, 18], [430, 18]], [[1193, 0], [1254, 0], [1254, 16], [1193, 16]], [[1328, 2], [1403, 2], [1403, 18], [1328, 18]], [[170, 28], [277, 28], [277, 67], [170, 67]], [[958, 28], [1570, 30], [1570, 69], [958, 67]], [[958, 65], [1265, 67], [1265, 105], [958, 103]], [[1244, 69], [1567, 71], [1566, 105], [1244, 103]], [[954, 99], [1565, 103], [1565, 140], [954, 136]], [[954, 133], [1565, 138], [1565, 176], [954, 170]], [[530, 145], [544, 145], [544, 158], [530, 158]], [[954, 167], [1563, 170], [1563, 204], [954, 200]], [[253, 197], [274, 197], [274, 216], [253, 216]], [[954, 200], [1559, 200], [1559, 232], [954, 232]], [[274, 222], [298, 222], [298, 243], [274, 243]], [[530, 225], [544, 225], [544, 239], [530, 239]], [[953, 230], [1565, 230], [1565, 262], [953, 262]], [[953, 261], [1566, 261], [1566, 292], [953, 292]], [[951, 289], [1082, 289], [1082, 323], [951, 323]], [[946, 315], [1667, 319], [1666, 362], [945, 358]], [[910, 353], [1026, 353], [1026, 386], [910, 386]], [[275, 379], [298, 379], [298, 399], [275, 399]], [[360, 379], [382, 379], [382, 399], [360, 399]], [[951, 381], [1665, 381], [1665, 418], [951, 418]], [[12, 409], [165, 409], [165, 443], [12, 443]], [[909, 413], [1026, 413], [1026, 447], [909, 447]], [[12, 436], [754, 440], [754, 479], [12, 475]], [[947, 438], [1666, 440], [1666, 484], [947, 482]], [[0, 468], [754, 471], [754, 509], [0, 505]], [[907, 475], [1026, 475], [1026, 509], [907, 509]], [[0, 498], [284, 502], [284, 539], [0, 535]], [[923, 498], [1668, 498], [1668, 546], [923, 546]], [[907, 537], [1005, 537], [1005, 571], [907, 571]], [[491, 558], [516, 558], [516, 583], [491, 583]], [[949, 565], [1666, 565], [1666, 603], [949, 603]], [[467, 579], [512, 573], [515, 596], [470, 602]], [[246, 610], [272, 610], [272, 638], [246, 638]], [[348, 605], [364, 621], [349, 636], [333, 620]], [[426, 608], [451, 608], [451, 627], [426, 627]], [[519, 604], [540, 604], [540, 627], [519, 627]], [[909, 601], [1023, 601], [1023, 629], [909, 629]], [[947, 627], [1666, 627], [1666, 665], [947, 665]], [[192, 658], [222, 655], [227, 703], [197, 707]], [[907, 661], [1023, 661], [1023, 695], [907, 695]], [[340, 677], [374, 677], [374, 753], [340, 753]], [[226, 693], [270, 693], [270, 723], [226, 723]], [[521, 693], [542, 693], [542, 716], [521, 716]], [[945, 689], [1251, 689], [1251, 727], [945, 727]], [[949, 723], [1340, 723], [1340, 755], [949, 755]], [[947, 753], [1340, 753], [1340, 785], [947, 785]], [[254, 787], [268, 787], [268, 801], [254, 801]], [[524, 782], [544, 782], [544, 803], [524, 803]], [[947, 783], [1102, 783], [1102, 815], [947, 815]], [[945, 813], [1244, 813], [1244, 845], [945, 845]], [[986, 840], [1144, 840], [1144, 879], [986, 879]], [[261, 872], [279, 872], [279, 890], [261, 890]], [[349, 870], [372, 870], [372, 890], [349, 890]], [[528, 868], [549, 868], [549, 888], [528, 888]], [[942, 868], [1638, 870], [1638, 909], [942, 907]], [[0, 907], [175, 907], [175, 941], [0, 941]], [[902, 902], [1249, 900], [1249, 937], [902, 939]], [[0, 939], [70, 939], [70, 973], [0, 973]], [[53, 939], [177, 939], [177, 973], [53, 973]], [[947, 936], [1633, 936], [1633, 968], [947, 968]], [[0, 969], [70, 969], [70, 1005], [0, 1005]], [[53, 969], [175, 969], [175, 1003], [53, 1003]], [[903, 966], [1247, 964], [1247, 998], [904, 1000]], [[0, 1004], [69, 999], [71, 1034], [0, 1039]], [[51, 998], [177, 998], [177, 1037], [51, 1037]], [[919, 996], [1047, 996], [1047, 1030], [919, 1030]], [[942, 1024], [1651, 1024], [1651, 1062], [942, 1062]], [[903, 1055], [1647, 1053], [1647, 1086], [903, 1088]], [[24, 1065], [162, 1061], [163, 1095], [25, 1099]], [[28, 1094], [754, 1084], [754, 1122], [28, 1131]], [[905, 1085], [1491, 1081], [1491, 1115], [905, 1118]], [[0, 1126], [276, 1116], [278, 1155], [0, 1165]], [[1277, 1150], [1298, 1172], [1277, 1191], [1256, 1172]], [[382, 1168], [407, 1168], [407, 1191], [382, 1191]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫微", "级讲义", "派时中", "初级讲义", "图二六冲", "甲子乙丑丙寅丁卯戊辰己已庚午辛未壬申癸酉", "甲戌乙亥丙子丁丑戊寅", "寅己卯庚辰辛已壬午癸未", "甲申乙酉丙戌丁亥戌子已丑庚寅 辛卯壬辰癸已", "甲午乙未丙申丁酉戊戌己亥庚子辛丑壬寅癸卯", "电", "甲辰乙已丙午丁未戊申已酉庚戌辛亥壬子癸丑", "。", "甲寅乙卯丙辰丁已戌午已未庚申辛酉壬戌癸亥", "雪", "西", "因甲为天干之首、子为地支之首，故简称六十甲子、或六十花甲子。", "又干支组成六十花甲，每组合又另成五行，其五行之性，称为纳音：", "六十花甲纳音", "甲子乙丑海中金、丙寅丁卵炉中火、戊辰已已大林木。庚午辛末路旁土、壬申", "癸本剑锋金。", "寅", "旺", "甲戌乙亥山头火、丙子丁丑涧下水。戊寅已卯城头土。庚辰辛已白蜡金，壬午", "6、地支三会局", "癸未杨柳木。", "十二地支相隔三宫位置会合，可组成一局，全盘共四局，每局亦即是紫微斗数中", "甲申乙酉泉中水，丙戌丁亥屋上土。戊子已丑霹雳火。庚寅辛卯松柏木，壬辰", "所谓三方四正的[三方]。[三方]组成一局，亦即是[生旺墓]的组合，如水局申为长", "癸已长流水。", "生、子为帝旺、辰为墓库之类。", "甲午乙未沙中金，丙申丁西山下火，戊戌已亥平地木。庚子辛丑壁上土，壬寅癸", "卵金泊金。", "长", "甲辰乙已覆灯火，丙午丁未天河水。戊申己酉大泽土，庚戌辛亥钗金，壬子", "7生", "百", "X", "利", "申", "癸丑桑拓木。", "甲寅乙卯大溪水，丙辰丁已沙中土，戊午已未天上火，庚申辛酉石榴木，壬戌", "慕库", "癸亥大海水。", "水二局", ">", "酉", "算六十纳音，可用一简诀来决定：", "甲乙锦江灯(金水火)丙丁没谷田(水火土)", "戊已营堤柳(火土木）庚辛挂丈钱(土木金)", "16", "戌", "子寅辰午申戌", "壬癸林钟满(木金水)花甲纳音全", "丑卵巳末本亥", "黄", "丑", "亥", "例如算丙辰纳音，依丙丁没谷田诀：[没谷田]的偏旁即水火土。以子丑配水、", "申子辰合水局", "寅卵配火、辰已配土。故丙辰纳音土。", "演午戌", "合火局", "", "亥卯未", "合木局", "申酉配金、戌亥配水。故壬申纳音金。", "酉丑", "1合金局", "8五行长生", "金、木、水、火、土，五行五种元素在十二宫中都有一定生长，壮旺而至哀死", "的过程，有如春天的草木萌芽，夏天开花，秋天结果，冬天衰死而果实埋藏，到春", "7、六十甲子", "十天干以甲为首，十二地支以子为首，一天干配一地支，而排成六十个干支数，", "天后又再发芽，五行的变化，术数中便以下列十二个阶段来形容：", "周而复始，即成为六十甲子。", "", "13"], "rec_scores": [0.929408848285675, 0.9681571125984192, 0.3926726281642914, 0.8489844799041748, 0.999787449836731, 0.9463480710983276, 0.9891055822372437, 0.8754985928535461, 0.9172022938728333, 0.9468280673027039, 0.46385258436203003, 0.9415151476860046, 0.2969161868095398, 0.9430216550827026, 0.47769156098365784, 0.4969167113304138, 0.9985100626945496, 0.9944784641265869, 0.9997565150260925, 0.9737685918807983, 0.998099148273468, 0.9873569011688232, 0.615673303604126, 0.9684725403785706, 0.9995078444480896, 0.9968770146369934, 0.9993931651115417, 0.9632323980331421, 0.9927096366882324, 0.9593210816383362, 0.9987266659736633, 0.9780749678611755, 0.9855071902275085, 0.9950082302093506, 0.9709957242012024, 0.9177501797676086, 0.2530212998390198, 0.25939124822616577, 0.25340840220451355, 0.9875797629356384, 0.9954219460487366, 0.9782127141952515, 0.9012335538864136, 0.9988150000572205, 0.9985787868499756, 0.8494137525558472, 0.7292594909667969, 0.9961004853248596, 0.976671576499939, 0.9559118151664734, 0.7234071493148804, 0.8515301942825317, 0.9912071228027344, 0.994617760181427, 0.8573982119560242, 0.9227323532104492, 0.8450808525085449, 0.6675317287445068, 0.9746058583259583, 0.9962990283966064, 0.965625524520874, 0.842380702495575, 0.9976269602775574, 0.0, 0.7256934642791748, 0.9974629878997803, 0.9780809879302979, 0.9905995726585388, 0.9492000937461853, 0.9921194314956665, 0.9984784126281738, 0.9986560344696045, 0.999492883682251, 0.9915616512298584, 0.9974732995033264, 0.9987618923187256, 0.0, 0.9992183446884155], "rec_polys": [[[279, 0], [368, 0], [368, 16], [279, 16]], [[430, 2], [489, 2], [489, 18], [430, 18]], [[1193, 0], [1254, 0], [1254, 16], [1193, 16]], [[1328, 2], [1403, 2], [1403, 18], [1328, 18]], [[170, 28], [277, 28], [277, 67], [170, 67]], [[958, 28], [1570, 30], [1570, 69], [958, 67]], [[958, 65], [1265, 67], [1265, 105], [958, 103]], [[1244, 69], [1567, 71], [1566, 105], [1244, 103]], [[954, 99], [1565, 103], [1565, 140], [954, 136]], [[954, 133], [1565, 138], [1565, 176], [954, 170]], [[530, 145], [544, 145], [544, 158], [530, 158]], [[954, 167], [1563, 170], [1563, 204], [954, 200]], [[253, 197], [274, 197], [274, 216], [253, 216]], [[954, 200], [1559, 200], [1559, 232], [954, 232]], [[274, 222], [298, 222], [298, 243], [274, 243]], [[530, 225], [544, 225], [544, 239], [530, 239]], [[953, 230], [1565, 230], [1565, 262], [953, 262]], [[953, 261], [1566, 261], [1566, 292], [953, 292]], [[951, 289], [1082, 289], [1082, 323], [951, 323]], [[946, 315], [1667, 319], [1666, 362], [945, 358]], [[910, 353], [1026, 353], [1026, 386], [910, 386]], [[275, 379], [298, 379], [298, 399], [275, 399]], [[360, 379], [382, 379], [382, 399], [360, 399]], [[951, 381], [1665, 381], [1665, 418], [951, 418]], [[12, 409], [165, 409], [165, 443], [12, 443]], [[909, 413], [1026, 413], [1026, 447], [909, 447]], [[12, 436], [754, 440], [754, 479], [12, 475]], [[947, 438], [1666, 440], [1666, 484], [947, 482]], [[0, 468], [754, 471], [754, 509], [0, 505]], [[907, 475], [1026, 475], [1026, 509], [907, 509]], [[0, 498], [284, 502], [284, 539], [0, 535]], [[923, 498], [1668, 498], [1668, 546], [923, 546]], [[907, 537], [1005, 537], [1005, 571], [907, 571]], [[491, 558], [516, 558], [516, 583], [491, 583]], [[949, 565], [1666, 565], [1666, 603], [949, 603]], [[467, 579], [512, 573], [515, 596], [470, 602]], [[246, 610], [272, 610], [272, 638], [246, 638]], [[348, 605], [364, 621], [349, 636], [333, 620]], [[426, 608], [451, 608], [451, 627], [426, 627]], [[519, 604], [540, 604], [540, 627], [519, 627]], [[909, 601], [1023, 601], [1023, 629], [909, 629]], [[947, 627], [1666, 627], [1666, 665], [947, 665]], [[192, 658], [222, 655], [227, 703], [197, 707]], [[907, 661], [1023, 661], [1023, 695], [907, 695]], [[340, 677], [374, 677], [374, 753], [340, 753]], [[226, 693], [270, 693], [270, 723], [226, 723]], [[521, 693], [542, 693], [542, 716], [521, 716]], [[945, 689], [1251, 689], [1251, 727], [945, 727]], [[949, 723], [1340, 723], [1340, 755], [949, 755]], [[947, 753], [1340, 753], [1340, 785], [947, 785]], [[254, 787], [268, 787], [268, 801], [254, 801]], [[524, 782], [544, 782], [544, 803], [524, 803]], [[947, 783], [1102, 783], [1102, 815], [947, 815]], [[945, 813], [1244, 813], [1244, 845], [945, 845]], [[986, 840], [1144, 840], [1144, 879], [986, 879]], [[261, 872], [279, 872], [279, 890], [261, 890]], [[349, 870], [372, 870], [372, 890], [349, 890]], [[528, 868], [549, 868], [549, 888], [528, 888]], [[942, 868], [1638, 870], [1638, 909], [942, 907]], [[0, 907], [175, 907], [175, 941], [0, 941]], [[902, 902], [1249, 900], [1249, 937], [902, 939]], [[0, 939], [70, 939], [70, 973], [0, 973]], [[53, 939], [177, 939], [177, 973], [53, 973]], [[947, 936], [1633, 936], [1633, 968], [947, 968]], [[0, 969], [70, 969], [70, 1005], [0, 1005]], [[53, 969], [175, 969], [175, 1003], [53, 1003]], [[903, 966], [1247, 964], [1247, 998], [904, 1000]], [[0, 1004], [69, 999], [71, 1034], [0, 1039]], [[51, 998], [177, 998], [177, 1037], [51, 1037]], [[919, 996], [1047, 996], [1047, 1030], [919, 1030]], [[942, 1024], [1651, 1024], [1651, 1062], [942, 1062]], [[903, 1055], [1647, 1053], [1647, 1086], [903, 1088]], [[24, 1065], [162, 1061], [163, 1095], [25, 1099]], [[28, 1094], [754, 1084], [754, 1122], [28, 1131]], [[905, 1085], [1491, 1081], [1491, 1115], [905, 1118]], [[0, 1126], [276, 1116], [278, 1155], [0, 1165]], [[1277, 1150], [1298, 1172], [1277, 1191], [1256, 1172]], [[382, 1168], [407, 1168], [407, 1191], [382, 1191]]], "rec_boxes": [[279, 0, 368, 16], [430, 2, 489, 18], [1193, 0, 1254, 16], [1328, 2, 1403, 18], [170, 28, 277, 67], [958, 28, 1570, 69], [958, 65, 1265, 105], [1244, 69, 1567, 105], [954, 99, 1565, 140], [954, 133, 1565, 176], [530, 145, 544, 158], [954, 167, 1563, 204], [253, 197, 274, 216], [954, 200, 1559, 232], [274, 222, 298, 243], [530, 225, 544, 239], [953, 230, 1565, 262], [953, 261, 1566, 292], [951, 289, 1082, 323], [945, 315, 1667, 362], [910, 353, 1026, 386], [275, 379, 298, 399], [360, 379, 382, 399], [951, 381, 1665, 418], [12, 409, 165, 443], [909, 413, 1026, 447], [12, 436, 754, 479], [947, 438, 1666, 484], [0, 468, 754, 509], [907, 475, 1026, 509], [0, 498, 284, 539], [923, 498, 1668, 546], [907, 537, 1005, 571], [491, 558, 516, 583], [949, 565, 1666, 603], [467, 573, 515, 602], [246, 610, 272, 638], [333, 605, 364, 636], [426, 608, 451, 627], [519, 604, 540, 627], [909, 601, 1023, 629], [947, 627, 1666, 665], [192, 655, 227, 707], [907, 661, 1023, 695], [340, 677, 374, 753], [226, 693, 270, 723], [521, 693, 542, 716], [945, 689, 1251, 727], [949, 723, 1340, 755], [947, 753, 1340, 785], [254, 787, 268, 801], [524, 782, 544, 803], [947, 783, 1102, 815], [945, 813, 1244, 845], [986, 840, 1144, 879], [261, 872, 279, 890], [349, 870, 372, 890], [528, 868, 549, 888], [942, 868, 1638, 909], [0, 907, 175, 941], [902, 900, 1249, 939], [0, 939, 70, 973], [53, 939, 177, 973], [947, 936, 1633, 968], [0, 969, 70, 1005], [53, 969, 175, 1003], [903, 964, 1247, 1000], [0, 999, 71, 1039], [51, 998, 177, 1037], [919, 996, 1047, 1030], [942, 1024, 1651, 1062], [903, 1053, 1647, 1088], [24, 1061, 163, 1099], [28, 1084, 754, 1131], [905, 1081, 1491, 1118], [0, 1116, 278, 1165], [1256, 1150, 1298, 1191], [382, 1168, 407, 1191]]}