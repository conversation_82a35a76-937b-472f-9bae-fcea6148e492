{"input_path": "data/中州派紫微斗数初级讲义_3.pdf", "page_index": 0, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": false}, "dt_polys": [[[325, 39], [524, 39], [524, 60], [325, 60]], [[1156, 37], [1358, 37], [1358, 64], [1156, 64]], [[121, 69], [754, 69], [754, 90], [121, 90]], [[82, 96], [767, 96], [767, 122], [82, 122]], [[954, 97], [1452, 99], [1452, 131], [954, 129]], [[82, 124], [454, 124], [454, 151], [82, 151]], [[118, 154], [307, 154], [307, 181], [118, 181]], [[1026, 168], [1086, 168], [1086, 197], [1026, 197]], [[1168, 167], [1230, 167], [1230, 197], [1168, 197]], [[1291, 168], [1389, 168], [1389, 197], [1291, 197]], [[119, 183], [768, 183], [768, 209], [119, 209]], [[82, 211], [768, 211], [768, 237], [82, 237]], [[967, 216], [1098, 216], [1098, 243], [967, 243]], [[1147, 218], [1202, 218], [1202, 241], [1147, 241]], [[1251, 216], [1381, 216], [1381, 243], [1251, 243]], [[81, 241], [614, 241], [614, 268], [81, 268]], [[651, 246], [667, 246], [667, 259], [651, 259]], [[82, 268], [768, 268], [768, 294], [82, 294]], [[968, 264], [1047, 264], [1047, 287], [968, 287]], [[1147, 264], [1226, 264], [1226, 287], [1147, 287]], [[1252, 266], [1379, 266], [1379, 287], [1252, 287]], [[118, 298], [272, 298], [272, 324], [118, 324]], [[967, 310], [1110, 310], [1110, 337], [967, 337]], [[1147, 314], [1224, 314], [1224, 335], [1147, 335]], [[1251, 314], [1379, 314], [1379, 335], [1251, 335]], [[121, 331], [767, 331], [767, 353], [121, 353]], [[82, 358], [528, 358], [528, 385], [82, 385]], [[968, 362], [1109, 362], [1109, 383], [968, 383]], [[1147, 362], [1226, 362], [1226, 383], [1147, 383]], [[1251, 360], [1381, 360], [1381, 386], [1251, 386]], [[119, 390], [565, 390], [565, 411], [119, 411]], [[970, 408], [1109, 408], [1109, 429], [970, 429]], [[1147, 408], [1202, 408], [1202, 431], [1147, 431]], [[1249, 406], [1381, 406], [1381, 432], [1249, 432]], [[239, 431], [612, 431], [612, 452], [239, 452]], [[270, 456], [616, 454], [616, 480], [270, 482]], [[970, 455], [1098, 455], [1098, 477], [970, 477]], [[1145, 454], [1202, 454], [1202, 477], [1145, 477]], [[1252, 455], [1381, 455], [1381, 477], [1252, 477]], [[239, 487], [402, 487], [402, 509], [239, 509]], [[968, 503], [1109, 503], [1109, 525], [968, 525]], [[1252, 503], [1331, 503], [1331, 525], [1252, 525]], [[468, 535], [516, 535], [516, 551], [468, 551]], [[968, 548], [1110, 548], [1110, 574], [968, 574]], [[1147, 549], [1226, 549], [1226, 572], [1147, 572]], [[1249, 548], [1381, 548], [1381, 574], [1249, 574]], [[970, 597], [1109, 597], [1109, 619], [970, 619]], [[1252, 597], [1331, 597], [1331, 620], [1252, 620]], [[968, 645], [1047, 645], [1047, 668], [968, 668]], [[1147, 645], [1228, 645], [1228, 666], [1147, 666]], [[1252, 643], [1381, 643], [1381, 670], [1252, 670]], [[968, 693], [1047, 693], [1047, 716], [968, 716]], [[1252, 693], [1331, 693], [1331, 716], [1252, 716]], [[968, 741], [1037, 741], [1037, 762], [968, 762]], [[1252, 741], [1331, 741], [1331, 764], [1252, 764]], [[914, 822], [1600, 822], [1600, 849], [914, 849]], [[914, 852], [1600, 852], [1600, 879], [914, 879]], [[916, 884], [1302, 884], [1302, 906], [916, 906]], [[458, 923], [521, 923], [521, 939], [458, 939]], [[123, 971], [767, 971], [767, 992], [123, 992]], [[82, 998], [472, 998], [472, 1024], [82, 1024]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫薇斗数初级讲义", "中州派紫薇斗数初级讲义", "推算斗数，必须知道生年干支，这可以在万年历中找到；知道出生年干支后，", "如甲、丙、戊、庚、壬年出生人属阳，男命为阳男，女命为阳女。乙、丁、巳、辛、", "台湾应用[日光节约时间]及[夏令时间]历年起迄日期", "癸年出生属阴命，男命为阴男，女命为阴女。", "2、出生月、日换算法", "年份", "名称", "起迄日期", "如果出生的资料是西历或干支的月和曰，可翻查一般民间[万年历]。民间[万年", "历]有一些是不正确的，所以最好相对两本以上来定出生年月和曰.或按中国紫金山", "民国三十四年至四十年", "夏令时间", "五朋一日至九月三十日", "天文台编的[1901—2000一百年日历表](科学出版社)二书为准。", "+", "例：某甲出生为西历一九三八年十二月二十七日万年历得知为戊寅年十一月初六日", "民国四十一年", "日光节约时间", "三月一日至十月册一日", "3、出生时换算法.", "民国四十二年至四十三年", "日光节约时间", "四月一日至十月册一日", "此微斗数起例，最重要是决定出生时间，本派起出生时是以洛阳地区作为绝对", "标准，每与洛阳相差十五度经线便有一个小时之时差。", "民国四十四年至四十五年", "日光节约时间", "四月一日至九月三十日", "香港标准时间与洛阳时间相差七分半钟，除特别命外。", "民国四十六年至四十八年", "夏令时间", "四月一日至九月三十日", "例，如时头时尾生人外，一般可以作为标准时间来推算。", "香港有一段时间使用夏令时间，比标准时早一小时，", "民国四十九年至五十年", "夏令时间", "六月一日至九月三十日", "其施行时期分列于下表：", "民国五十一年至六十二年", "停止夏令时间", "标准时间", "民国六十三年至六十四年", "日光节约时间", "四月一日至九月三十日", "民国六十五年至六十七年", "停止夏令时间", "民国六十八年", "日光节约时间", "七月一日至九月三十日", "民国六十九年", "停止夏令时间", "民国七十年", "停止夏令时间", "注：关于[子时]的分割，尚有争论。有以由下午十一时至午夜一时，统归属为当日", "之子时者。即一交入十一时便作为新一日的开始。但笔者经验，仍以表列的分割为", "宜。即交入十二时(零时)，才是一天新的开始。", "全年标准时间", "例如：一九五八年西历五月十曰下午八时(左右生人)，本来八时应为戌时，因", "有夏令时间，应改为下午七时生人，作酉时算。"], "rec_scores": [0.9999280571937561, 0.9987792372703552, 0.9912049770355225, 0.9980787038803101, 0.9891087412834167, 0.9998632669448853, 0.9999580383300781, 0.9996746778488159, 0.9999583959579468, 0.9459893703460693, 0.99842369556427, 0.9927013516426086, 0.999024510383606, 0.996151328086853, 0.9846209287643433, 0.9976557493209839, 0.9210485219955444, 0.9998558759689331, 0.9970879554748535, 0.9900919795036316, 0.9421499371528625, 0.9580453038215637, 0.9973529577255249, 0.985961377620697, 0.9128161668777466, 0.9996051788330078, 0.999916136264801, 0.9968459606170654, 0.9868399500846863, 0.9951841235160828, 0.999873697757721, 0.9978076815605164, 0.9982264041900635, 0.9924982190132141, 0.9982936978340149, 0.9950833320617676, 0.998451828956604, 0.9946876764297485, 0.9968193769454956, 0.99810391664505, 0.9963065981864929, 0.997067928314209, 0.9955447316169739, 0.9981396198272705, 0.9876925349235535, 0.9937265515327454, 0.9983148574829102, 0.9917750954627991, 0.992771327495575, 0.9881374835968018, 0.990188479423523, 0.9987108707427979, 0.9908339381217957, 0.9986249208450317, 0.9979336261749268, 0.9993121027946472, 0.9997841715812683, 0.9998332858085632, 0.9971206784248352, 0.981181800365448, 0.99835604429245], "rec_polys": [[[325, 39], [524, 39], [524, 60], [325, 60]], [[1156, 37], [1358, 37], [1358, 64], [1156, 64]], [[121, 69], [754, 69], [754, 90], [121, 90]], [[82, 96], [767, 96], [767, 122], [82, 122]], [[954, 97], [1452, 99], [1452, 131], [954, 129]], [[82, 124], [454, 124], [454, 151], [82, 151]], [[118, 154], [307, 154], [307, 181], [118, 181]], [[1026, 168], [1086, 168], [1086, 197], [1026, 197]], [[1168, 167], [1230, 167], [1230, 197], [1168, 197]], [[1291, 168], [1389, 168], [1389, 197], [1291, 197]], [[119, 183], [768, 183], [768, 209], [119, 209]], [[82, 211], [768, 211], [768, 237], [82, 237]], [[967, 216], [1098, 216], [1098, 243], [967, 243]], [[1147, 218], [1202, 218], [1202, 241], [1147, 241]], [[1251, 216], [1381, 216], [1381, 243], [1251, 243]], [[81, 241], [614, 241], [614, 268], [81, 268]], [[651, 246], [667, 246], [667, 259], [651, 259]], [[82, 268], [768, 268], [768, 294], [82, 294]], [[968, 264], [1047, 264], [1047, 287], [968, 287]], [[1147, 264], [1226, 264], [1226, 287], [1147, 287]], [[1252, 266], [1379, 266], [1379, 287], [1252, 287]], [[118, 298], [272, 298], [272, 324], [118, 324]], [[967, 310], [1110, 310], [1110, 337], [967, 337]], [[1147, 314], [1224, 314], [1224, 335], [1147, 335]], [[1251, 314], [1379, 314], [1379, 335], [1251, 335]], [[121, 331], [767, 331], [767, 353], [121, 353]], [[82, 358], [528, 358], [528, 385], [82, 385]], [[968, 362], [1109, 362], [1109, 383], [968, 383]], [[1147, 362], [1226, 362], [1226, 383], [1147, 383]], [[1251, 360], [1381, 360], [1381, 386], [1251, 386]], [[119, 390], [565, 390], [565, 411], [119, 411]], [[970, 408], [1109, 408], [1109, 429], [970, 429]], [[1147, 408], [1202, 408], [1202, 431], [1147, 431]], [[1249, 406], [1381, 406], [1381, 432], [1249, 432]], [[239, 431], [612, 431], [612, 452], [239, 452]], [[270, 456], [616, 454], [616, 480], [270, 482]], [[970, 455], [1098, 455], [1098, 477], [970, 477]], [[1145, 454], [1202, 454], [1202, 477], [1145, 477]], [[1252, 455], [1381, 455], [1381, 477], [1252, 477]], [[239, 487], [402, 487], [402, 509], [239, 509]], [[968, 503], [1109, 503], [1109, 525], [968, 525]], [[1252, 503], [1331, 503], [1331, 525], [1252, 525]], [[468, 535], [516, 535], [516, 551], [468, 551]], [[968, 548], [1110, 548], [1110, 574], [968, 574]], [[1147, 549], [1226, 549], [1226, 572], [1147, 572]], [[1249, 548], [1381, 548], [1381, 574], [1249, 574]], [[970, 597], [1109, 597], [1109, 619], [970, 619]], [[1252, 597], [1331, 597], [1331, 620], [1252, 620]], [[968, 645], [1047, 645], [1047, 668], [968, 668]], [[1147, 645], [1228, 645], [1228, 666], [1147, 666]], [[1252, 643], [1381, 643], [1381, 670], [1252, 670]], [[968, 693], [1047, 693], [1047, 716], [968, 716]], [[1252, 693], [1331, 693], [1331, 716], [1252, 716]], [[968, 741], [1037, 741], [1037, 762], [968, 762]], [[1252, 741], [1331, 741], [1331, 764], [1252, 764]], [[914, 822], [1600, 822], [1600, 849], [914, 849]], [[914, 852], [1600, 852], [1600, 879], [914, 879]], [[916, 884], [1302, 884], [1302, 906], [916, 906]], [[458, 923], [521, 923], [521, 939], [458, 939]], [[123, 971], [767, 971], [767, 992], [123, 992]], [[82, 998], [472, 998], [472, 1024], [82, 1024]]], "rec_boxes": [[325, 39, 524, 60], [1156, 37, 1358, 64], [121, 69, 754, 90], [82, 96, 767, 122], [954, 97, 1452, 131], [82, 124, 454, 151], [118, 154, 307, 181], [1026, 168, 1086, 197], [1168, 167, 1230, 197], [1291, 168, 1389, 197], [119, 183, 768, 209], [82, 211, 768, 237], [967, 216, 1098, 243], [1147, 218, 1202, 241], [1251, 216, 1381, 243], [81, 241, 614, 268], [651, 246, 667, 259], [82, 268, 768, 294], [968, 264, 1047, 287], [1147, 264, 1226, 287], [1252, 266, 1379, 287], [118, 298, 272, 324], [967, 310, 1110, 337], [1147, 314, 1224, 335], [1251, 314, 1379, 335], [121, 331, 767, 353], [82, 358, 528, 385], [968, 362, 1109, 383], [1147, 362, 1226, 383], [1251, 360, 1381, 386], [119, 390, 565, 411], [970, 408, 1109, 429], [1147, 408, 1202, 431], [1249, 406, 1381, 432], [239, 431, 612, 452], [270, 454, 616, 482], [970, 455, 1098, 477], [1145, 454, 1202, 477], [1252, 455, 1381, 477], [239, 487, 402, 509], [968, 503, 1109, 525], [1252, 503, 1331, 525], [468, 535, 516, 551], [968, 548, 1110, 574], [1147, 549, 1226, 572], [1249, 548, 1381, 574], [970, 597, 1109, 619], [1252, 597, 1331, 620], [968, 645, 1047, 668], [1147, 645, 1228, 666], [1252, 643, 1381, 670], [968, 693, 1047, 716], [1252, 693, 1331, 716], [968, 741, 1037, 762], [1252, 741, 1331, 764], [914, 822, 1600, 849], [914, 852, 1600, 879], [916, 884, 1302, 906], [458, 923, 521, 939], [123, 971, 767, 992], [82, 998, 472, 1024]]}