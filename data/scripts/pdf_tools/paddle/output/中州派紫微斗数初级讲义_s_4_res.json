{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 4, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[1067, 25], [1602, 27], [1601, 71], [1066, 69]], [[165, 42], [635, 44], [635, 83], [165, 81]], [[111, 80], [635, 85], [635, 122], [110, 117]], [[1031, 71], [1165, 71], [1165, 115], [1031, 115]], [[108, 115], [343, 123], [341, 162], [107, 154]], [[1103, 122], [1195, 127], [1193, 167], [1101, 162]], [[1315, 127], [1458, 132], [1457, 171], [1313, 166]], [[379, 172], [403, 172], [403, 197], [379, 197]], [[460, 172], [484, 172], [484, 197], [460, 197]], [[1121, 170], [1174, 170], [1174, 214], [1121, 214]], [[1300, 175], [1466, 175], [1466, 214], [1300, 214]], [[168, 188], [233, 188], [233, 223], [168, 223]], [[278, 189], [371, 194], [369, 227], [277, 223]], [[165, 210], [231, 215], [228, 251], [162, 245]], [[278, 214], [379, 218], [378, 254], [277, 249]], [[1128, 218], [1160, 218], [1160, 255], [1128, 255]], [[1305, 222], [1461, 222], [1461, 261], [1305, 261]], [[163, 237], [227, 242], [224, 277], [160, 272]], [[279, 245], [388, 245], [388, 278], [279, 278]], [[161, 266], [219, 266], [219, 301], [161, 301]], [[274, 269], [398, 269], [398, 303], [274, 303]], [[1124, 266], [1158, 266], [1158, 303], [1124, 303]], [[1300, 264], [1460, 268], [1459, 307], [1299, 303]], [[161, 290], [225, 295], [223, 325], [159, 320]], [[273, 292], [386, 296], [385, 330], [271, 326]], [[160, 311], [233, 318], [229, 354], [157, 346]], [[273, 317], [353, 321], [351, 355], [271, 350]], [[1126, 314], [1154, 314], [1154, 346], [1126, 346]], [[1300, 312], [1458, 312], [1458, 346], [1300, 346]], [[160, 342], [221, 342], [221, 378], [160, 378]], [[272, 344], [360, 344], [360, 378], [272, 378]], [[158, 364], [220, 370], [217, 403], [155, 398]], [[1123, 356], [1156, 356], [1156, 392], [1123, 392]], [[1302, 356], [1454, 356], [1454, 390], [1302, 390]], [[274, 372], [346, 372], [346, 401], [274, 401]], [[156, 391], [222, 396], [219, 430], [153, 424]], [[267, 393], [434, 397], [433, 431], [266, 427]], [[1123, 402], [1154, 402], [1154, 441], [1123, 441]], [[1295, 399], [1459, 399], [1459, 438], [1295, 438]], [[156, 416], [227, 421], [225, 454], [154, 449]], [[268, 422], [363, 422], [363, 455], [268, 455]], [[155, 440], [220, 446], [217, 481], [152, 476]], [[267, 446], [385, 451], [383, 484], [266, 480]], [[1124, 450], [1151, 450], [1151, 486], [1124, 486]], [[1293, 447], [1459, 447], [1459, 486], [1293, 486]], [[156, 473], [216, 473], [216, 503], [156, 503]], [[267, 473], [346, 473], [346, 507], [267, 507]], [[154, 494], [224, 499], [221, 534], [152, 529]], [[268, 502], [351, 502], [351, 530], [268, 530]], [[1124, 500], [1152, 500], [1152, 530], [1124, 530]], [[1294, 493], [1454, 489], [1455, 528], [1295, 532]], [[153, 518], [231, 526], [228, 561], [150, 554]], [[267, 526], [407, 530], [406, 559], [266, 554]], [[152, 545], [221, 552], [217, 588], [148, 580]], [[1124, 542], [1151, 542], [1151, 572], [1124, 572]], [[1300, 539], [1452, 539], [1452, 572], [1300, 572]], [[268, 555], [386, 555], [386, 583], [268, 583]], [[153, 578], [219, 578], [219, 611], [153, 611]], [[267, 580], [396, 580], [396, 613], [267, 613]], [[1124, 587], [1152, 587], [1152, 619], [1124, 619]], [[1300, 583], [1452, 583], [1452, 617], [1300, 617]], [[149, 598], [227, 605], [224, 639], [146, 632]], [[267, 608], [361, 608], [361, 636], [267, 636]], [[147, 625], [227, 630], [225, 664], [145, 659]], [[264, 631], [362, 627], [364, 661], [266, 665]], [[1123, 631], [1154, 631], [1154, 668], [1123, 668]], [[1300, 626], [1456, 626], [1456, 665], [1300, 665]], [[146, 649], [217, 657], [213, 692], [143, 685]], [[262, 652], [372, 656], [371, 695], [261, 691]], [[145, 672], [216, 680], [211, 721], [141, 713]], [[265, 684], [353, 684], [353, 718], [265, 718]], [[1123, 675], [1156, 675], [1156, 716], [1123, 716]], [[1296, 676], [1462, 671], [1463, 710], [1297, 715]], [[144, 703], [213, 708], [210, 745], [141, 740]], [[265, 709], [441, 713], [440, 746], [265, 742]], [[1117, 725], [1160, 725], [1160, 759], [1117, 759]], [[1295, 723], [1465, 723], [1465, 757], [1295, 757]], [[142, 734], [209, 734], [209, 769], [142, 769]], [[265, 737], [344, 737], [344, 771], [265, 771]], [[135, 764], [621, 768], [621, 812], [135, 808]], [[84, 806], [440, 808], [440, 842], [84, 840]], [[922, 803], [1661, 792], [1661, 829], [923, 840]], [[914, 835], [1659, 822], [1660, 859], [914, 872]], [[919, 865], [1659, 852], [1660, 890], [920, 902]], [[25, 900], [300, 904], [300, 941], [24, 937]], [[919, 893], [1659, 882], [1660, 920], [919, 931]], [[919, 924], [1663, 913], [1663, 950], [920, 961]], [[0, 939], [114, 939], [114, 968], [0, 968]], [[922, 954], [1663, 941], [1663, 980], [923, 993]], [[921, 984], [1664, 971], [1665, 1010], [921, 1023]], [[927, 1076], [1064, 1070], [1065, 1104], [929, 1110]], [[1087, 1097], [1310, 1091], [1311, 1125], [1088, 1131]], [[968, 1122], [1666, 1113], [1667, 1157], [969, 1166]], [[1282, 1168], [1307, 1168], [1307, 1189], [1282, 1189]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["知道洛阳标准时间后，便可依下列表换算十二时辰：", "其他地方出生可以从出生地的经线相差而定出其时", "差，折算方法是以每十五度相差一小时，现把世界一些城", "时辰换算表", "市与洛阳相差举例如下：", "时辰", "标准洛阳时间", "城", "中", "吉", "凌晨12:00-上年1:00", "-17分钟", "北京Peking", "37分钟", "上海Shanghai", "丑", "上午1:00-上午3:00", "22分钟", "董庆Chungking", "5分钟", "广<PERSON>wong Chow", "第", "上午3:00-上午5:00", "7.5分钟", "香港Hong Kong", "-37.5分钟", "台北Taipei", "卯", "上午5:00-上午7:00", "-18分钟", "汕头Swatow", "-22分钟", "版", "上午7:00-上午9:00", "厦门Amoy", "+22分钟", "越南西贡Vietnam-Saigon", "e", "上午9:00-上午11:0", "-35.5分钟", "马尼拉Manila", "字10+", "新加坡Singapore", "女", "上午11:00-下年1:00", "-15分钟", "沙巴Sabak", "+7.5小时", "伦敦London", "未", "00", "+15.5小时", "三海市SanFrancisco", "+16小时", "申", "下午3:00-下年5:00", "温哥华Vancouver", "+15小时", "埃德蒙顿Edmonton", "显", "下年5:00-下午7:00", "+12.5小时", "温太华Ottawa", "+125小时", "纽约NewYork", "戌", "00:6-00", "", "换斯敦Houston", "+48分钟", "槟城Penang", "亥", "下午9:00-下午年11:00", "特", "泰国曼谷Thaiand Bangk", "夜子", "下午11:00-凌展12:00", "#", "文莱Brunei", "注：图表所列举之地方时差，是以当地时间作", "加或减，而求出洛阳之正确时间。", "我国计算年月日的历法：是阴历(以月亮为标准)和阳历(以太阳为标准)的混合农", "历。太阳和月亮在天上运时辰转换，有一个固定的差数，每年是十月二十一小时十", "二秒，因为有了这个差数，所以就增加闰月来弥补，每三年一闰，及每十九年七个", "过节气之分别及闺月推算法", "闰月来计算。中国夏历分十二个月，每个月分别有一个节和一个气。闰月即是没有", "中气的一个月。[子平]推算八字，是要以生曰是否过了节来决定是前一个月或后一", "甲过节气", "个月，所以不产生闰月的问题，例如某人出生于丁年农历正月的某曰，但因那天未", "过[立春]，所以便以丙年农历十二月(亥月)来算，但是紫微斗数却是以阴历作为基", "份推算的问题。", "乙闰月推算法", "闰月的推算，是以前半个月属前一个月份、后半个月属后一个月份，例如闰三", "10"], "rec_scores": [0.9914616346359253, 0.9995098114013672, 0.9690434336662292, 0.9977633357048035, 0.9927635788917542, 0.9977036714553833, 0.9986303448677063, 0.9947874546051025, 0.20092789828777313, 0.321584016084671, 0.9160698652267456, 0.9029911160469055, 0.9818394184112549, 0.9578097462654114, 0.982441782951355, 0.9889199137687683, 0.9212709069252014, 0.9657578468322754, 0.894207775592804, 0.9944002032279968, 0.982362687587738, 0.9176801443099976, 0.9596574306488037, 0.9940224885940552, 0.973375678062439, 0.9037949442863464, 0.9132297039031982, 0.3783795237541199, 0.9404431581497192, 0.887729823589325, 0.9533755779266357, 0.8700393438339233, 0.5559800863265991, 0.9694411754608154, 0.9564695358276367, 0.7298038601875305, 0.9489916563034058, 0.8758842349052429, 0.9407860636711121, 0.9404366612434387, 0.9306440353393555, 0.45800620317459106, 0.9798672199249268, 0.347272127866745, 0.9067763090133667, 0.9699079394340515, 0.9871516823768616, 0.9398820996284485, 0.9395952224731445, 0.9756059050559998, 0.3604309558868408, 0.9166465997695923, 0.952075183391571, 0.9512224197387695, 0.9497002363204956, 0.8685697317123413, 0.9956080913543701, 0.9961193799972534, 0.984121561050415, 0.5274162888526917, 0.9061949849128723, 0.953398585319519, 0.8650573492050171, 0.9586799144744873, 0.9603849053382874, 0.6865036487579346, 0.7538615465164185, 0.0, 0.9315299987792969, 0.9190409779548645, 0.8988120555877686, 0.9107316136360168, 0.8773916363716125, 0.28248271346092224, 0.9728966951370239, 0.999634861946106, 0.9519979953765869, 0.34712523221969604, 0.9882125854492188, 0.9977898597717285, 0.9993143081665039, 0.9990416765213013, 0.999469518661499, 0.9770672917366028, 0.9576975703239441, 0.9873319268226624, 0.9762451648712158, 0.9972042441368103, 0.9895713329315186, 0.9946662783622742, 0.9982743859291077, 0.9358752369880676, 0.9861716032028198, 0.9995648264884949], "rec_polys": [[[1067, 25], [1602, 27], [1601, 71], [1066, 69]], [[165, 42], [635, 44], [635, 83], [165, 81]], [[111, 80], [635, 85], [635, 122], [110, 117]], [[1031, 71], [1165, 71], [1165, 115], [1031, 115]], [[108, 115], [343, 123], [341, 162], [107, 154]], [[1103, 122], [1195, 127], [1193, 167], [1101, 162]], [[1315, 127], [1458, 132], [1457, 171], [1313, 166]], [[379, 172], [403, 172], [403, 197], [379, 197]], [[460, 172], [484, 172], [484, 197], [460, 197]], [[1121, 170], [1174, 170], [1174, 214], [1121, 214]], [[1300, 175], [1466, 175], [1466, 214], [1300, 214]], [[168, 188], [233, 188], [233, 223], [168, 223]], [[278, 189], [371, 194], [369, 227], [277, 223]], [[165, 210], [231, 215], [228, 251], [162, 245]], [[278, 214], [379, 218], [378, 254], [277, 249]], [[1128, 218], [1160, 218], [1160, 255], [1128, 255]], [[1305, 222], [1461, 222], [1461, 261], [1305, 261]], [[163, 237], [227, 242], [224, 277], [160, 272]], [[279, 245], [388, 245], [388, 278], [279, 278]], [[161, 266], [219, 266], [219, 301], [161, 301]], [[274, 269], [398, 269], [398, 303], [274, 303]], [[1124, 266], [1158, 266], [1158, 303], [1124, 303]], [[1300, 264], [1460, 268], [1459, 307], [1299, 303]], [[161, 290], [225, 295], [223, 325], [159, 320]], [[273, 292], [386, 296], [385, 330], [271, 326]], [[160, 311], [233, 318], [229, 354], [157, 346]], [[273, 317], [353, 321], [351, 355], [271, 350]], [[1126, 314], [1154, 314], [1154, 346], [1126, 346]], [[1300, 312], [1458, 312], [1458, 346], [1300, 346]], [[160, 342], [221, 342], [221, 378], [160, 378]], [[272, 344], [360, 344], [360, 378], [272, 378]], [[158, 364], [220, 370], [217, 403], [155, 398]], [[1123, 356], [1156, 356], [1156, 392], [1123, 392]], [[1302, 356], [1454, 356], [1454, 390], [1302, 390]], [[274, 372], [346, 372], [346, 401], [274, 401]], [[156, 391], [222, 396], [219, 430], [153, 424]], [[267, 393], [434, 397], [433, 431], [266, 427]], [[1123, 402], [1154, 402], [1154, 441], [1123, 441]], [[1295, 399], [1459, 399], [1459, 438], [1295, 438]], [[156, 416], [227, 421], [225, 454], [154, 449]], [[268, 422], [363, 422], [363, 455], [268, 455]], [[155, 440], [220, 446], [217, 481], [152, 476]], [[267, 446], [385, 451], [383, 484], [266, 480]], [[1124, 450], [1151, 450], [1151, 486], [1124, 486]], [[1293, 447], [1459, 447], [1459, 486], [1293, 486]], [[156, 473], [216, 473], [216, 503], [156, 503]], [[267, 473], [346, 473], [346, 507], [267, 507]], [[154, 494], [224, 499], [221, 534], [152, 529]], [[268, 502], [351, 502], [351, 530], [268, 530]], [[1124, 500], [1152, 500], [1152, 530], [1124, 530]], [[1294, 493], [1454, 489], [1455, 528], [1295, 532]], [[153, 518], [231, 526], [228, 561], [150, 554]], [[267, 526], [407, 530], [406, 559], [266, 554]], [[152, 545], [221, 552], [217, 588], [148, 580]], [[1124, 542], [1151, 542], [1151, 572], [1124, 572]], [[1300, 539], [1452, 539], [1452, 572], [1300, 572]], [[268, 555], [386, 555], [386, 583], [268, 583]], [[153, 578], [219, 578], [219, 611], [153, 611]], [[267, 580], [396, 580], [396, 613], [267, 613]], [[1124, 587], [1152, 587], [1152, 619], [1124, 619]], [[1300, 583], [1452, 583], [1452, 617], [1300, 617]], [[149, 598], [227, 605], [224, 639], [146, 632]], [[267, 608], [361, 608], [361, 636], [267, 636]], [[147, 625], [227, 630], [225, 664], [145, 659]], [[264, 631], [362, 627], [364, 661], [266, 665]], [[1123, 631], [1154, 631], [1154, 668], [1123, 668]], [[1300, 626], [1456, 626], [1456, 665], [1300, 665]], [[146, 649], [217, 657], [213, 692], [143, 685]], [[262, 652], [372, 656], [371, 695], [261, 691]], [[145, 672], [216, 680], [211, 721], [141, 713]], [[265, 684], [353, 684], [353, 718], [265, 718]], [[1123, 675], [1156, 675], [1156, 716], [1123, 716]], [[1296, 676], [1462, 671], [1463, 710], [1297, 715]], [[144, 703], [213, 708], [210, 745], [141, 740]], [[265, 709], [441, 713], [440, 746], [265, 742]], [[1117, 725], [1160, 725], [1160, 759], [1117, 759]], [[1295, 723], [1465, 723], [1465, 757], [1295, 757]], [[142, 734], [209, 734], [209, 769], [142, 769]], [[265, 737], [344, 737], [344, 771], [265, 771]], [[135, 764], [621, 768], [621, 812], [135, 808]], [[84, 806], [440, 808], [440, 842], [84, 840]], [[922, 803], [1661, 792], [1661, 829], [923, 840]], [[914, 835], [1659, 822], [1660, 859], [914, 872]], [[919, 865], [1659, 852], [1660, 890], [920, 902]], [[25, 900], [300, 904], [300, 941], [24, 937]], [[919, 893], [1659, 882], [1660, 920], [919, 931]], [[919, 924], [1663, 913], [1663, 950], [920, 961]], [[0, 939], [114, 939], [114, 968], [0, 968]], [[922, 954], [1663, 941], [1663, 980], [923, 993]], [[921, 984], [1664, 971], [1665, 1010], [921, 1023]], [[927, 1076], [1064, 1070], [1065, 1104], [929, 1110]], [[1087, 1097], [1310, 1091], [1311, 1125], [1088, 1131]], [[968, 1122], [1666, 1113], [1667, 1157], [969, 1166]], [[1282, 1168], [1307, 1168], [1307, 1189], [1282, 1189]]], "rec_boxes": [[1066, 25, 1602, 71], [165, 42, 635, 83], [110, 80, 635, 122], [1031, 71, 1165, 115], [107, 115, 343, 162], [1101, 122, 1195, 167], [1313, 127, 1458, 171], [379, 172, 403, 197], [460, 172, 484, 197], [1121, 170, 1174, 214], [1300, 175, 1466, 214], [168, 188, 233, 223], [277, 189, 371, 227], [162, 210, 231, 251], [277, 214, 379, 254], [1128, 218, 1160, 255], [1305, 222, 1461, 261], [160, 237, 227, 277], [279, 245, 388, 278], [161, 266, 219, 301], [274, 269, 398, 303], [1124, 266, 1158, 303], [1299, 264, 1460, 307], [159, 290, 225, 325], [271, 292, 386, 330], [157, 311, 233, 354], [271, 317, 353, 355], [1126, 314, 1154, 346], [1300, 312, 1458, 346], [160, 342, 221, 378], [272, 344, 360, 378], [155, 364, 220, 403], [1123, 356, 1156, 392], [1302, 356, 1454, 390], [274, 372, 346, 401], [153, 391, 222, 430], [266, 393, 434, 431], [1123, 402, 1154, 441], [1295, 399, 1459, 438], [154, 416, 227, 454], [268, 422, 363, 455], [152, 440, 220, 481], [266, 446, 385, 484], [1124, 450, 1151, 486], [1293, 447, 1459, 486], [156, 473, 216, 503], [267, 473, 346, 507], [152, 494, 224, 534], [268, 502, 351, 530], [1124, 500, 1152, 530], [1294, 489, 1455, 532], [150, 518, 231, 561], [266, 526, 407, 559], [148, 545, 221, 588], [1124, 542, 1151, 572], [1300, 539, 1452, 572], [268, 555, 386, 583], [153, 578, 219, 611], [267, 580, 396, 613], [1124, 587, 1152, 619], [1300, 583, 1452, 617], [146, 598, 227, 639], [267, 608, 361, 636], [145, 625, 227, 664], [264, 627, 364, 665], [1123, 631, 1154, 668], [1300, 626, 1456, 665], [143, 649, 217, 692], [261, 652, 372, 695], [141, 672, 216, 721], [265, 684, 353, 718], [1123, 675, 1156, 716], [1296, 671, 1463, 715], [141, 703, 213, 745], [265, 709, 441, 746], [1117, 725, 1160, 759], [1295, 723, 1465, 757], [142, 734, 209, 769], [265, 737, 344, 771], [135, 764, 621, 812], [84, 806, 440, 842], [922, 792, 1661, 840], [914, 822, 1660, 872], [919, 852, 1660, 902], [24, 900, 300, 941], [919, 882, 1660, 931], [919, 913, 1663, 961], [0, 939, 114, 968], [922, 941, 1663, 993], [921, 971, 1665, 1023], [927, 1070, 1065, 1110], [1087, 1091, 1311, 1131], [968, 1113, 1667, 1166], [1282, 1168, 1307, 1189]]}