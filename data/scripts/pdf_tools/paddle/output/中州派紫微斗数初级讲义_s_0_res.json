{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 0, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[284, 0], [493, 0], [493, 19], [284, 19]], [[351, 34], [428, 34], [428, 83], [351, 83]], [[1086, 44], [1496, 44], [1496, 76], [1086, 76]], [[7, 96], [121, 96], [121, 140], [7, 140]], [[1188, 108], [1381, 108], [1381, 145], [1188, 145]], [[5, 161], [214, 161], [214, 206], [5, 206]], [[1242, 163], [1328, 163], [1328, 186], [1242, 186]], [[212, 177], [256, 177], [256, 195], [212, 195]], [[949, 186], [1658, 190], [1658, 222], [949, 218]], [[91, 213], [216, 213], [216, 252], [91, 252]], [[681, 222], [714, 222], [714, 246], [681, 246]], [[907, 218], [1431, 218], [1431, 250], [907, 250]], [[90, 241], [253, 247], [252, 286], [89, 280]], [[681, 252], [723, 252], [723, 282], [681, 282]], [[949, 246], [1658, 250], [1658, 287], [949, 284]], [[90, 271], [251, 275], [250, 318], [89, 313]], [[656, 282], [724, 282], [724, 315], [656, 315]], [[905, 278], [1658, 280], [1658, 317], [905, 315]], [[2, 319], [540, 323], [540, 367], [2, 363]], [[907, 310], [1658, 314], [1658, 351], [907, 347]], [[905, 340], [1642, 344], [1642, 381], [905, 377]], [[88, 368], [255, 373], [254, 412], [87, 407]], [[686, 379], [719, 379], [719, 406], [686, 406]], [[909, 376], [1144, 376], [1144, 408], [909, 408]], [[88, 398], [311, 404], [310, 443], [87, 437]], [[949, 406], [1558, 409], [1558, 443], [949, 439]], [[1539, 407], [1656, 412], [1655, 445], [1538, 441]], [[0, 443], [454, 443], [454, 491], [0, 491]], [[909, 436], [1656, 440], [1656, 477], [909, 473]], [[907, 471], [1284, 471], [1284, 503], [907, 503]], [[85, 496], [318, 500], [317, 537], [84, 533]], [[949, 500], [1652, 503], [1652, 537], [949, 533]], [[85, 526], [330, 530], [329, 569], [84, 565]], [[904, 530], [1649, 534], [1649, 571], [903, 567]], [[318, 542], [432, 542], [432, 558], [318, 558]], [[903, 565], [1652, 569], [1652, 601], [903, 597]], [[0, 580], [54, 580], [54, 615], [0, 615]], [[69, 576], [351, 582], [350, 621], [68, 615]], [[51, 587], [77, 587], [77, 606], [51, 606]], [[370, 590], [432, 590], [432, 610], [370, 610]], [[903, 599], [1454, 599], [1454, 631], [903, 631]], [[946, 627], [1652, 631], [1652, 665], [945, 661]], [[0, 638], [54, 638], [54, 681], [0, 681]], [[67, 638], [349, 642], [349, 681], [66, 677]], [[617, 645], [688, 645], [688, 679], [617, 679]], [[900, 657], [1652, 661], [1652, 698], [900, 695]], [[68, 700], [353, 700], [353, 743], [68, 743]], [[903, 691], [1651, 695], [1651, 728], [903, 725]], [[0, 704], [51, 704], [51, 743], [0, 743]], [[902, 725], [1100, 725], [1100, 757], [902, 757]], [[942, 753], [1649, 757], [1649, 789], [942, 785]], [[0, 767], [51, 767], [51, 806], [0, 806]], [[51, 776], [74, 776], [74, 794], [51, 794]], [[68, 767], [351, 767], [351, 805], [68, 805]], [[902, 783], [1651, 787], [1651, 821], [902, 817]], [[902, 813], [1649, 817], [1649, 854], [902, 851]], [[0, 828], [53, 828], [53, 872], [0, 872]], [[67, 826], [349, 826], [349, 870], [67, 870]], [[900, 845], [1645, 849], [1645, 886], [900, 883]], [[0, 885], [217, 882], [218, 932], [0, 934]], [[900, 877], [1644, 883], [1644, 915], [900, 909]], [[900, 913], [1014, 913], [1014, 941], [900, 941]], [[60, 936], [284, 936], [284, 975], [60, 975]], [[939, 937], [1644, 945], [1643, 980], [938, 973]], [[61, 966], [226, 966], [226, 1005], [61, 1005]], [[895, 971], [1644, 975], [1644, 1012], [895, 1008]], [[61, 996], [263, 996], [263, 1035], [61, 1035]], [[600, 1007], [675, 1007], [675, 1040], [600, 1040]], [[898, 1008], [949, 1008], [949, 1039], [898, 1039]], [[7, 1051], [39, 1051], [39, 1083], [7, 1083]], [[35, 1051], [68, 1051], [68, 1076], [35, 1076]], [[61, 1047], [102, 1047], [102, 1081], [61, 1081]], [[100, 1058], [114, 1058], [114, 1072], [100, 1072]], [[632, 1062], [674, 1062], [674, 1086], [632, 1086]], [[1362, 1068], [1639, 1074], [1638, 1108], [1361, 1102]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["中州派紫薇斗数初级讲义", "目录", "中州派紫微斗数初级讲义", "一、序论", "王亭之编著", "二、入门及起例·.", "一.序论", "......", "紫微斗数是由北宋年代开始发展的一门术数，它的前身是(十八飞星)。刺激起", "甲安星法", "...6", "这门术数兴起的背景，是由于当时[五星]与[子平]的竞争。", "乙闰月推算法", ".10", "原来我国古代推算禄命之术，最成功的是[五星]。它根据人的出生年、月、日、", "丙星盘推断法", "..0", "时，先排成干支八字，然后由八字据公式计算，水、火、木、金、土五星，及太阴", "三、诸星曜及星系显示的面貌身型性格特征", "太阳的行度，分布于十二宫位，总称之为[七政]；此外，另据公式布二十八宿，及", "紫气、月孛、罗喉、计都等[四馀]的宫度，井由此推断人的禄命。这种算命术,", "甲形性赋注释·", "29..", "因此也就称为[七政四馀]。", "乙[中州派]诸星形性", "但由于古代天文学的计算公式不精密，时间一久，即变成错宫失度，", "，因此后", "四、诸星曜及星系坐十二宫吉凶推断", "来虽然有[琴堂派]兴起，另行厘定[量天尺]，以及创立星曜会合的新推断法，但依", "然无济于事，结果仍让[子平家]取得优势。", "甲诸星曜基本性质…", "[子平]可以说是截取[五星]推命术的上半截而革命 发展出来的术数。一般相", "乙诸星曜入十二宫性质", "信，它兴创于唐代。它的推算方法是，仍然据人的生年、月、日、时起干支八字，", "...............", "称为[四柱]，然后根据八字的阴阳五行、生 克制化、刑冲破害、三合六合、丛辰诸", "(附)", "辅佐煞曜对兄弟宫的影响", "", "..........", "", "[子平]之学，由宋代起，经历代文人学士的发展，至今已获得很大的成就，尤", "(W)", "辅佐煞曜对子女宫的影响", ".158", "其是明代传为刘伯温所注的[滴天髓]，更将这门算命术发扬成为哲理，清代陈素庵", "辅佐煞曜对财帛宫的影响", "相国启后承先，撰成[命理约言]，由是确立了所谓[书房派]的地位，与[江湖派]分", "(附)", "成子平学的两大壁垒。", "", "(附)", "", "辅佐煞曜对迁移宫的影响", "所不如，却依然能够推断准确。一些历代相传的口诀，如[日坐比肩逢比劫、必然三", "度作新郎]；[癸水生人用戊官，少年定嫁白头夫]等，的确有一定的准确性。这些口", "(附)", "辅佐煞曜对交友宫的影响", "甲", "五、年限推断法", "湖派]却依然可以迄立不倒。这 等于科学的发展，理论日日翻新，但实验却依然是", "理论的基础。", "甲大限与流年的推断", "本讲义保留一切版权利益，如有任何人士翻印、抄录、复制、翻译该讲义，或", "乙流月的推断", "未得著作人书面许可而引用该讲义部分或全部内容者，将进行法律诉讼并追究一切", "丙流日流时的推断", "..221", "责任。", "后", "", "2", "", "·224", "紫微文化服务社有限公司谨启"], "rec_scores": [0.9982724785804749, 0.9999121427536011, 0.9996116757392883, 0.993407130241394, 0.9998315572738647, 0.8927691578865051, 0.94569993019104, 0.5459145903587341, 0.9974706172943115, 0.9985446333885193, 0.6266103982925415, 0.9927882552146912, 0.9886978268623352, 0.7100139260292053, 0.9965591430664062, 0.998786985874176, 0.6403053402900696, 0.99797123670578, 0.9980993866920471, 0.9905111789703369, 0.9802559614181519, 0.9169842600822449, 0.7671663761138916, 0.9851617217063904, 0.9599294662475586, 0.9984564185142517, 0.9998151063919067, 0.9812983274459839, 0.9954541921615601, 0.9991676211357117, 0.9530441761016846, 0.9883139133453369, 0.9961642026901245, 0.9910628795623779, 0.6911113858222961, 0.9881570935249329, 0.967933177947998, 0.994976282119751, 0.0, 0.6092716455459595, 0.0, 0.9953889846801758, 0.7772941589355469, 0.9921506643295288, 0.896268367767334, 0.9966626763343811, 0.9928813576698303, 0.9887654185295105, 0.9627716541290283, 0.9978928565979004, 0.0, 0.9771292209625244, 0.0, 0.9938493371009827, 0.9970654845237732, 0.9885010719299316, 0.9289955496788025, 0.9915710091590881, 0.3037818968296051, 0.9972134232521057, 0.9826253056526184, 0.9999304413795471, 0.9982787370681763, 0.9960076808929443, 0.9963529109954834, 0.9991633892059326, 0.9964340925216675, 0.822342038154602, 0.999819278717041, 0.9999983310699463, 0.0, 0.7529124021530151, 0.0, 0.8915703296661377, 0.9983965754508972], "rec_polys": [[[284, 0], [493, 0], [493, 19], [284, 19]], [[351, 34], [428, 34], [428, 83], [351, 83]], [[1086, 44], [1496, 44], [1496, 76], [1086, 76]], [[7, 96], [121, 96], [121, 140], [7, 140]], [[1188, 108], [1381, 108], [1381, 145], [1188, 145]], [[5, 161], [214, 161], [214, 206], [5, 206]], [[1242, 163], [1328, 163], [1328, 186], [1242, 186]], [[212, 177], [256, 177], [256, 195], [212, 195]], [[949, 186], [1658, 190], [1658, 222], [949, 218]], [[91, 213], [216, 213], [216, 252], [91, 252]], [[681, 222], [714, 222], [714, 246], [681, 246]], [[907, 218], [1431, 218], [1431, 250], [907, 250]], [[90, 241], [253, 247], [252, 286], [89, 280]], [[681, 252], [723, 252], [723, 282], [681, 282]], [[949, 246], [1658, 250], [1658, 287], [949, 284]], [[90, 271], [251, 275], [250, 318], [89, 313]], [[656, 282], [724, 282], [724, 315], [656, 315]], [[905, 278], [1658, 280], [1658, 317], [905, 315]], [[2, 319], [540, 323], [540, 367], [2, 363]], [[907, 310], [1658, 314], [1658, 351], [907, 347]], [[905, 340], [1642, 344], [1642, 381], [905, 377]], [[88, 368], [255, 373], [254, 412], [87, 407]], [[686, 379], [719, 379], [719, 406], [686, 406]], [[909, 376], [1144, 376], [1144, 408], [909, 408]], [[88, 398], [311, 404], [310, 443], [87, 437]], [[949, 406], [1558, 409], [1558, 443], [949, 439]], [[1539, 407], [1656, 412], [1655, 445], [1538, 441]], [[0, 443], [454, 443], [454, 491], [0, 491]], [[909, 436], [1656, 440], [1656, 477], [909, 473]], [[907, 471], [1284, 471], [1284, 503], [907, 503]], [[85, 496], [318, 500], [317, 537], [84, 533]], [[949, 500], [1652, 503], [1652, 537], [949, 533]], [[85, 526], [330, 530], [329, 569], [84, 565]], [[904, 530], [1649, 534], [1649, 571], [903, 567]], [[318, 542], [432, 542], [432, 558], [318, 558]], [[903, 565], [1652, 569], [1652, 601], [903, 597]], [[0, 580], [54, 580], [54, 615], [0, 615]], [[69, 576], [351, 582], [350, 621], [68, 615]], [[51, 587], [77, 587], [77, 606], [51, 606]], [[370, 590], [432, 590], [432, 610], [370, 610]], [[903, 599], [1454, 599], [1454, 631], [903, 631]], [[946, 627], [1652, 631], [1652, 665], [945, 661]], [[0, 638], [54, 638], [54, 681], [0, 681]], [[67, 638], [349, 642], [349, 681], [66, 677]], [[617, 645], [688, 645], [688, 679], [617, 679]], [[900, 657], [1652, 661], [1652, 698], [900, 695]], [[68, 700], [353, 700], [353, 743], [68, 743]], [[903, 691], [1651, 695], [1651, 728], [903, 725]], [[0, 704], [51, 704], [51, 743], [0, 743]], [[902, 725], [1100, 725], [1100, 757], [902, 757]], [[942, 753], [1649, 757], [1649, 789], [942, 785]], [[0, 767], [51, 767], [51, 806], [0, 806]], [[51, 776], [74, 776], [74, 794], [51, 794]], [[68, 767], [351, 767], [351, 805], [68, 805]], [[902, 783], [1651, 787], [1651, 821], [902, 817]], [[902, 813], [1649, 817], [1649, 854], [902, 851]], [[0, 828], [53, 828], [53, 872], [0, 872]], [[67, 826], [349, 826], [349, 870], [67, 870]], [[900, 845], [1645, 849], [1645, 886], [900, 883]], [[0, 885], [217, 882], [218, 932], [0, 934]], [[900, 877], [1644, 883], [1644, 915], [900, 909]], [[900, 913], [1014, 913], [1014, 941], [900, 941]], [[60, 936], [284, 936], [284, 975], [60, 975]], [[939, 937], [1644, 945], [1643, 980], [938, 973]], [[61, 966], [226, 966], [226, 1005], [61, 1005]], [[895, 971], [1644, 975], [1644, 1012], [895, 1008]], [[61, 996], [263, 996], [263, 1035], [61, 1035]], [[600, 1007], [675, 1007], [675, 1040], [600, 1040]], [[898, 1008], [949, 1008], [949, 1039], [898, 1039]], [[7, 1051], [39, 1051], [39, 1083], [7, 1083]], [[35, 1051], [68, 1051], [68, 1076], [35, 1076]], [[61, 1047], [102, 1047], [102, 1081], [61, 1081]], [[100, 1058], [114, 1058], [114, 1072], [100, 1072]], [[632, 1062], [674, 1062], [674, 1086], [632, 1086]], [[1362, 1068], [1639, 1074], [1638, 1108], [1361, 1102]]], "rec_boxes": [[284, 0, 493, 19], [351, 34, 428, 83], [1086, 44, 1496, 76], [7, 96, 121, 140], [1188, 108, 1381, 145], [5, 161, 214, 206], [1242, 163, 1328, 186], [212, 177, 256, 195], [949, 186, 1658, 222], [91, 213, 216, 252], [681, 222, 714, 246], [907, 218, 1431, 250], [89, 241, 253, 286], [681, 252, 723, 282], [949, 246, 1658, 287], [89, 271, 251, 318], [656, 282, 724, 315], [905, 278, 1658, 317], [2, 319, 540, 367], [907, 310, 1658, 351], [905, 340, 1642, 381], [87, 368, 255, 412], [686, 379, 719, 406], [909, 376, 1144, 408], [87, 398, 311, 443], [949, 406, 1558, 443], [1538, 407, 1656, 445], [0, 443, 454, 491], [909, 436, 1656, 477], [907, 471, 1284, 503], [84, 496, 318, 537], [949, 500, 1652, 537], [84, 526, 330, 569], [903, 530, 1649, 571], [318, 542, 432, 558], [903, 565, 1652, 601], [0, 580, 54, 615], [68, 576, 351, 621], [51, 587, 77, 606], [370, 590, 432, 610], [903, 599, 1454, 631], [945, 627, 1652, 665], [0, 638, 54, 681], [66, 638, 349, 681], [617, 645, 688, 679], [900, 657, 1652, 698], [68, 700, 353, 743], [903, 691, 1651, 728], [0, 704, 51, 743], [902, 725, 1100, 757], [942, 753, 1649, 789], [0, 767, 51, 806], [51, 776, 74, 794], [68, 767, 351, 805], [902, 783, 1651, 821], [902, 813, 1649, 854], [0, 828, 53, 872], [67, 826, 349, 870], [900, 845, 1645, 886], [0, 882, 218, 934], [900, 877, 1644, 915], [900, 913, 1014, 941], [60, 936, 284, 975], [938, 937, 1644, 980], [61, 966, 226, 1005], [895, 971, 1644, 1012], [61, 996, 263, 1035], [600, 1007, 675, 1040], [898, 1008, 949, 1039], [7, 1051, 39, 1083], [35, 1051, 68, 1076], [61, 1047, 102, 1081], [100, 1058, 114, 1072], [632, 1062, 674, 1086], [1361, 1068, 1639, 1108]]}