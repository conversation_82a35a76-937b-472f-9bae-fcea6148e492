{"input_path": "../data/紫微斗数全书_s.pdf", "page_index": 1, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[184, 0], [455, 0], [455, 53], [184, 53]], [[186, 36], [460, 46], [458, 95], [184, 85]], [[399, 250], [786, 260], [784, 302], [398, 293]], [[408, 360], [787, 360], [787, 396], [408, 396]], [[406, 454], [789, 452], [789, 496], [406, 498]], [[408, 556], [789, 554], [789, 593], [408, 595]], [[223, 658], [264, 658], [264, 705], [223, 705]], [[464, 654], [510, 654], [510, 705], [464, 705]], [[709, 656], [757, 656], [757, 705], [709, 705]], [[962, 656], [1001, 656], [1001, 700], [962, 700]], [[211, 723], [278, 723], [278, 772], [211, 772]], [[455, 719], [521, 719], [521, 770], [455, 770]], [[700, 719], [767, 719], [767, 768], [700, 768]], [[918, 719], [1046, 719], [1046, 765], [918, 765]], [[181, 789], [310, 789], [310, 840], [181, 840]], [[424, 786], [548, 786], [548, 831], [424, 831]], [[698, 781], [769, 781], [769, 838], [698, 838]], [[227, 860], [268, 860], [268, 905], [227, 905]], [[564, 853], [654, 853], [654, 898], [564, 898]], [[962, 854], [1003, 854], [1003, 902], [962, 902]], [[216, 924], [280, 924], [280, 968], [216, 968]], [[950, 917], [1019, 917], [1019, 968], [950, 968]], [[181, 984], [310, 984], [310, 1035], [181, 1035]], [[950, 982], [1016, 982], [1016, 1035], [950, 1035]], [[227, 1056], [266, 1056], [266, 1100], [227, 1100]], [[962, 1051], [1003, 1051], [1003, 1098], [962, 1098]], [[209, 1117], [282, 1117], [282, 1174], [209, 1174]], [[952, 1116], [1016, 1116], [1016, 1160], [952, 1160]], [[179, 1182], [308, 1182], [308, 1233], [179, 1233]], [[971, 1191], [1008, 1191], [1008, 1214], [971, 1214]], [[221, 1244], [268, 1251], [261, 1301], [214, 1295]], [[464, 1249], [505, 1249], [505, 1296], [464, 1296]], [[709, 1244], [755, 1244], [755, 1296], [709, 1296]], [[961, 1252], [1001, 1252], [1001, 1300], [961, 1300]], [[209, 1317], [275, 1317], [275, 1361], [209, 1361]], [[453, 1313], [517, 1318], [513, 1362], [449, 1357]], [[697, 1312], [764, 1312], [764, 1361], [697, 1361]], [[950, 1319], [1008, 1319], [1008, 1356], [950, 1356]], [[177, 1379], [307, 1379], [307, 1430], [177, 1430]], [[420, 1381], [546, 1381], [546, 1426], [420, 1426]], [[688, 1373], [765, 1367], [771, 1436], [693, 1442]], [[948, 1382], [1010, 1382], [1010, 1424], [948, 1424]], [[596, 1633], [631, 1633], [631, 1670], [596, 1670]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["紫微斗数圈", "<PERSON><PERSON>", "离火宫中六岁奇，初二骑马初四龙,", "进三退二各一日，逆回三步是生时，", "先阳后阴逆退二，另有进一各期端，", "退二安一退二日，顺进五宫方是基。", "B", "士", "未", "申", "初十", "初二", "初八", "十四二八", "二四二九", "+三K+", "=", "辰", "木三局", "显", "初四", "初一", "十八二三", "+二", "卯", "", "十二", "初七", "十七二七", "K", "寅", "丑", "子", "亥", "初六", "初五", "初九", "初三", "十一二六", "十五二五", "九", "三十", "93"], "rec_scores": [0.9948169589042664, 0.9492928385734558, 0.9750139713287354, 0.9981768131256104, 0.9978604912757874, 0.9977449178695679, 0.8746013045310974, 0.7331283688545227, 0.9664146900177002, 0.999494194984436, 0.9959301352500916, 0.9999479651451111, 0.9999564290046692, 0.999336838722229, 0.9998673796653748, 0.7775841951370239, 0.8876163959503174, 0.9997757077217102, 0.9943411350250244, 0.9528778791427612, 0.9999635815620422, 0.9420905709266663, 0.9909018874168396, 0.8126732110977173, 0.6261284351348877, 0.0, 0.9459697008132935, 0.9998152256011963, 0.7396385669708252, 0.9356545209884644, 0.9981091022491455, 0.9905440807342529, 0.9964821338653564, 0.5060091018676758, 0.9997237920761108, 0.9999924302101135, 0.9998970031738281, 0.9998948574066162, 0.8526833653450012, 0.999763011932373, 0.9865468740463257, 0.7189857363700867, 0.9992756247520447], "rec_polys": [[[184, 0], [455, 0], [455, 53], [184, 53]], [[186, 36], [460, 46], [458, 95], [184, 85]], [[399, 250], [786, 260], [784, 302], [398, 293]], [[408, 360], [787, 360], [787, 396], [408, 396]], [[406, 454], [789, 452], [789, 496], [406, 498]], [[408, 556], [789, 554], [789, 593], [408, 595]], [[223, 658], [264, 658], [264, 705], [223, 705]], [[464, 654], [510, 654], [510, 705], [464, 705]], [[709, 656], [757, 656], [757, 705], [709, 705]], [[962, 656], [1001, 656], [1001, 700], [962, 700]], [[211, 723], [278, 723], [278, 772], [211, 772]], [[455, 719], [521, 719], [521, 770], [455, 770]], [[700, 719], [767, 719], [767, 768], [700, 768]], [[918, 719], [1046, 719], [1046, 765], [918, 765]], [[181, 789], [310, 789], [310, 840], [181, 840]], [[424, 786], [548, 786], [548, 831], [424, 831]], [[698, 781], [769, 781], [769, 838], [698, 838]], [[227, 860], [268, 860], [268, 905], [227, 905]], [[564, 853], [654, 853], [654, 898], [564, 898]], [[962, 854], [1003, 854], [1003, 902], [962, 902]], [[216, 924], [280, 924], [280, 968], [216, 968]], [[950, 917], [1019, 917], [1019, 968], [950, 968]], [[181, 984], [310, 984], [310, 1035], [181, 1035]], [[950, 982], [1016, 982], [1016, 1035], [950, 1035]], [[227, 1056], [266, 1056], [266, 1100], [227, 1100]], [[962, 1051], [1003, 1051], [1003, 1098], [962, 1098]], [[209, 1117], [282, 1117], [282, 1174], [209, 1174]], [[952, 1116], [1016, 1116], [1016, 1160], [952, 1160]], [[179, 1182], [308, 1182], [308, 1233], [179, 1233]], [[971, 1191], [1008, 1191], [1008, 1214], [971, 1214]], [[221, 1244], [268, 1251], [261, 1301], [214, 1295]], [[464, 1249], [505, 1249], [505, 1296], [464, 1296]], [[709, 1244], [755, 1244], [755, 1296], [709, 1296]], [[961, 1252], [1001, 1252], [1001, 1300], [961, 1300]], [[209, 1317], [275, 1317], [275, 1361], [209, 1361]], [[453, 1313], [517, 1318], [513, 1362], [449, 1357]], [[697, 1312], [764, 1312], [764, 1361], [697, 1361]], [[950, 1319], [1008, 1319], [1008, 1356], [950, 1356]], [[177, 1379], [307, 1379], [307, 1430], [177, 1430]], [[420, 1381], [546, 1381], [546, 1426], [420, 1426]], [[688, 1373], [765, 1367], [771, 1436], [693, 1442]], [[948, 1382], [1010, 1382], [1010, 1424], [948, 1424]], [[596, 1633], [631, 1633], [631, 1670], [596, 1670]]], "rec_boxes": [[184, 0, 455, 53], [184, 36, 460, 95], [398, 250, 786, 302], [408, 360, 787, 396], [406, 452, 789, 498], [408, 554, 789, 595], [223, 658, 264, 705], [464, 654, 510, 705], [709, 656, 757, 705], [962, 656, 1001, 700], [211, 723, 278, 772], [455, 719, 521, 770], [700, 719, 767, 768], [918, 719, 1046, 765], [181, 789, 310, 840], [424, 786, 548, 831], [698, 781, 769, 838], [227, 860, 268, 905], [564, 853, 654, 898], [962, 854, 1003, 902], [216, 924, 280, 968], [950, 917, 1019, 968], [181, 984, 310, 1035], [950, 982, 1016, 1035], [227, 1056, 266, 1100], [962, 1051, 1003, 1098], [209, 1117, 282, 1174], [952, 1116, 1016, 1160], [179, 1182, 308, 1233], [971, 1191, 1008, 1214], [214, 1244, 268, 1301], [464, 1249, 505, 1296], [709, 1244, 755, 1296], [961, 1252, 1001, 1300], [209, 1317, 275, 1361], [449, 1313, 517, 1362], [697, 1312, 764, 1361], [950, 1319, 1008, 1356], [177, 1379, 307, 1430], [420, 1381, 546, 1426], [688, 1367, 771, 1442], [948, 1382, 1010, 1424], [596, 1633, 631, 1670]]}