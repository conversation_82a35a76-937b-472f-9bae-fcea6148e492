{"input_path": "../data/中州派紫微斗数初级讲义_s.pdf", "page_index": 1, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 180}, "dt_polys": [[[1288, 2], [1309, 2], [1309, 27], [1288, 27]], [[23, 23], [777, 39], [777, 77], [23, 60]], [[1408, 45], [1641, 39], [1642, 72], [1409, 78]], [[1626, 43], [1682, 43], [1682, 71], [1626, 71]], [[18, 55], [734, 71], [733, 110], [17, 94]], [[924, 73], [1680, 71], [1681, 108], [924, 110]], [[937, 105], [1680, 101], [1681, 140], [937, 144]], [[370, 115], [439, 115], [439, 149], [370, 149]], [[919, 137], [1680, 133], [1681, 172], [919, 176]], [[300, 172], [504, 176], [503, 215], [300, 211]], [[928, 170], [1677, 170], [1677, 202], [928, 202]], [[933, 198], [1672, 200], [1672, 232], [933, 230]], [[919, 229], [1672, 229], [1672, 261], [919, 261]], [[919, 259], [1672, 261], [1672, 292], [919, 291]], [[930, 289], [1672, 291], [1672, 324], [930, 323]], [[118, 314], [767, 317], [766, 351], [117, 347]], [[914, 317], [1624, 321], [1624, 358], [914, 354]], [[9, 342], [768, 349], [768, 386], [9, 379]], [[1137, 354], [1670, 354], [1670, 386], [1137, 386]], [[25, 372], [725, 378], [724, 415], [24, 409]], [[918, 383], [1675, 386], [1675, 418], [917, 415]], [[400, 415], [760, 415], [760, 441], [400, 441]], [[917, 415], [1628, 417], [1628, 445], [917, 443]], [[0, 440], [761, 441], [761, 475], [0, 473]], [[1504, 443], [1677, 447], [1677, 479], [1503, 475]], [[0, 470], [717, 471], [717, 505], [0, 503]], [[914, 475], [1679, 475], [1679, 512], [914, 512]], [[226, 500], [760, 503], [759, 541], [226, 537]], [[916, 509], [1675, 509], [1675, 541], [916, 541]], [[0, 530], [716, 535], [716, 574], [0, 569]], [[914, 541], [1670, 541], [1670, 567], [914, 567]], [[393, 572], [754, 572], [754, 599], [393, 599]], [[916, 571], [1633, 571], [1633, 603], [916, 603]], [[2, 601], [754, 601], [754, 633], [2, 633]], [[1007, 601], [1677, 601], [1677, 638], [1007, 638]], [[0, 629], [754, 629], [754, 666], [0, 666]], [[910, 634], [1631, 634], [1631, 666], [910, 666]], [[0, 663], [753, 665], [752, 697], [0, 695]], [[1154, 666], [1677, 666], [1677, 698], [1154, 698]], [[0, 693], [709, 697], [709, 728], [0, 725]], [[907, 698], [1677, 698], [1677, 730], [907, 730]], [[0, 727], [751, 727], [751, 759], [0, 759]], [[909, 728], [1630, 728], [1630, 760], [909, 760]], [[2, 759], [751, 759], [751, 790], [2, 790]], [[1212, 757], [1675, 760], [1675, 794], [1212, 790]], [[0, 789], [749, 790], [749, 824], [0, 822]], [[919, 790], [1677, 792], [1677, 826], [919, 824]], [[0, 824], [749, 822], [749, 854], [0, 856]], [[912, 822], [1631, 824], [1631, 856], [912, 854]], [[0, 853], [710, 851], [710, 888], [0, 890]], [[1361, 854], [1673, 854], [1673, 886], [1361, 886]], [[905, 879], [1674, 886], [1673, 924], [905, 916]], [[109, 890], [747, 890], [747, 916], [109, 916]], [[0, 918], [749, 916], [749, 953], [0, 955]], [[904, 913], [1674, 916], [1673, 955], [903, 952]], [[0, 950], [747, 946], [747, 984], [0, 987]], [[917, 948], [1670, 950], [1670, 984], [917, 982]], [[0, 984], [747, 980], [747, 1012], [0, 1016]], [[902, 978], [1628, 982], [1628, 1016], [902, 1012]], [[0, 1014], [749, 1010], [749, 1047], [0, 1051]], [[1156, 1010], [1672, 1014], [1672, 1048], [1156, 1044]], [[10, 1048], [707, 1044], [707, 1081], [11, 1085]], [[907, 1040], [1675, 1042], [1675, 1081], [907, 1079]], [[149, 1081], [745, 1079], [746, 1108], [149, 1110]], [[909, 1078], [1677, 1079], [1677, 1111], [909, 1109]], [[0, 1110], [751, 1102], [751, 1145], [0, 1152]], [[917, 1109], [1675, 1109], [1675, 1141], [917, 1141]], [[0, 1140], [712, 1131], [712, 1175], [0, 1184]], [[900, 1136], [1633, 1136], [1633, 1173], [900, 1173]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "box_thresh": 0.6, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "text_rec_score_thresh": 0.0, "rec_texts": ["3", "", "由是判断财帛积聚与否。", "径，日", "学习紫微斗数，其实应该对起例(安星法)有深刻的认识，然后在飞劝星盘推算", "但斗数却远可以推断所获者是什么性质的财，可以看出得财之年还有什么用钱的途", "的细节推断方面，斗数远比[子平]更胜一筹。举例来说，[子平]可以算出财运好，", "前言", "使已发展成熟，变成一个足以与[子平]抗衡的推算禄命体系。甚至可以说，在运势", "二、入门及起例", "每月、每日，甚至每个时辰的天干而产生质的变化。由南宋启蒙，经元代，至明初，", "[四化曜]建立于一非常灵活的程式之上，于是使整个星盘能随着每个大限、每年、", "系八十二宫的性质时，将会详细论及)，再加上将星曜系列重组，并且扩大，特别是", "同时每一个[三六四正]都是一个关系密切的有机组合(关于这点，我们在讨论到诸星", "新订定宫位名称，如取消相貌宫之名，而正名为父母宫，这就使星盘申六亲齐备，", "的基础。倘若认为这些知识已无发展的余地，那便反而是固执与迷信了。", "紫微斗数的创立，是对[十八飞星]的重大革新。它改变了十二宫排列顺序，重", "认为谈论紫微斗数即是宣扬迷信。另一目的，则是想将已知的知识成为后来者发展", "原则以及呆板的星盘结构，来解释社会的现象与人际关系。", "笔者公开自己的知识与心得，有两个目的，一个是纠正尸般[伪科学]的看法，", "结构，这样就令到统计与原则脱节。即是说，无论怎样修正原则，总难应用有限的", "者相信，紫微斗数的准确性便应该更高。", "第二，[十八飞星]分布宫位的程式，规定得欠灵动性，使星盘成为一个呆板的", "是秘密。倘若能发展到用电脑来帮助统计，同时有更多人提供多方面的资料，笔", "说明彼此的关系。", "正由于这样，．笔者也就无须藏私，不将自己的知识与心得公开。因为这根本不", "分重视了家族的关系；至于兄弟宫与迁移宫相对，与妻妾宫及福德宫相会，则更难", "任何人都可以根据统计与实践，补充甚至修正笔者的说法。", "构便非常之不合理。例如命宫与妻妾宫相对，与子女宫及迁移宫在三方相会，即过", "所以各位不必将笔者的观点视为金科玉律，只能视之为入门的指导，入门之后，", "子女、奴仆、妻妾、疾厄、迁移、官禄、福德、相貌。这样一来，[三方四正]的结", "样才能保持紫微斗数的灵活性与准确度。", "第一[十八飞星]的十二宫沿袭[五星]，其排列次序为命宫、财帛、兄弟、田宅，", "够全面，甚至可以说，只要目前的社会文化改变，笔者的意见便亦应随之改变，", "推算法，但有两个局限性，使这门术数不得不起革命。这两个局限性是：", "修订如关于血癌、鼻咽癌的预示，即为师传所无。但可以说，这些修订一定仍然", "[十八飞星]的创立非常之粗糙，虽然后来在实践申创立了兼视[三方四正]的", "读者的，仅属于中州派所传的星系基本性质，以及各种变化，加上笔者统计发展", "就有如[子平]的[江湖派]一样，不得不重视实践与统计。", "学习紫微斗数，亦一定要统计、发展，然后才可以适应时代，笔者所能提供", "术便缺乏理论，全 凭实践，并且在实践中不断根据统计来修正自己的推断法则。这", "至少有百分之六十以上婚姻美满，而家庭主妇则有百分之八十以上婚姻出现问题", "由于除紫微之外，十八颗飞星均属虚构出来的符号，所以[十八飞星]这门算命", "此推断便应改变。笔者统计过近百个武曲星系坐守夫妻宫的女命，只要其人有职业", "有些袭取择日家的“丛辰”，有些则是假借星名。", "那是由于古代妇女没有生财的能力，所以便不将武曲看成是财星之故，现代社会", "天福、天禄，天杖、天异、头、天刃、天姚、天刑、天哭。它们有些源[五星]，", "景不同，星系性质亦应随之变异。例如古人认为[武曲之星为寡星]，不利妇女婚", "这十八颗虚星是：天虚、天贵、天印、天寿、天空、红鸾、天库、天贯、文昌、", "亦正由于星系的基本性质由统计而来，所以可塑性便相当大。由于社会文化", "套安星的程式，并用之推断禄命。", "数]。而初具规模的[十八飞星]，则是以紫微为主星，然后设立十八颗虚星，制订一", "计与推理。我们只可以说，这门术数带点目前科学所未能解释的神秘。", "才能达到所谓趋吉避凶的目的。研究紫微斗数，绝对不是迷信，因为它筑基于", "所以由北极星触发而成的术，既有推 算地运的[太乙数]，亦有推算人命的[紫微斗", "如果加以容忍，又会出现如何如何的变化。这样就可以容许后天人事的安排，因", "日天文学的观点来看，这些称谓都不正确，但在宋元之际，这些俗称却一直流行。", "某年夫妻感情有变，原因是什么，可能发展到离婚，离婚之后，其演变会怎么林", "北极星的名称在通俗称谓上很混淆，有称之为紫微、有称之为太乙，虽然以今", "年会发生一些什么性质的事。以婚姻而言，中州派不会说某年你一定离婚，只会议", "等于绕着北极星旋转，每转一个圈，大致上即是一年。", "所以中州派从来不承认宿命，决不会说，某年你一定会怎样怎样，只会说，", "并极星恒常不动，只有斗柄随着春、夏、秋、冬四季而指向东、南、西、北的方位，", "便可以据此推理，判断将会有什么事件发生，从而定出趋避之方。", "这样做的好处，是因为北极星对地球 的相对运动为零，亦即对地面的观察方来说，", "本性质，再加上辅、佐、煞、化诸曜的会合，使基本性质加强、削弱或改变，然", "原始构思，是针对[五星]宫度不准的缺点，选择北极星作为主星，以此计算坐标。", "这种细节的推断，基础在于统计，由大量统计定出各种星系组合在十二宫的", "紫微斗数前身[十八飞星]的兴起，则是另一种以实验为主的推算禄命术。它的"], "rec_scores": [0.9992934465408325, 0.0, 0.9998735189437866, 0.972461462020874, 0.9990301728248596, 0.9994906187057495, 0.986709475517273, 0.999139666557312, 0.9955848455429077, 0.9975876808166504, 0.9960832595825195, 0.984237551689148, 0.9989180564880371, 0.9860098958015442, 0.9969097971916199, 0.9950963258743286, 0.9966477751731873, 0.9947085976600647, 0.9995687007904053, 0.9892796277999878, 0.9934774041175842, 0.9996979832649231, 0.9816234111785889, 0.9978318214416504, 0.9998206496238708, 0.9886131286621094, 0.995818018913269, 0.9994043111801147, 0.9978735446929932, 0.9981991648674011, 0.9900625348091125, 0.9993337988853455, 0.9868803024291992, 0.9995853900909424, 0.9960386753082275, 0.999720573425293, 0.9953005313873291, 0.9994310736656189, 0.9975840449333191, 0.9996562004089355, 0.9795110821723938, 0.9994385838508606, 0.9983860850334167, 0.985010027885437, 0.9988808035850525, 0.9992419481277466, 0.9850563406944275, 0.9964850544929504, 0.9981847405433655, 0.9986413717269897, 0.998484194278717, 0.9894649982452393, 0.9994997978210449, 0.9969527125358582, 0.9863964915275574, 0.9995330572128296, 0.9994878768920898, 0.9817386269569397, 0.9990189671516418, 0.9957410097122192, 0.9994416236877441, 0.9984852075576782, 0.9920859336853027, 0.999269962310791, 0.9800156354904175, 0.9984841346740723, 0.9975447654724121, 0.9989235401153564, 0.9945472478866577], "rec_polys": [[[1288, 2], [1309, 2], [1309, 27], [1288, 27]], [[23, 23], [777, 39], [777, 77], [23, 60]], [[1408, 45], [1641, 39], [1642, 72], [1409, 78]], [[1626, 43], [1682, 43], [1682, 71], [1626, 71]], [[18, 55], [734, 71], [733, 110], [17, 94]], [[924, 73], [1680, 71], [1681, 108], [924, 110]], [[937, 105], [1680, 101], [1681, 140], [937, 144]], [[370, 115], [439, 115], [439, 149], [370, 149]], [[919, 137], [1680, 133], [1681, 172], [919, 176]], [[300, 172], [504, 176], [503, 215], [300, 211]], [[928, 170], [1677, 170], [1677, 202], [928, 202]], [[933, 198], [1672, 200], [1672, 232], [933, 230]], [[919, 229], [1672, 229], [1672, 261], [919, 261]], [[919, 259], [1672, 261], [1672, 292], [919, 291]], [[930, 289], [1672, 291], [1672, 324], [930, 323]], [[118, 314], [767, 317], [766, 351], [117, 347]], [[914, 317], [1624, 321], [1624, 358], [914, 354]], [[9, 342], [768, 349], [768, 386], [9, 379]], [[1137, 354], [1670, 354], [1670, 386], [1137, 386]], [[25, 372], [725, 378], [724, 415], [24, 409]], [[918, 383], [1675, 386], [1675, 418], [917, 415]], [[400, 415], [760, 415], [760, 441], [400, 441]], [[917, 415], [1628, 417], [1628, 445], [917, 443]], [[0, 440], [761, 441], [761, 475], [0, 473]], [[1504, 443], [1677, 447], [1677, 479], [1503, 475]], [[0, 470], [717, 471], [717, 505], [0, 503]], [[914, 475], [1679, 475], [1679, 512], [914, 512]], [[226, 500], [760, 503], [759, 541], [226, 537]], [[916, 509], [1675, 509], [1675, 541], [916, 541]], [[0, 530], [716, 535], [716, 574], [0, 569]], [[914, 541], [1670, 541], [1670, 567], [914, 567]], [[393, 572], [754, 572], [754, 599], [393, 599]], [[916, 571], [1633, 571], [1633, 603], [916, 603]], [[2, 601], [754, 601], [754, 633], [2, 633]], [[1007, 601], [1677, 601], [1677, 638], [1007, 638]], [[0, 629], [754, 629], [754, 666], [0, 666]], [[910, 634], [1631, 634], [1631, 666], [910, 666]], [[0, 663], [753, 665], [752, 697], [0, 695]], [[1154, 666], [1677, 666], [1677, 698], [1154, 698]], [[0, 693], [709, 697], [709, 728], [0, 725]], [[907, 698], [1677, 698], [1677, 730], [907, 730]], [[0, 727], [751, 727], [751, 759], [0, 759]], [[909, 728], [1630, 728], [1630, 760], [909, 760]], [[2, 759], [751, 759], [751, 790], [2, 790]], [[1212, 757], [1675, 760], [1675, 794], [1212, 790]], [[0, 789], [749, 790], [749, 824], [0, 822]], [[919, 790], [1677, 792], [1677, 826], [919, 824]], [[0, 824], [749, 822], [749, 854], [0, 856]], [[912, 822], [1631, 824], [1631, 856], [912, 854]], [[0, 853], [710, 851], [710, 888], [0, 890]], [[1361, 854], [1673, 854], [1673, 886], [1361, 886]], [[905, 879], [1674, 886], [1673, 924], [905, 916]], [[109, 890], [747, 890], [747, 916], [109, 916]], [[0, 918], [749, 916], [749, 953], [0, 955]], [[904, 913], [1674, 916], [1673, 955], [903, 952]], [[0, 950], [747, 946], [747, 984], [0, 987]], [[917, 948], [1670, 950], [1670, 984], [917, 982]], [[0, 984], [747, 980], [747, 1012], [0, 1016]], [[902, 978], [1628, 982], [1628, 1016], [902, 1012]], [[0, 1014], [749, 1010], [749, 1047], [0, 1051]], [[1156, 1010], [1672, 1014], [1672, 1048], [1156, 1044]], [[10, 1048], [707, 1044], [707, 1081], [11, 1085]], [[907, 1040], [1675, 1042], [1675, 1081], [907, 1079]], [[149, 1081], [745, 1079], [746, 1108], [149, 1110]], [[909, 1078], [1677, 1079], [1677, 1111], [909, 1109]], [[0, 1110], [751, 1102], [751, 1145], [0, 1152]], [[917, 1109], [1675, 1109], [1675, 1141], [917, 1141]], [[0, 1140], [712, 1131], [712, 1175], [0, 1184]], [[900, 1136], [1633, 1136], [1633, 1173], [900, 1173]]], "rec_boxes": [[1288, 2, 1309, 27], [23, 23, 777, 77], [1408, 39, 1642, 78], [1626, 43, 1682, 71], [17, 55, 734, 110], [924, 71, 1681, 110], [937, 101, 1681, 144], [370, 115, 439, 149], [919, 133, 1681, 176], [300, 172, 504, 215], [928, 170, 1677, 202], [933, 198, 1672, 232], [919, 229, 1672, 261], [919, 259, 1672, 292], [930, 289, 1672, 324], [117, 314, 767, 351], [914, 317, 1624, 358], [9, 342, 768, 386], [1137, 354, 1670, 386], [24, 372, 725, 415], [917, 383, 1675, 418], [400, 415, 760, 441], [917, 415, 1628, 445], [0, 440, 761, 475], [1503, 443, 1677, 479], [0, 470, 717, 505], [914, 475, 1679, 512], [226, 500, 760, 541], [916, 509, 1675, 541], [0, 530, 716, 574], [914, 541, 1670, 567], [393, 572, 754, 599], [916, 571, 1633, 603], [2, 601, 754, 633], [1007, 601, 1677, 638], [0, 629, 754, 666], [910, 634, 1631, 666], [0, 663, 753, 697], [1154, 666, 1677, 698], [0, 693, 709, 728], [907, 698, 1677, 730], [0, 727, 751, 759], [909, 728, 1630, 760], [2, 759, 751, 790], [1212, 757, 1675, 794], [0, 789, 749, 824], [919, 790, 1677, 826], [0, 822, 749, 856], [912, 822, 1631, 856], [0, 851, 710, 890], [1361, 854, 1673, 886], [905, 879, 1674, 924], [109, 890, 747, 916], [0, 916, 749, 955], [903, 913, 1674, 955], [0, 946, 747, 987], [917, 948, 1670, 984], [0, 980, 747, 1016], [902, 978, 1628, 1016], [0, 1010, 749, 1051], [1156, 1010, 1672, 1048], [10, 1044, 707, 1085], [907, 1040, 1675, 1081], [149, 1079, 746, 1110], [909, 1078, 1677, 1111], [0, 1102, 751, 1152], [917, 1109, 1675, 1141], [0, 1131, 712, 1184], [900, 1136, 1633, 1173]]}