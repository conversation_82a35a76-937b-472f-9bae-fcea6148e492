import pymupdf  as pd # PyMuPDF
import json
import os
from pathlib import Path
from pdf2image import convert_from_path
#import layoutparser as lp
# pdf文件转成json格式的内容

def dump_pages(input_pdf, output_pdf, start_page_num=0, end_page_num=10000):
    """ 导出指定页数为新的pdf文件

    Args:
        input_pdf (_type_): 要选择页数的原始pdf
        output_pdf (_type_): 保存的新pdf
        start_page_num (int, optional): 起始页 Defaults to 0.
        end_page_num (int, optional): 结束页. Defaults to 10000.
    """
    
    doc = pd.open(input_pdf)
    select_pages = [x for x in range(start_page_num, end_page_num)]
    doc.select(select_pages)
    doc.save(output_pdf)


def pdf_to_json(pdf_path, output_json, max_page_num=10000, images_dir=None):
    """解析PDF为JSON格式，并保存图片

    Args:
        pdf_path (str): PDF文件路径
        output_json (str): 输出JSON文件路径
        max_page_num (int, optional): 最大处理页数. Defaults to 10000.
        images_dir (str, optional): 图片保存目录，为None时不保存图片. Defaults to None.
    """
    doc = pd.open(pdf_path)
    
    # 处理元数据
    metadata = {}
    if doc.metadata:
        for key in ["title", "author", "subject", "keywords", "creator", "producer"]:
            if key in doc.metadata and doc.metadata[key]:
                metadata[key] = doc.metadata[key]
    
    book_data = {
        "metadata": metadata,
        "chapters": [],
        "images": []
    }


#    model = lp.models.Detectron2LayoutModel(
#        config_path="lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config",
#        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8]
#    )

    # 创建图片保存目录
    if images_dir:
        os.makedirs(images_dir, exist_ok=True)

    end_page_num = min(len(doc), max_page_num)
    for page_num in range(end_page_num):
        page = doc[page_num]
        text = page.get_text("dict")

        # 结构分析
        # image = convert_from_path(
        #    pdf_path, first_page=page_num+1, last_page=page_num+1)[0]
        #layout = model.detect(image)

        chapter = {
            "page": page_num + 1,
            "blocks": [],
            "images": []
        }

        # 处理文本块
        for block in text["blocks"]:
            if block["type"] == 0:  # 文本块
                chapter["blocks"].append({
                    "type": "text",
                    "content": "".join([line["spans"][0]["text"] if line["spans"] else "" for line in block["lines"]]),
                    "bbox": block["bbox"]
                })

        # 提取图片
        img_list = page.get_images(full=True)
        for img_index, img in enumerate(img_list):
            xref = img[0]
            base_image = doc.extract_image(xref)
            
            # 获取图片在页面上的位置
            rect = page.get_image_bbox(img)
            
            # 图片ID
            img_id = f"img_p{page_num+1}_{img_index+1}"
            
            # 构建图片数据
            image_data = {
                "id": img_id,
                "format": base_image["ext"],
                "bbox": [rect.x0, rect.y0, rect.x1, rect.y1],
                "page": page_num + 1
            }
            
            # 保存图片到本地
            if images_dir:
                img_path = os.path.join(images_dir, f"{img_id}.{base_image['ext']}")
                with open(img_path, "wb") as f:
                    f.write(base_image["image"])
                image_data["file_path"] = img_path
            
            chapter["images"].append(image_data)
            book_data["images"].append(image_data)

        book_data["chapters"].append(chapter)

    with open(output_json, "w", encoding="utf-8") as f:
        json.dump(book_data, f, ensure_ascii=False, indent=2)
    
    print(f"已处理 {end_page_num} 页PDF，输出到: {output_json}")
    if images_dir:
        print(f"图片已保存到目录: {images_dir}")

# 使用示例
if __name__ == "__main__":
    #pdf_file = r'data/紫微斗数全书_s.pdf'
    #output_file = r'data/紫微斗数全书_s.json'
    pdf_file = r'data/中州派紫微斗数初级讲义.pdf'
    output_file = r'data/中州派紫微斗数初级讲义_3.pdf'
    images_dir = r'data/紫微斗数/images'
    #pdf_to_json(pdf_file, output_file, 10, images_dir)
    #dump_pages(pdf_file, output_file, start_page_num=2, end_page_num=3)
    dump_pages(pdf_file, output_file, start_page_num=3, end_page_num=4)
