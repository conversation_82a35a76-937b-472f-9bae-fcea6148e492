import os, pymupdf
from natsort import natsorted
#import PySimpleGUI as psg  # for showing a progress bar
doc = pymupdf.open()  # PDF with the pictures
imgdir = "/Users/<USER>/Project/auto_screenshot/screenshot"  # where the pics are

def list_naturally_sorted_files(directory: str):
    # 获取目录中的文件列表
    files = os.listdir(directory)
    # 使用natsorted进行自然排序
    naturally_sorted_files = natsorted(files)
    return naturally_sorted_files

imglist = list_naturally_sorted_files(imgdir)  # list of them
imgcount = len(imglist)  # pic count

for i, f in enumerate(imglist):
    img = pymupdf.open(os.path.join(imgdir, f))  # open pic as document
    rect = img[0].rect  # pic dimension
    pdfbytes = img.convert_to_pdf()  # make a PDF stream
    img.close()  # no longer needed
    imgPDF = pymupdf.open("pdf", pdfbytes)  # open stream as PDF
    page = doc.new_page(width = rect.width,  # new page with ...
                       height = rect.height)  # pic dimension
    page.show_pdf_page(rect, imgPDF, 0)  # image fills the page
    print("Import Images",  i+1, imgcount)

doc.save("screenshot.pdf")