import pymupdf
from pathlib  import Path

src = pymupdf.open("/Users/<USER>/Project/fate_explorer/data/test_data/南北山人_紫微斗数全书_s.pdf")
doc = pymupdf.open()  # empty output PDF

i = 0
for spage in src:  # for each page in input
    r = spage.rect  # input page rectangle
    d = pymupdf.Rect(spage.cropbox_position,  # CropBox displacement if not
                  spage.cropbox_position)  # starting at (0, 0)
    #--------------------------------------------------------------------------
    # example: cut input page into 2 x 2 parts
    #--------------------------------------------------------------------------
    r1 = r / 2  # top left rect
    r2 = r1 + (r1.width, 0, r1.width, 0)  # top right rect
    r3 = r1 + (0, r1.height, 0, r1.height)  # bottom left rect
    r4 = pymupdf.Rect(r1.br, r.br)  # bottom right rect
    rect_list = [r1, r2, r3, r4]  # put them in a list

    for rx in rect_list:  # run thru rect list
        rx += d  # add the CropBox displacement
        page = doc.new_page(-1,  # new output page with rx dimensions
                           width = rx.width,
                           height = rx.height)
        page.show_pdf_page(
                page.rect,  # fill all new page with the image
                src,  # input document
                spage.number,  # input page number
                clip = rx,  # which part to use of input page
            )
    i += 1
    if (i == 2):
        break        

# that's it, save output file
path = Path(src.name)
output_file = path.stem + "-split" + path.suffix
doc.save(output_file,
         garbage=3,  # eliminate duplicate objects
         deflate=True,  # compress stuff where possible
)