import pymupdf
import os
from pathlib import Path

data_path = os.path.join(os.path.dirname(__file__), '../../data/test_data')
input_file = os.path.join(data_path, '中州派紫微斗数初级讲义_5.pdf')

src = pymupdf.open(input_file)
doc = pymupdf.open()  # empty output PDF

for spage in src:  # for each page in input
    rotation = spage.rotation  # 获取页面旋转角度
    r = spage.rect  # input page rectangle

    print(f"原始页面信息: 宽={r.width}, 高={r.height}, 旋转角度={rotation}")

    # 如果页面旋转了90度或270度，需要交换宽高
    if rotation in (90, 270):
        actual_width = r.height
        actual_height = r.width
    else:
        actual_width = r.width
        actual_height = r.height

    print(f"实际页面尺寸: 宽={actual_width}, 高={actual_height}")

    # 创建左右两个裁剪区域（考虑旋转后的实际尺寸）
    if rotation == 90:
        # 对于90度旋转的页面，左右分割实际上是bottom和top
        left_rect = pymupdf.Rect(r.y0, r.x0 + r.width/2, r.y1, r.x1)
        right_rect = pymupdf.Rect(r.y0, r.x0, r.y1, r.x0 + r.width/2)
    else:
        # 正常的左右分割
        left_rect = pymupdf.Rect(r.x0, r.y0, r.x0 + r.width/2, r.y1)
        right_rect = pymupdf.Rect(r.x0 + r.width/2, r.y0, r.x1, r.y1)

    rect_list = [left_rect, right_rect]

    for i, rx in enumerate(rect_list):
        page = doc.new_page(-1,
                            width=rx.width,
                            height=rx.height)

        # 设置正确的旋转角度
        page.set_rotation(rotation)  # 重置旋转角度为0

        print(f"新页面 {i+1} 尺寸: 宽={page.rect.width}, 高={page.rect.height}")

        # 显示PDF页面时考虑旋转
        page.show_pdf_page(
            page.rect,  # 目标矩形（新页面的完整区域）
            src,        # 源文档
            spage.number,  # 页码
            clip=rx,    # 裁剪区域
        )

path = Path(src.name)
output_file = path.stem + "-split" + path.suffix
print(f"保存到: {output_file}")
doc.save(output_file,
         garbage=3,
         deflate=True)
