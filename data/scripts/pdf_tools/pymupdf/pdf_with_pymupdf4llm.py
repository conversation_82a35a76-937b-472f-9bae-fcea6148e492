# convert the document to markdown
import pymupdf4llm
import pathlib
import os

data_dir = '/Users/<USER>/Project/fate_explorer/data/books/'
file_name = '紫微斗数全书微信版.pdf'
source_file = data_dir + file_name
output_file = data_dir + file_name.split('.')[0] + '_pymupdf.md'
img_path = data_dir + r'img/'
md_text = pymupdf4llm.to_markdown(source_file, write_images=True, image_path=img_path, pages=[i for i in range(2)])
# Write the text to some file in UTF8-encoding
pathlib.Path(output_file).write_bytes(md_text.encode())