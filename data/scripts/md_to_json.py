import re
import os
from typing import Dict, List, Any, Optional
import json

class MarkdownToJsonConverter:
    def __init__(self):
        self.result: Dict[str, Any] = {"content": []}
        # 章节
        self.chapters: List[Dict[str, Any]] = self.result["content"]
        # 当前章节
        self.current_chapter = [Dict[str, Any]]
        # 当前段落
        self.current_paragraph: Optional[Dict[str, Any]] = None

        self.input_file = ""

    def read_file(self, md_file: str) -> str:
        """读取输入文件"""
        self.input_file = md_file
        with open(md_file, 'r', encoding='utf-8') as f:
            return f.read()

    def process_header(self, line: str) -> bool:
        """处理标题行"""
        header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
        if not header_match:
            return False

        level = len(header_match.group(1))
        title = header_match.group(2).strip()
        
        # 创建新的章节对象
        new_chapter = {
            "title": title,
            "level": level,
            "content": [],  # txt, img, quote
            "subchapters": []
        }
        
        """添加章节到层级结构中"""
        parent = None
        for i in range(level-1):
            if parent is None:
                parent = self.chapters[-1]
            else:
                parent = parent['subchapters'][-1]
        if parent is None:
            self.chapters.append(new_chapter)
        else:
            parent['subchapters'].append(new_chapter)        
        self.current_chapter = new_chapter 
        self.current_paragraph = None
        return True

    def process_image(self, line: str) -> bool:
        """处理图片行"""
        image_match = re.match(r'!\[(.*?)\]\((.*?)\)', line)
        if not image_match:
            return False

        alt_text = image_match.group(1)
        url = image_match.group(2)
        image_obj = {"type": "image", "alt": alt_text, "url": url}
        self.current_chapter["content"].append(image_obj)
        return True

    def process_quote(self, line: str) -> bool:
        """处理引用行"""
        quote_match = re.match(r'>\s+(.+)', line)
        if not quote_match:
            return False

        quote_text = quote_match.group(1).strip()
        if self.current_paragraph:
            self.current_paragraph["quote"] = quote_text
        else:
            paragraph = {"type": "paragraph", "text": "", "quote": quote_text}
            self.current_chapter["content"].append(paragraph)
            self.current_paragraph = None
        return True

    def process_paragraph(self, lines: List[str], start_index: int) -> int:
        """处理段落，返回处理后的索引位置"""
        paragraph_lines = []
        i = start_index
        
        while i < len(lines):
            line = lines[i].strip()
            
            # 如果遇到空行，说明段落结束
            if not line:
                break
                
            # 如果遇到特殊标记，说明段落结束
            if (line.startswith('#') or 
                line.startswith('!') or 
                line.startswith('>')):
                break
                
            paragraph_lines.append(line)
            i += 1
            
        if paragraph_lines:
            paragraph_text = ' '.join(paragraph_lines)
            paragraph = {"type": "paragraph", "text": paragraph_text}
            self.current_chapter["content"].append(paragraph)
            self.current_paragraph = paragraph
            
        return i - 1  # 返回最后处理的位置

    def convert_file(self, markdown_file: str) -> str:
        """读入Markdown文件，转为Json结构字符串返回"""
        content = self.read_file(markdown_file)
        return self.convert(content)

    def convert(self, markdown_text: str) -> Dict[str, Any]:
        """将Markdown文本转换为JSON结构"""
        lines = markdown_text.strip().split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            # 跳过空行
            if not line:
                i += 1
                continue
            
            # 按优先级处理不同类型的内容
            if self.process_header(line):
                i += 1
                continue
                
            if self.process_image(line):
                i += 1
                continue
                
            if self.process_quote(line):
                i += 1
                continue
                
            # 处理普通段落
            i = self.process_paragraph(lines, i) + 1
            
        return self.result

    def to_json(self) -> str:
        """将处理后的内容转换为JSON格式"""
        book_data = {
            "title": os.path.basename(self.input_file).replace('.md', ''),
            "chapters": self.chapters
        }
        return json.dumps(book_data, ensure_ascii=False, indent=2)

def markdown_to_json(md_file: str) -> Dict[str, Any]:
    """便捷函数，用于直接转换Markdown文本"""
    converter = MarkdownToJsonConverter()
    converter.convert_file(md_file) 
    return converter.to_json()

if __name__ == '__main__':
    data_dir = 'data/books/紫微斗数全书/pub/'
    md_file = data_dir + '紫微斗数整理版_卷1.md'
    json_data = markdown_to_json(md_file)
    # 保存JSON输出
    output_file = md_file.replace('.md', '.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_data)

    print(f"转换完成！输出文件：{output_file}")

