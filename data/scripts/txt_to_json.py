import json
import re
from typing import List, Dict, Any
import os


class TxtToJsonConverter:
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.chapters: List[Dict[str, Any]] = []
        self.content = ""

    def read_file(self) -> None:
        """读取输入文件"""
        with open(self.input_file, 'r', encoding='utf-8') as f:
            self.content = f.read()

    def get_chapter_no(self, title: str) -> str:
        """获取章节的层级"""
        # 匹配数字格式的标题，如1.1.2或1.4
        match = re.match(r'^(\d+(\.\d+)*)', title)
        if match:
            return match.group(1)
        return 0

    def add_chapter(self, chapter_no: str,  title: str) -> Dict[str, Any]:
        """添加章节到层级结构中"""
        chapter = {
            "chapter_no": chapter_no,
            "title": title.strip(),
            "content": "",
            "groups": [],
            "subchapters": []
        }
        chapter_levels = chapter_no.split('.')
        parent = None
        for i in range(len(chapter_levels)-1):
            cur_no = int(chapter_levels[i])
            if parent is None:
                parent = self.chapters[cur_no-1]
            else:
                parent = parent['subchapters'][cur_no-1]
        if parent is None:
            self.chapters.append(chapter)
        else:
            parent['subchapters'].append(chapter)        


    def extract_chapters(self) -> None:
        """从文件开头提取多级章节列表"""
        # 匹配数字格式的标题
        chapter_pattern = r'^(\d+(\.\d+)*)\s+.*$'
        lines = self.content.split('\n')

        is_chapter_begin = False
        for line in lines:
            line = line.strip()
            if not line:
                continue

            match = re.match(chapter_pattern, line)
            if match:
                is_chapter_begin = True
                line_content_lst = line.split('\t')
                title = line_content_lst[-1]
                chapter_no = self.get_chapter_no(line_content_lst[0])
                # 把标题添加到章节内容里去，建立起章节结构
                self.add_chapter(chapter_no, title)
            else:
                if line.replace(' ', '') == '':
                    continue
                elif is_chapter_begin:
                    # 章节结束
                    break

    def process_tables(self, text: str) -> str:
        """处理表格内容，用<table_content>替换"""
        # 处理以'----'开头的表格
        text = re.sub(r'----[\s\S]*?----', '<table_content>', text)
        # 处理以'|'开头的表格
        text = re.sub(r'\|[\s\S]*?\|', '<table_content>', text)
        return text

    def process_groups(self, content: str) -> List[str]:
        """将内容分割成groups，每个group由两个以上空行分隔"""
        # 使用两个或更多空行作为分隔符
        groups = re.split(r'\n\s*\n\s*\n+', content)
        # 清理每个group中的空行
        return [re.sub(r'\n\s*\n', '\n', group.strip()) for group in groups if group.strip()]

    def process_content(self) -> None:
        """处理文件内容，将内容分配到对应章节"""
        def process_chapter(chapter: Dict[str, Any]) -> None:
            current_content = []
            lines = self.content.split('\n')
            in_chapter = False

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是当前章节的标题
                if line == chapter["title"]:
                    in_chapter = True
                    continue

                # 检查是否是子章节的标题
                is_subchapter = False
                for subchapter in chapter["subchapters"]:
                    if line == subchapter["title"]:
                        is_subchapter = True
                        break

                if is_subchapter:
                    in_chapter = False
                    continue

                if in_chapter:
                    # 处理表格内容
                    processed_line = self.process_tables(line)
                    current_content.append(processed_line)

            # 将内容分割成groups
            chapter["groups"] = self.process_groups('\n'.join(current_content))

            # 递归处理子章节
            for subchapter in chapter["subchapters"]:
                process_chapter(subchapter)

        # 处理所有顶级章节
        for chapter in self.chapters:
            process_chapter(chapter)

    def to_json(self) -> str:
        """将处理后的内容转换为JSON格式"""
        book_data = {
            "title": os.path.basename(self.input_file).replace('.txt', ''),
            "chapters": self.chapters
        }
        return json.dumps(book_data, ensure_ascii=False, indent=2)

    def convert(self) -> str:
        """执行完整的转换流程"""
        self.read_file()
        self.extract_chapters()
        self.process_content()
        return self.to_json()


def main():
    # 示例用法
    input_file = "data/books/紫微斗數全書_wiki版.txt"  # 替换为实际的输入文件路径
    converter = TxtToJsonConverter(input_file)
    json_output = converter.convert()

    # 保存JSON输出
    output_file = input_file.replace('.txt', '.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_output)

    print(f"转换完成！输出文件：{output_file}")


if __name__ == "__main__":
    main()
