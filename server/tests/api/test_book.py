import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from app.core.config import settings
from app.core.security import create_access_token
from app.main import app

# 配置信息
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.ALGORITHM
TEST_USER_ID = "13693663221"
URL = "http://localhost:8080/api"


@pytest.mark.asyncio
async def test_list_books():
    """测试获取书籍列表"""
    category = "ziwei"
    test_token = create_access_token(TEST_USER_ID)
    async with AsyncClient(base_url=f"{URL}/book") as client:
        response = await client.get(f"/list?category={category}&is_completed=false&page=1&size=10")
        print(response.json())
    # 断言状态码和错误信息
    assert response.status_code == 200

#@pytest.mark.asyncio
def test_get_book():
    """测试获取书籍章节和第一章内容"""
    with TestClient(app) as client:
        response = client.post(f"{URL}/auth/login", json={
            "username": "13693663221",
            "password": "test1234",
            "login_type": "phone"
        })
        if response.status_code != 200:
            assert False
        client.cookies.set("access_token", response.cookies["access_token"], domain="testserver")
        book_id = "5dcc4db291044030b40d8a2b"
        #async with AsyncClient(base_url=f"{URL}/book") as client:
        response = client.get(f"{URL}/book/{book_id}")
        #response = await client.get(f"/{book_id}",
        #                            headers={"Authorization": f"Bearer {test_token}"})
    # 手动设置 Cookie 到 client
    # 断言状态码和错误信息
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_get_chapter():
    """测试获取指定章节内容及其子章节所有"""
    test_token = create_access_token(TEST_USER_ID)
    chapter_id = "3c1c4905c7bf4ae5897e2e77"
    async with AsyncClient(base_url=f"{URL}/book") as client:
        response = await client.get(f"/chapter/{chapter_id}",
                                    headers={"Authorization": f"Bearer {test_token}"})
        print(response.json())
    # 断言状态码和错误信息
    assert response.status_code == 200
