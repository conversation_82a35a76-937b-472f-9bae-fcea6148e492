import pytest
from httpx import AsyncClient
from jose import jwt
from datetime import datetime, timedelta
from app.core.config import settings

# 配置信息
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.ALGORITHM
TEST_USER_ID = "13693663221"
url = "http://localhost:8080/api"


@pytest.fixture
def test_token():
    """创建测试用的 JWT Token"""
    return jwt.encode({
        "sub": TEST_USER_ID,
        "exp": datetime.now() + timedelta(minutes=60)
    }, SECRET_KEY, algorithm=ALGORITHM)


@pytest.mark.asyncio
async def test_protected_route(test_token):
    """测试受保护的路由"""
    async with AsyncClient(base_url=f"{url}/test") as client:
        # 发送请求并携带 Authorization 头
        response = await client.get("/protected", headers={
            "Authorization": f"Bearer {test_token}"
        })
    
    # 断言状态码和返回值
    assert response.status_code == 200
    assert response.json() == {"message": f"Hello, {TEST_USER_ID}"}


@pytest.mark.asyncio
async def test_protected_route_unauthorized():
    """测试未授权访问"""
    async with AsyncClient(base_url=f"{url}/test") as client:
        response = await client.get("/protected")
    
    # 断言状态码和错误信息
    assert response.status_code == 401
    assert response.json()["detail"] == "缺少认证信息"

@pytest.mark.asyncio
async def test_register():
    """测试注册接口"""
    request = {
        "password": "test1234",
        "phone": "13693663221",
    }
    async with AsyncClient(base_url=f"{url}/auth") as client:
        response = await client.post("/register", json=request)
        print(response.json())
    # 断言状态码和错误信息
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_login():
    """测试用户登录"""
    login_data = {
        "username": "13693663221",
        "password": "test1234"
    }
    print("=========start=======")
    async with AsyncClient(base_url=f"{url}/auth") as client:
        response = await client.post("/login/json", json=login_data)
        print("=========")
    
    print("=========end=======")
    # 断言状态码和返回值
    assert response.status_code == 200
    assert "access_token" in response.json()
    assert response.json()["token_type"] == "bearer"
    print(response.json)
