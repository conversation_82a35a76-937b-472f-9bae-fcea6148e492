import pytest
import logging
from fastapi.testclient import TestClient
from app.db.models.user import UserRole
from app.main import app
from app.core.config import settings
from app.core.security import create_access_token

# 设置详细的日志级别
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 配置信息
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.ALGORITHM
TEST_USER_ID = "13693663221"
URL = "http://localhost:8080/api"
USER_ROLE = UserRole.REGULAR

@pytest.mark.asyncio
async def test_ziwei():
    """测试获取书籍章节和第一章内容"""
    with TestClient(app) as client:
        test_token = create_access_token(subject='13693663221', user_id=TEST_USER_ID, role=USER_ROLE)
        response = client.post(f"{URL}/ziwei/divinate", json={
            "user_name": "张三",
            "gender": "男",
            "birth_time": "1981-05-30 06:58:00",
            "true_time": "1981-05-30 06:27:00",
            "is_lunar": False,
            "is_true_time": True,
            "is_actual_zi": True,
            "relation_type": "other",
            "is_save_case": True,
            "birth_place": ["北京", "北京", "东城区"],
        }, headers={"Authorization": f"Bearer {test_token}"})

        print(response.json())
    # 断言状态码和错误信息
    assert response.status_code == 200