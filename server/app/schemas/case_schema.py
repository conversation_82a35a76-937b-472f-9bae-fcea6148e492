from typing import Optional 
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from app.schemas.enums import CaseType, RelationType

# 出生信息
class BirthData(BaseModel):
    # 性别
    gender: str 
    # 出生时间: 公历 例如：2025-06-05 12:00:00
    birth_time_solar: datetime
    # 出生时间: 农历 例如：2025年六月初五子时
    birth_time_lunar: str 
    #  四柱 例如：[戊辰, 己未, 甲戌, 己巳]
    bazi: list[str]
    # 出生地点: 例如：[北京,北京,东城区]
    birth_place: list[str] 

# 基础档案
class CaseBase(BaseModel):
    case_id: str
    user_name: str
    # 出生信息
    birth_data: BirthData
    # 测算结果
    divinate_result: dict
    # 关系类型
    relation_type: RelationType
    # 测算类型
    case_type: CaseType
    created_at: datetime
    updated_at: datetime

class DivinateRequestSchema(BaseModel):
    user_name: str
    gender: str
    birth_time: datetime
    # 真太阳时
    true_time: datetime
    is_lunar: bool = False
    # 真太阳时: 是否使用真太阳时
    is_true_time: bool = True
    # 是否使用早晚子时
    is_actual_zi: bool = True
    # 出生地点: 例如：[北京,北京,东城区]
    birth_place: list[str]
    # 关系类型
    relation_type: RelationType = RelationType.OTHER
    # 是否保存案例
    is_save_case: bool = True

