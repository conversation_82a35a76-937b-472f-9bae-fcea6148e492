from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class HeavenlyStem(str, Enum):
    """天干"""
    JIA = "甲"
    YI = "乙"
    BING = "丙"
    DING = "丁"
    WU = "戊"
    JI = "己"
    GENG = "庚"
    XIN = "辛"
    REN = "壬"
    GUI = "癸"


class EarthlyBranch(str, Enum):
    """地支"""
    ZI = "子"
    CHOU = "丑"
    YIN = "寅"
    MAO = "卯"
    CHEN = "辰"
    SI = "巳"
    WU = "午"
    WEI = "未"
    SHEN = "申"
    YOU = "酉"
    XU = "戌"
    HAI = "亥"


class Palace(str, Enum):
    """宫位"""
    MING = "命宫"
    PARENTS = "父母宫"
    FORTUNE = "财帛宫"
    SIBLINGS = "兄弟宫"
    CHILDREN = "子女宫"
    SPOUSE = "夫妻宫"
    HEALTH = "疾厄宫"
    CAREER = "事业宫"
    PROPERTY = "田宅宫"
    TRAVEL = "交友宫"
    OFFICIALS = "官禄宫"
    KARMA = "福德宫"


class Star(BaseModel):
    """星曜"""
    name: str
    brightness: Optional[str] = None  # 星曜亮度：庙、旺、得地、平、陷
    description: Optional[str] = None  # 星曜描述


class PalaceData(BaseModel):
    """宫位数据"""
    palace: Palace  # 宫位
    earthly_branch: EarthlyBranch  # 地支
    stars: List[Star] = []  # 星曜列表
    description: Optional[str] = None  # 宫位描述


class ZiweiChartRequest(BaseModel):
    """紫微斗数命盘请求"""
    birth_date: datetime  # 出生日期
    birth_time: str  # 出生时间（HH:MM格式）
    gender: str  # 性别
    is_lunar_calendar: bool = True  # 是否农历
    
    @validator('birth_time')
    def validate_birth_time(cls, v):
        import re
        if not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', v):
            raise ValueError('出生时间必须是有效的HH:MM格式')
        return v


class ZiweiChart(BaseModel):
    """紫微斗数命盘"""
    user_id: Optional[str] = None  # 用户ID
    name: Optional[str] = None  # 命盘名称
    birth_date: datetime  # 出生日期
    birth_time: str  # 出生时间
    gender: str  # 性别
    is_lunar_calendar: bool  # 是否农历
    
    # 命盘核心数据
    year_pillar: str = Field(..., description="年柱（天干地支）")
    month_pillar: str = Field(..., description="月柱（天干地支）")
    day_pillar: str = Field(..., description="日柱（天干地支）")
    hour_pillar: str = Field(..., description="时柱（天干地支）")
    
    ming_gong: EarthlyBranch = Field(..., description="命宫地支")
    shen_gong: EarthlyBranch = Field(..., description="身宫地支")
    
    palaces: List[PalaceData] = []  # 十二宫数据
    
    # 大运数据
    major_cycles: Optional[List[dict]] = None
    
    # 流年数据
    annual_cycles: Optional[List[dict]] = None
    
    # 分析结果
    analysis: Optional[dict] = None
    
    created_at: Optional[datetime] = None
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "1234567890",
                "name": "张三命盘",
                "birth_date": "1990-01-01T00:00:00",
                "birth_time": "12:00",
                "gender": "男",
                "is_lunar_calendar": True,
                "year_pillar": "庚午",
                "month_pillar": "己丑",
                "day_pillar": "甲子",
                "hour_pillar": "丙午",
                "ming_gong": "寅",
                "shen_gong": "申",
            }
        } 