from typing import Optional
import re
from pydantic import BaseModel, EmailStr, Field, field_validator, ConfigDict
from pydantic import model_validator
from app.db.models.user import UserRole

class TokenSchema(BaseModel):
    """令牌模型"""
    access_token: str
    token_type: str = "bearer"

class TokenPayloadSchema(BaseModel):
    """令牌载荷模型"""
    sub: Optional[str] = None  # 用户名
    user_id: Optional[str] = None  # 用户ID
    role: Optional[UserRole] = None  # 用户角色
    exp: Optional[int] = None  # 过期时间

class CurrentUser(BaseModel):
    """当前用户信息（轻量级）"""
    user_id: str
    user_name: str
    role: UserRole
    expires_at: int
    
    model_config = ConfigDict(from_attributes=True)

class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str  # 登录用户名（邮箱或手机号）
    password: Optional[str] = None  # 密码
    login_type: Optional[str] = Field(None, description="登录类型：email 或 phone")  # 登录类型
    code: Optional[str] = Field(None, min_length=6, max_length=6)  # 验证码

    @field_validator('login_type')
    def validate_login_type(cls, v):  # pylint: disable=no-self-argument
        if v and v not in ['email', 'phone']:
            raise ValueError('登录类型必须是 email 或 phone')
        return v

    @model_validator(mode='before')
    def validate_pwd_or_code(cls, values): # pylint: disable=no-self-argument
        if not values.get('code') and not values.get('password'):
            raise ValueError('必须提供密码或验证码')
        return values

class RegisterRequest(BaseModel):
    """用户注册模型"""
    password: str = Field(..., min_length=8)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=11, max_length=11)

    @field_validator('phone')
    def validate_phone(cls, v):  # pylint: disable=no-self-argument
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v

    @field_validator('email')
    def validate_email(cls, v):  # pylint: disable=no-self-argument
        if v and not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', v):
            raise ValueError('邮箱格式不正确')
        return v

    @field_validator('password')
    def validate_password(cls, v):  # pylint: disable=no-self-argument
        if len(v) < 8:
            raise ValueError('密码长度不能小于8位')
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('密码必须包含英文字母')
        if not re.search(r'\d', v):
            raise ValueError('密码必须包含数字')
        return v

    @model_validator(mode='before')
    def validate_email_or_phone(cls, values): # pylint: disable=no-self-argument
        if not values.get('email') and not values.get('phone'):
            raise ValueError('必须提供邮箱或手机号')
        return values

class PasswordResetRequestSchema(BaseModel):
    """密码重置请求模型"""
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=11, max_length=11)

    @model_validator(mode='before')
    def validate_email_or_phone(cls, values): # pylint: disable=no-self-argument
        if not values.get('email') and not values.get('phone'):
            raise ValueError('必须提供邮箱或手机号')
        return values

class PasswordResetSchema(BaseModel):
    """密码重置模型"""
    token: str
    new_password: str

class ChangePasswordSchema(BaseModel):
    """修改密码模型"""
    current_password: str
    new_password: str

class UserResponse(BaseModel):
    """用户响应模型"""
    user_id: str
    user_name: str
    email: Optional[EmailStr]
    phone: Optional[str]
    is_active: bool
    role: UserRole

    model_config = ConfigDict(from_attributes=True)