from enum import Enum

class CaseType(str, Enum):
    """案例类别枚举"""
    BAZI = "bazi"  # 八字
    MEIHUA = "meihua"  # 梅花易数
    ZIWEI = "ziwei"  # 紫微斗数

    def __str__(self) -> str:
        return self.value
    
    @classmethod
    def get_case_type_from_display_name(cls, display_name: str) -> "CaseType":
        """根据显示名称获取枚举类别
        
        Args:   
            display_name: 显示名称
            
        Returns:
            对应的枚举类别，如果没有匹配则返回OTHER
        """
        name_to_enum = {
            "八字": cls.BAZI,
            "梅花易数": cls.MEIHUA,
            "紫微斗数": cls.ZIWEI
        }
        return name_to_enum.get(display_name, cls.OTHER) 
    
    def get_display_name(cls, value: str) -> str:
        """获取类别的显示名称
        
        Args:
            value: 枚举值
            
        Returns:
            显示名称
        """ 
        display_names = {
            cls.BAZI.value: "八字",
            cls.MEIHUA.value: "梅花易数",
            cls.ZIWEI.value: "紫微斗数"
        }
        return display_names.get(value, "未知类别")

class BookCategory(str, Enum):
    """书籍类别枚举"""
    BAZI = "bazi"  # 八字
    MEIHUA = "meihua"  # 梅花易数
    ZIWEI = "ziwei"  # 紫微斗数
    GENERAL = "general"  # 通用命理
    OTHER = "other"  # 其他
    
    def __str__(self) -> str:
        return self.value
        
    @classmethod
    def get_display_name(cls, value: str) -> str:
        """获取类别的显示名称
        
        Args:
            value: 枚举值
            
        Returns:
            显示名称
        """
        display_names = {
            cls.BAZI.value: "八字",
            cls.MEIHUA.value: "梅花易数",
            cls.ZIWEI.value: "紫微斗数",
            cls.GENERAL.value: "通用命理",
            cls.OTHER.value: "其他"
        }
        return display_names.get(value, "未知类别")
        
    @classmethod
    def get_category_from_display_name(cls, display_name: str) -> "BookCategory":
        """根据显示名称获取枚举类别
        
        Args:
            display_name: 显示名称
            
        Returns:
            对应的枚举类别，如果没有匹配则返回OTHER
        """
        name_to_enum = {
            "八字": cls.BAZI,
            "梅花易数": cls.MEIHUA,
            "紫微斗数": cls.ZIWEI,
            "通用命理": cls.GENERAL,
            "其他": cls.OTHER
        }
        return name_to_enum.get(display_name, cls.OTHER) 

class RelationType(str, Enum):
    """案例与用户的关系类型"""
    FAMILY = "family"   # 家人
    FRIEND = "friend"   # 朋友
    CLASSMATE = "classmate"   # 同学
    COLLEAGUE = "colleague"   # 同事
    OTHER = "other"   # 其他

    def __str__(self) -> str:
        return self.value
    
    @classmethod
    def get_display_name(cls, value: str) -> str:
        """获取关系类型的显示名称
        
        Args:
            value: 枚举值
            
        Returns:
            显示名称
        """
        display_names = {
            cls.FAMILY.value: "家人",
            cls.FRIEND.value: "朋友",
            cls.CLASSMATE.value: "同学",
            cls.COLLEAGUE.value: "同事",
            cls.OTHER.value: "其他"
        }
        return display_names.get(value, "未知关系")
    
    @classmethod
    def get_relation_type_from_display_name(cls, display_name: str) -> "RelationType":
        """根据显示名称获取枚举关系类型
        
           Args:
            display_name: 显示名称
            
        Returns:
            对应的枚举关系类型，如果没有匹配则返回OTHER
        """
        name_to_enum = {
            "家人": cls.FAMILY,
            "朋友": cls.FRIEND,
            "同学": cls.CLASSMATE,
            "同事": cls.COLLEAGUE,
            "其他": cls.OTHER
        }
        return name_to_enum.get(display_name, cls.OTHER) 