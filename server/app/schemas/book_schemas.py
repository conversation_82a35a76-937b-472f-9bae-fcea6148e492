from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, ConfigDict


# 书籍基本信息模型
class BookInfoSchema(BaseModel):
    book_id: str
    title: str
    author: Optional[str] = None
    category: str
    description: Optional[str] = None
    cover_image: Optional[str] = None
    has_translation: bool = False
    is_completed: bool = True
    view_count: int = 0
    created_at: datetime
    updated_at: datetime
    content_format: str = "json"
    main_chapter_level: int = 1
    
    model_config = ConfigDict(from_attributes=True, extra="ignore")


'''
# 书籍创建模型
class BookCreate(BookBase):
    content_document_id: Optional[str] = None
    content_collection: str = "books"

# 书籍更新模型
class BookUpdate(BaseModel):
    title: Optional[str] = None
    author: Optional[str] = None
    category_enum: Optional[BookCategory] = None
    category_id: Optional[str] = None
    description: Optional[str] = None
    cover_image: Optional[str] = None
    has_translation: Optional[bool] = None
    content_format: Optional[str] = None
    is_published: Optional[bool] = None
'''

# 章节基本信息 
class BookChapterInfoSchema(BaseModel):
    book_id: str
    chapter_id: str  # 章节ID
    title: str     # 章节标题
    level: int = 1  # 章节级别，1为最高
    path: str      # 章节路径
    parent_id: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

# 章节的段落部分内容
class BookChapterSectionSchema(BaseModel):
    type: str
    content: str    # type是image时，content是图片url
    quote: Optional[str] = None      # 注解
    translation: Optional[str] = None # 翻译

# 书籍章节模型
class BookChapterContentSchema(BookChapterInfoSchema):
    content: List[BookChapterSectionSchema]
    subchapters: List['BookChapterContentSchema']

# 书籍列表响应模型
class BookListResponse(BaseModel):
    items: List[BookInfoSchema]
    total: int
    page: int
    page_size: int
    total_pages: int
    
# 书籍内容模型
class BookChapterContentResponse(BaseModel):
    chapters: BookChapterContentSchema

# 书籍首页响应模型
class BookMainResponse(BaseModel):
    #book_info: BookInfoSchema
    chapter_list: List[BookChapterInfoSchema]
    content: Optional[BookChapterContentSchema] = None 

# 循环引用解决
BookChapterContentSchema.model_rebuild()
