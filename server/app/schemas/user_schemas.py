"""待修改"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator, ConfigDict
from app.db.models.user import UserRole


# 共享属性
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    is_active: Optional[bool] = True
    full_name: Optional[str] = None
    phone: Optional[str] = None
    role: Optional[UserRole] = None
    birth_date: Optional[datetime] = None
    birth_time: Optional[str] = None
    birth_place: Optional[str] = None
    is_lunar_calendar: Optional[bool] = False
    gender: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


# 创建用户
class UserCreate(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8)
    full_name: Optional[str] = None
    phone: Optional[str] = None


# 更新用户
class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    full_name: Optional[str] = None
    phone: Optional[str] = None
    birth_date: Optional[datetime] = None
    birth_time: Optional[str] = None
    birth_place: Optional[str] = None
    is_lunar_calendar: Optional[bool] = None
    gender: Optional[str] = None
    
    @validator('birth_time')
    def validate_birth_time(cls, v):    # pylint: disable=no-self-argument
        if v is not None:
            # 检查时间格式是否为HH:MM
            import re
            if not re.match(r'^([01]\d|2[0-3]):([0-5]\d)$', v):
                raise ValueError('出生时间必须是有效的HH:MM格式')
        return v


# 读取用户信息（返回给前端）
class UserOut(UserBase):
    id: str
    created_at: datetime
    membership_expires_at: Optional[datetime] = None


# 管理员用户更新（仅管理员使用）
class UserAdminUpdate(UserUpdate):
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    role: Optional[UserRole] = None
    membership_expires_at: Optional[datetime] = None


# 用户权限检查
class UserPermissions(BaseModel):
    is_premium: bool = False
    is_admin: bool = False 