import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from py_iztro import AstrolabeModel

from app.dependencies.service import get_ziwei_service, get_current_user_info_optional
from app.services.divination.ziwei_service import ZiweiService
from app.schemas.case_schema import DivinateRequestSchema
from app.schemas.auth_schemas import CurrentUser

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/divinate", response_model=AstrolabeModel)
async def ziwei_divinate(
    request: DivinateRequestSchema,
    ziwei_service: ZiweiService = Depends(get_ziwei_service),
    current_user: CurrentUser = Depends(get_current_user_info_optional)
) -> Any:
    """
    紫微斗数测算, 目前 Astro库存在递归问题，不可用
    
    Args:
        request: 测算请求参数
        ziwei_service: 紫微斗数服务
        current_user: 当前用户（可选）
    
    Returns:
        AstrolabeModel: 紫微斗数星盘模型
    """
    try:
        logger.debug(f"用户 {current_user.user_id if current_user else 'anonymous'} 开始紫微斗数测算")
        logger.debug(f"测算参数: 姓名={request.user_name}, 性别={request.gender}, 出生时间={request.birth_time}")
        
        # 选择使用哪个时间：如果启用真太阳时则使用 true_time，否则使用 birth_time
        target_time = request.true_time if request.is_true_time else request.birth_time
        
        # # 调用紫微斗数服务进行测算
        # result = await ziwei_service.divinate(
        #     birth_time_solar=target_time,
        #     gender=request.gender
        # )
        
        # logger.debug(f"紫微斗数测算成功，用户: {current_user.user_id if current_user else 'anonymous'}")
        # if request.is_save_case:
        #     # 保存案例
        #     pass
        
        return None
        
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}") from e
    except Exception as e:
        logger.error(f"紫微斗数测算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"紫微斗数测算失败: {str(e)}") from e

@router.get("/test")
async def test_ziwei():
    """
    测试紫微斗数API
    """
    return {"message": "紫微斗数API正常运行"} 