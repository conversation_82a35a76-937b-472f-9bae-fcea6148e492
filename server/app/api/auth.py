from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Response, <PERSON>ie
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import ValidationError
from app.services.auth_service import AuthService
from app.schemas.auth_schemas import TokenSchema, LoginRequest, UserResponse, RegisterRequest, CurrentUser
from app.dependencies.service import get_auth_service, get_current_user, get_current_user_info, oauth2_scheme
from app.db.models.user import User
from app.core.config import settings

router = APIRouter()


# async def login_user(
#     form_data: OAuth2PasswordRequestForm = Depends(),
#     auth_service: AuthService = Depends(get_auth_service)):
#     """
#     OAuth2 兼容的令牌登录
#     """
#     try:
#         token = await auth_service.authenticate_user(form_data.username, form_data.password)
#         return token
#     except HTTPException as e:
#         raise e
@router.post("/login")
async def login(request: LoginRequest, response: Response, auth_service: AuthService = Depends(get_auth_service)):
    # Cookie
    try:
        token = await auth_service.authenticate_user(
            username=request.username, 
            password=request.password,
            login_type=request.login_type
        )
        cookie_domain = ".bazi.com" 
        response.set_cookie(
            key="access_token",
            value=token.access_token,
            httponly=True,
            secure=True,
            samesite="none",
            max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            domain=cookie_domain,
            path="/",
        )
        return token
    except HTTPException as e:
        raise e


@router.post("/login/json", response_model=TokenSchema)
async def login_json(
    request: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)) :
    """
    JSON格式的登录接口，支持指定登录类型
    """
    try:
        token = await auth_service.authenticate_user(
            username=request.username, 
            password=request.password,
            login_type=request.login_type
        )
        return token
    except HTTPException as e:
        raise e


@router.post("/register", response_model=UserResponse)
async def register_user(
    request: RegisterRequest,
    auth_service: AuthService = Depends(get_auth_service)):
    """
    用户注册
    """
    try:
        user = await auth_service.register_user(request)
        return UserResponse(
            user_id=user.user_id,
            user_name=user.user_name,
            email=user.email,
            phone=user.phone,
            is_active=user.is_active,
            role=user.role
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="注册失败")


@router.post("/refresh", response_model=TokenSchema)
async def refresh_token(
     auth_service: AuthService = Depends(get_auth_service),
     token: str = Depends(oauth2_scheme)):
    """
    刷新访问令牌
    """
    try:
        new_token = await auth_service.refresh_token(token)
        return new_token
    except HTTPException as e:
        raise e

@router.post("/refresh/cookie", response_model=TokenSchema)
async def refresh_token_with_cookie(
    response: Response,  
    auth_service: AuthService = Depends(get_auth_service),
    access_token: str = Cookie(None),
    ):
    """
    刷新访问令牌
    """
    try:
        new_token = await auth_service.refresh_token(access_token)
        cookie_domain = ".bazi.com"
        response.set_cookie(
            key="access_token",
            value=new_token.access_token,
            httponly=True,
            secure=True,
            samesite="none",
            max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            domain=cookie_domain,
            path="/",
        )
        return new_token
    except HTTPException as e:
        raise e


@router.get("/me", response_model=UserResponse)
async def read_users_me(
    user: User = Depends(get_current_user)):
    """
    获取当前登录用户完整信息
    """
    return UserResponse(
        user_id=user.user_id,
        user_name=user.user_name,
        email=user.email,
        phone=user.phone,
        is_active=user.is_active,
        role=user.role
    )