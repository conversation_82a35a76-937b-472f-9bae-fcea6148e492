from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from typing import Any, Optional, Union

from jose import jwt
from passlib.context import CryptContext

from app.core.config import settings
from app.db.models.user import UserRole

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(
    subject: Union[str, Any], 
    user_id: str,
    role: UserRole,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    创建JWT访问令牌
    
    Args:
        subject: 用户名
        user_id: 用户ID
        role: 用户角色
        expires_delta: 过期时间间隔
        
    Returns:
        编码后的JWT令牌字符串
    """
    local_tz = ZoneInfo('Asia/Shanghai')  # 使用中国时区
    if expires_delta:
        expire = datetime.now(local_tz) + expires_delta
    else:
        expire = datetime.now(local_tz) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire, 
        "sub": str(subject),
        "user_id": user_id,
        "role": role.value  # 使用枚举的value
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证明文密码与哈希密码是否匹配"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码的哈希值"""
    return pwd_context.hash(password) 