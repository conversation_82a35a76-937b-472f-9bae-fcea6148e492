import json
import logging
from typing import Any, Optional, Type
from pydantic import BaseModel
import redis.asyncio as async_redis
import redis as sync_redis
from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """Redis缓存管理器"""
    
    def __init__(self, config=None):
        """初始化Redis缓存管理器
        
        Args:
            config: 配置对象，默认使用settings
        """
        self.config = config or settings
        self._async_redis_pool = None
        self._async_redis_client = None
        self._sync_redis_pool = None
        self._sync_redis_client = None
        self._init_pools()

    def _init_pools(self) -> None:
        """初始化Redis连接池（同步和异步）"""
        try:
            # 异步连接池
            self._async_redis_pool = async_redis.ConnectionPool(
                host=self.config.REDIS_HOST,
                port=self.config.REDIS_PORT,
                db=self.config.REDIS_DB,
                password=self.config.REDIS_PASSWORD,
                max_connections=20,
                decode_responses=True
            )
            self._async_redis_client = async_redis.Redis(connection_pool=self._async_redis_pool)
            
            # 同步连接池
            self._sync_redis_pool = sync_redis.ConnectionPool(
                host=self.config.REDIS_HOST,
                port=self.config.REDIS_PORT,
                db=self.config.REDIS_DB,
                password=self.config.REDIS_PASSWORD,
                max_connections=10,  # 离线处理连接池较小
                decode_responses=True
            )
            self._sync_redis_client = sync_redis.Redis(connection_pool=self._sync_redis_pool)
            
            logger.info("Redis连接池初始化成功（同步和异步）")
        except Exception as e:
            logger.error("Redis连接池初始化失败: %s", {str(e)})
            raise

    async def is_connected(self) -> bool:
        """检查异步Redis连接是否可用
        
        Returns:
            连接是否可用
        """
        if not self._async_redis_client:
            logger.error("Redis客户端未初始化")
            return False
        try:
            # 使用ping命令检查连接
            await self._async_redis_client.ping()
            logger.info("Redis 异步已连接")
            return True
        except Exception as e:
            logger.error("Redis异步连接检查失败: %s", {str(e)})
            return False
            
    def is_sync_connected(self) -> bool:
        """检查同步Redis连接是否可用
        
        Returns:
            连接是否可用
        """
        if not self._sync_redis_client:
            logger.error("Redis客户端未初始化")
            return False
        try:
            # 使用ping命令检查连接
            self._sync_redis_client.ping()
            logger.info("Redis 已连接")
            return True
        except Exception as e:
            logger.error("Redis同步连接检查失败: %s", {str(e)})
            return False

    async def close(self) -> None:
        """关闭Redis连接"""
        if self._async_redis_pool:
            await self._async_redis_pool.disconnect()
            self._async_redis_pool = None
            self._async_redis_client = None
        
        if self._sync_redis_pool:
            self._sync_redis_pool.disconnect()
            self._sync_redis_pool = None
            self._sync_redis_client = None
            
        logger.info("Redis连接已关闭")

    # 异步操作方法
    async def get(self, key: str, model: Optional[Type[BaseModel]]=None) -> Optional[Any]:
        """从缓存中异步获取数据"""
        try:
            data = await self._async_redis_client.get(key)
            if not data:
                return None
            data = json.loads(data)
            if model and "__pydantic_type" in data and data["__pydantic_type"] == "single":
                return model.model_validate(json.loads(data["data"]))  # 反序列化后构造模型
            elif model and "__pydantic_type" in data and data["__pydantic_type"] == "list":
                return [model.model_validate(json.loads(item)) for item in data["data"]]
            else:
                return data
        except Exception as e:
            logger.error("异步获取缓存失败: %s", {str(e)})
            return None

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """异步将数据存入缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒），如果不指定则使用默认值
        """
        try:
            if expire is None:
                expire = self.config.REDIS_DEFAULT_EXPIRE
                
            if isinstance(value, BaseModel):
                value = {"__pydantic_type": "single", "data": value.model_dump_json()}
            elif isinstance(value, list) and all(isinstance(v, BaseModel) for v in value):
                value = {"__pydantic_type": "list", "data": [v.model_dump_json() for v in value]}
            await self._async_redis_client.set(key, json.dumps(value), ex=expire)
            return True
        except Exception as e:
            logger.error("异步设置缓存失败: %s", {str(e)})
            return False

    async def set_short(self, key: str, value: Any) -> bool:
        """异步将数据存入短期缓存（5分钟）"""
        return await self.set(key, value, self.config.REDIS_SHORT_EXPIRE)

    async def set_long(self, key: str, value: Any) -> bool:
        """异步将数据存入长期缓存（24小时）"""
        return await self.set(key, value, self.config.REDIS_LONG_EXPIRE)

    async def set_user_session(self, key: str, value: Any) -> bool:
        """异步将用户会话数据存入缓存（2小时）"""
        return await self.set(key, value, self.config.REDIS_USER_SESSION_EXPIRE)

    async def delete(self, key: str) -> bool:
        """异步删除缓存"""
        try:
            await self._async_redis_client.delete(key)
            return True
        except Exception as e:
            logger.error("异步删除缓存失败: %s", {str(e)})
            return False
    
    # 同步操作方法
    def get_sync(self, key: str) -> Optional[Any]:
        """从缓存中同步获取数据"""
        try:
            data = self._sync_redis_client.get(key)
            return json.loads(data) if data else None
        except Exception as e:
            logger.error("同步获取缓存失败: %s", {str(e)})
            return None

    def set_sync(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """同步将数据存入缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒），如果不指定则使用默认值
        """
        try:
            if expire is None:
                expire = self.config.REDIS_DEFAULT_EXPIRE
            self._sync_redis_client.set(key, json.dumps(value), ex=expire)
            return True
        except Exception as e:
            logger.error("同步设置缓存失败: %s", {str(e)})
            return False

    def set_short_sync(self, key: str, value: Any) -> bool:
        """同步将数据存入短期缓存（5分钟）"""
        return self.set_sync(key, value, self.config.REDIS_SHORT_EXPIRE)

    def set_long_sync(self, key: str, value: Any) -> bool:
        """同步将数据存入长期缓存（24小时）"""
        return self.set_sync(key, value, self.config.REDIS_LONG_EXPIRE)

    def set_user_session_sync(self, key: str, value: Any) -> bool:
        """同步将用户会话数据存入缓存（2小时）"""
        return self.set_sync(key, value, self.config.REDIS_USER_SESSION_EXPIRE)

    def delete_sync(self, key: str) -> bool:
        """同步删除缓存"""
        try:
            self._sync_redis_client.delete(key)
            return True
        except Exception as e:
            logger.error("同步删除缓存失败: %s", {str(e)})
            return False

# 初始化Redis缓存的函数
def init_redis_cache(config=None):
    """初始化Redis缓存，用于app.state
    
    Args:
        config: 配置对象，默认使用全局settings
        
    Returns:
        RedisCache实例
    """
    return RedisCache(config)

## 获取Redis缓存的函数 (兼容旧代码)
#def get_cache(app=None):
#    """获取Redis缓存
#    
#    Args:
#        app: FastAPI应用实例，如果提供则从app.state获取
#        
#    Returns:
#        RedisCache实例
#    """
#    # 如果有app参数，从app.state获取
#    if app and hasattr(app, 'state') and hasattr(app.state, 'redis_cache'):
#        return app.state.redis_cache
#    
#    # 兼容代码，在没有app参数的情况下，从当前活动的app获取
#    try:
#        from fastapi.applications import active_app
#        if active_app and hasattr(active_app.state, 'redis_cache'):
#            return active_app.state.redis_cache
#    except (ImportError, AttributeError):
#        pass
#    
#    # 如果无法获取，则创建新实例（不推荐）
#    raise ValueError("无法从app.state获取Redis缓存，请先创建新实例")