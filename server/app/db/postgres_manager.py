from sqlalchemy.orm import declarative_base
from app.db.base_db_manager import BaseSyncManager, BaseAsyncManager
from app.core.config import settings
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import AsyncAdaptedQueuePool
import logging

logger = logging.getLogger(__name__)

class SyncPostgresManager(BaseSyncManager):
    """PostgreSQL同步连接管理器"""
    def get_engine(self):
        if not self.engine:
            self.engine = create_engine(
                self.config.DATABASE_URI,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True
            )
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
        return self.engine

class AsyncPostgresManager(BaseAsyncManager):
    """PostgreSQL异步连接管理器"""
    def get_async_engine(self):
        if not self.async_engine:
            async_url = self.config.DATABASE_URI.replace("postgresql://", "postgresql+asyncpg://")
            self.async_engine = create_async_engine(
                async_url,
                echo=self.config.DEBUG,
                future=True,
                # 配置连接池
                poolclass=AsyncAdaptedQueuePool,
                pool_size=20,  # 连接池大小
                max_overflow=10,  # 允许超出池大小的连接数
                pool_timeout=30,  # 获取连接的超时时间
                pool_recycle=1800,  # 连接回收时间(秒)
                pool_pre_ping=True,  # 使用前先ping一下确保连接有效
            )
            self.AsyncSessionLocal = sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        return self.async_engine

    async def verify_connection(self):
        """显式执行连接验证"""
        self.get_async_engine()
        async with self.async_engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
            logger.info("PostgreSQL 连接验证通过")

# 初始化异步PostgreSQL管理器
def init_async_postgres(config=None) -> AsyncPostgresManager:
    """初始化PostgreSQL异步管理器，用于app.state
    
    Args:
        config: 配置对象，默认使用全局settings
        
    Returns:
        AsyncPostgresManager实例
    """
    config = config or settings
    manager = AsyncPostgresManager(config)
    manager.get_async_engine()  # 初始化连接
    return manager

# 初始化同步PostgreSQL管理器
def init_sync_postgres(config=None) -> SyncPostgresManager:
    """初始化PostgreSQL同步管理器，用于app.state
    
    Args:
        config: 配置对象，默认使用全局settings
        
    Returns:
        SyncPostgresManager实例
    """
    config = config or settings
    manager = SyncPostgresManager(config)
    manager.get_engine()  # 初始化连接
    return manager

#def get_async_postgres_manager(app=None):
#    """获取PostgreSQL管理器
#    
#    Args:
#        app: FastAPI应用实例，如果提供则从app.state获取
#        sync: 是否返回同步管理器，默认为False（返回异步管理器）
#        
#    Returns:
#        PostgreSQL管理器实例
#    """
#    # 如果有app参数，从app.state获取
#    if app and hasattr(app, 'state'):
#        if hasattr(app.state, 'postgres'):
#            return app.state.postgres
#
#    # 兼容代码，在没有app参数的情况下，从当前活动的app获取
#    try:
#        from fastapi.applications import active_app
#        if active_app and hasattr(active_app.state, 'postgres'):
#            return active_app.state.postgres
#    except (ImportError, AttributeError):
#        pass
#    
#    # 如果无法获取，则报错
#    raise ValueError("无法从app.state获取PostgreSQL管理器，请先创建新实例")


