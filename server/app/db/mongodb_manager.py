from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorClientSession
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, List, AsyncIterator
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class MongoDBManager:
    """MongoDB数据库管理器(同步模式)"""
    
    def __init__(self, config=None):
        """初始化MongoDB管理器
        
        Args:
            config: 配置对象，默认使用settings
        """
        self.config = config or settings
        self._client = None
        self._db = None
        self._init_client()

    def _init_client(self) -> None:
        """初始化MongoDB连接"""
        try:
            self._client = MongoClient(
                self.config.MONGODB_URI,
                maxPoolSize=50,  # 连接池大小
                minPoolSize=10,  # 最小保持连接数
                maxIdleTimeMS=30000,  # 空闲连接最大保留时间
                serverSelectionTimeoutMS=5000  # 服务器选择超时
            )
            self._db = self._client[self.config.MONGODB_DB]
            logger.info("MongoDB初始化成功")
            #logger.debug("MongoDB初始化成功: %s/%s", self.config.MONGODB_URI, self.config.MONGODB_DB)
        except Exception as e:
            logger.error("MongoDB初始化失败: %s", str(e))
            self._client = None
            self._db = None
            raise

    def get_database(self) -> Optional[Database]:
        """获取MongoDB数据库实例"""
        if self._db is None:
            self._init_client()
        return self._db
        
    def get_client(self) -> Optional[MongoClient]:
        """获取MongoDB客户端实例"""
        if self._client is None:
            self._init_client()
        return self._client

    def get_collection(self, collection_name: str) -> Optional[Collection]:
        """获取MongoDB集合
        
        Args:
            collection_name: 集合名称
            
        Returns:
            MongoDB集合对象
        """
        if self._db is None:
            self._init_client()
        return self._db[collection_name]
        
    def close(self) -> None:
        """关闭MongoDB连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            logger.info("MongoDB连接已关闭")

class AsyncMongoDBManager:
    """异步MongoDB数据库管理器"""
    
    def __init__(self, config=None):
        """初始化MongoDB管理器
        
        Args:
            config: 配置对象，默认使用settings
        """
        self.config = config or settings
        self._client = None
        self._db = None
        self._init_client()

    def _init_client(self) -> None:
        """初始化MongoDB异步连接"""
        if self._client is not None and self._db is not None:
            return 
        try:
            self._client = AsyncIOMotorClient(
                self.config.MONGODB_URI,
                maxPoolSize=50,  # 连接池大小
                minPoolSize=10,  # 最小保持连接数
                maxIdleTimeMS=30000,  # 空闲连接最大保留时间
                serverSelectionTimeoutMS=5000  # 服务器选择超时
            )
            self._db = self._client[self.config.MONGODB_DB]
            logger.info("MongoDB异步连接初始化成功")
            #logger.debug("MongoDB异步连接初始化成功: %s", self.config.MONGODB_URI)
        except Exception as e:
            logger.error("MongoDB异步连接初始化失败: %s", str(e))
            self._client = None
            self._db = None
            raise
    
    async def verify_connection(self):
        try:
            await self._client.admin.command("ping")
            logger.info("MongoDB 异步已连接")
            #logger.info("col name:%s", await self._db.list_collection_names())
        except Exception as e:
            logger.error("MongoDB 连接测试失败：%s", e)
            raise

    #async def get_client(self) -> Optional[MongoClient]:
    #    """获取MongoDB客户端实例"""
    #    if self._client is None:
    #        await self.init_connectiong()
    #    return self._client

    #async def get_database(self) -> Optional[AsyncIOMotorDatabase]:
    #    """获取MongoDB数据库实例"""
    #    if self._db is None:
    #        await self.init_connectiong()
    #    return self._db

    def get_collection(self, collection_name: str) -> Optional[Collection]:
        """获取MongoDB集合
        
        Args:
            collection_name: 集合名称
            
        Returns:
            MongoDB集合对象
        """
        if self._db is None:
            self._init_client()
        return self._db[collection_name]

    @asynccontextmanager
    async def transaction(self) -> AsyncIterator[AsyncIOMotorClientSession]:
        """统一事务管理：自动启动事务"""
        session = await self._client.start_session()
        try:
            async with session.start_transaction():
                yield session
                await session.commit_transaction()  # 正常执行则提交
        except Exception as e:
            await session.abort_transaction()  # 异常时回滚
        finally:
            await session.end_session()

    #@asynccontextmanager
    #async def start_session(self): 
    #    """提供MongoDB的事务session上下文"""
    #    session = await self._client.start_session()
    #    try:
    #        yield session
    #    finally:
    #        await session.end_session()
        
    async def find_one(self, collection_name: str, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从集合中查询单个文档
        
        Args:
            collection_name: 集合名称
            query: 查询条件
            
        Returns:
            查询结果文档
        """
        collection = self.get_collection(collection_name)
        return await collection.find_one(query)
    
    async def find_many(self, collection_name: str, query: Dict[str, Any], 
                       skip: int = 0, limit: int = 0, 
                       sort: List[tuple] = None) -> List[Dict[str, Any]]:
        """从集合中查询多个文档
        
        Args:
            collection_name: 集合名称
            query: 查询条件
            skip: 跳过的文档数
            limit: 返回的最大文档数
            sort: 排序条件，如[("created_at", -1)]
            
        Returns:
            查询结果文档列表
        """
        collection = self.get_collection(collection_name)
        cursor = collection.find(query).skip(skip)
        
        if limit > 0:
            cursor = cursor.limit(limit)
            
        if sort:
            cursor = cursor.sort(sort)
            
        return await cursor.to_list(length=None)
    
    async def insert_one(self, collection_name: str, document: Dict[str, Any]) -> str:
        """插入单个文档
        
        Args:
            collection_name: 集合名称
            document: 要插入的文档
            
        Returns:
            插入的文档ID
        """
        collection = self.get_collection(collection_name)
        result = await collection.insert_one(document)
        return str(result.inserted_id)
    
    async def insert_many(self, collection_name: str, documents: List[Dict[str, Any]], session: Optional[AsyncIOMotorClientSession] = None) -> List[str]:
        """插入多个文档, 当前实现未采用事务机制
        
        Args:
            collection_name: 集合名称
            documents: 要插入的文档列表
            
        Returns:
            插入的文档ID列表
        """
        collection = self.get_collection(collection_name)
        if session:
            result = await collection.insert_many(documents, session=session)
        else:
            result = await collection.insert_many(documents)
        return [str(id) for id in result.inserted_ids]
    
    async def update_one(self, collection_name: str, filter: Dict[str, Any], 
                        update: Dict[str, Any], upsert: bool = False) -> int:
        """更新单个文档
        
        Args:
            collection_name: 集合名称
            filter: 查询条件
            update: 更新操作
            upsert: 如果为True，则在没有匹配文档时插入新文档
            
        Returns:
            更新的文档数量
        """
        collection = self.get_collection(collection_name)
        result = await collection.update_one(filter, update, upsert=upsert)
        return result.modified_count
    
    async def update_many(self, collection_name: str, filter: Dict[str, Any], 
                         update: Dict[str, Any], upsert: bool = False, session: Optional[AsyncIOMotorClientSession] = None) -> int:
        """更新多个文档
        
        Args:
            collection_name: 集合名称
            filter: 查询条件
            update: 更新操作
            upsert: 如果为True，则在没有匹配文档时插入新文档
            
        Returns:
            更新的文档数量
        """
        collection = self.get_collection(collection_name)
        if session:
            result = await collection.update_many(filter, update, upsert=upsert, session=session)
        else:
            result = await collection.update_many(filter, update, upsert=upsert)
        return result.modified_count
    
    async def delete_one(self, collection_name: str, filter: Dict[str, Any]) -> int:
        """删除单个文档
        
        Args:
            collection_name: 集合名称
            filter: 查询条件
            
        Returns:
            删除的文档数量
        """
        collection = self.get_collection(collection_name)
        result = await collection.delete_one(filter)
        return result.deleted_count
    
    async def delete_many(self, collection_name: str, filter: Dict[str, Any], session: Optional[AsyncIOMotorClientSession] = None) -> int:
        """删除多个文档
        
        Args:
            collection_name: 集合名称
            filter: 查询条件
            
        Returns:
            删除的文档数量
        """
        collection = self.get_collection(collection_name)
        if session: 
            result = await collection.delete_many(filter, session=session)
        else:
            result = await collection.delete_many(filter)
        return result.deleted_count
    
    async def count_documents(self, collection_name: str, filter: Dict[str, Any]) -> int:
        """统计文档数量
        
        Args:
            collection_name: 集合名称
            filter: 查询条件
            
        Returns:
            匹配的文档数量
        """
        collection = self.get_collection(collection_name)
        return await collection.count_documents(filter)
        
    async def close(self) -> None:
        """关闭MongoDB连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            logger.info("MongoDB异步连接已关闭")

    async def __aenter__(self):
        self._init_client()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.close()

def init_sync_mongodb(config=None):
    """初始化MongoDB管理器
    
    Args:
        config: 配置对象，默认使用全局settings
        
    Returns:
        MongoDBManager实例
    """
    manager = MongoDBManager(config or settings)
    try:
        return manager
    except Exception as e:
        logger.error(f"初始化同步MongoDB连接失败: {str(e)}")
        raise

# 初始化异步MongoDB管理器
def init_async_mongodb(config=None) -> AsyncMongoDBManager:
    """初始化异步MongoDB管理器
    
    Args:
        config: 配置对象
        
    Returns:
        AsyncMongoDBManager实例
    """
    manager = AsyncMongoDBManager(config or settings)
    # 添加一个简单的操作来测试连接
    try:
        return manager
    except Exception as e:
        logger.error(f"初始化异步MongoDB连接失败: {str(e)}")
        raise

#def get_async_mongodb_manager(app=None) -> AsyncMongoDBManager:
#    """获取MongoDB管理器实例
#    
#    Args:
#        app: FastAPI应用实例
#        
#    Returns:
#        AsyncMongoDBManager实例
#    """
#    if app and hasattr(app.state, "mongodb"):
#        return app.state.mongodb
#
#    # 兼容代码，在没有app参数的情况下，从当前活动的app获取
#    try:
#        from fastapi.applications import active_app
#        if active_app and hasattr(active_app.state, 'mongodb'):
#            return active_app.state.mongodb
#    except (ImportError, AttributeError):
#        pass
#    
#    # 如果无法从app获取，则报错
#    raise ValueError("无法从app.state获取MongoDB管理器，请先创建新实例")