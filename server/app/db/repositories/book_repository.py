from typing import List, Optional, Dict, Any, Union
import re
import logging
from bson.objectid import ObjectId
from sqlalchemy import select, update, desc
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.models.book_info import BookInfo
from app.schemas.enums import BookCategory
from app.db.models.book_content import BookChapter
from app.db.repositories.base_repository import BaseRepository
from app.db.mongodb_manager import AsyncMongoDBManager
from app.core.config import settings

logger = logging.getLogger(__name__)

class BookRepository(BaseRepository):
    """书籍数据访问层"""
    
    def __init__(self, pg_db: Optional[AsyncSession] = None, mongo_db: Optional[AsyncMongoDBManager] = None, redis_db = None):
        """初始化书籍Repository"""
        super().__init__(pg_db, mongo_db, redis_db)
        # mongoDB collection name
        self.collection_name = settings.MONGODB_BOOK_COLLECTION
        if mongo_db:
            self.mongo_collection = mongo_db.get_collection(self.collection_name)
        
    async def get_bookinfo_by_book_id(self, book_id: str) -> Optional[BookInfo]:
        """通过ID获取书籍"""
        self.check_db_dependencies(needs_pg=True)
        
        result = await self.pg_db.execute(select(BookInfo).filter(BookInfo.book_id == book_id))
        return result.scalars().first()

    async def get_chapterlist_by_book_id(self, book_id: Union[str, ObjectId]) -> List[BookChapter]:
        """通过book_id获取书籍的所有章节列表"""
        self.check_db_dependencies(needs_pg=True)
        
        result = await self.pg_db.execute(select(BookChapter).filter(BookChapter.book_id == book_id))
        return result.scalars().all()

    async def list_books(
        self,
        skip: int = 0, 
        limit: int = 10,
        category: Optional[BookCategory] = None,
        is_completed: Optional[bool] = True
    ) -> List[BookInfo]:
        """获取书籍列表"""
        self.check_db_dependencies(needs_pg=True)
        
        # 构建查询条件
        query = select(BookInfo)
        #count_query = select(func.count(BookInfo.id)).select_from(BookInfo)
        
        if category:
            query = query.filter(BookInfo.category == category)
            #count_query = count_query.filter(BookInfo.category == category)
        
        if is_completed is not None:
            query = query.filter(BookInfo.is_completed== is_completed)
            #count_query = count_query.filter(BookInfo.is_published == is_published)
        
        # 排序和分页
        query = query.order_by(BookInfo.category.asc(), desc(BookInfo.updated_at)).offset(skip).limit(limit)
        
        # 执行查询
        result = await self.pg_db.execute(query)
        
        return result.scalars().all()
    
    async def get_chapter_with_content(self, chapter_id: str) -> Optional[Dict[str, Any]]:
        """获取章节详细内容，如果子章节不为空，则获取所有子孙章节"""
        self.check_db_dependencies(needs_mongo=True)

        # 1. 获取目标章节
        target_chapter = await self.mongo_collection.find_one({"chapter_id": chapter_id})
        if not target_chapter:
            return None

        # 2. 构建正则表达式匹配所有后代章节
        target_path = re.escape(target_chapter["path"])
        # 匹配目标路径下的所有子路径（如 /1/2/3, /1/2/4 等）
        subchapters_regex = re.compile(f"^{target_path}\/")

        # 3. 查询所有后代章节（按层级排序）
        subchapters = await self.mongo_collection.find(
            {
                "path": {"$regex": subchapters_regex},
                "book_id": target_chapter["book_id"]
            },
            sort=[("path", 1)]  # 按路径自然顺序排序
        ).to_list(length=None)

        return {
            "chapter": target_chapter,
            "subchapters": subchapters
        }
        
    async def get_subchapters(self, book_id: str, parent_id: str) -> List[Dict[str, Any]]:
        """获取直接子章节"""
        self.check_db_dependencies(needs_mongo=True)
        
        try:
            subchapters = list(self.mongo_collection.find(
                {"book_id": book_id, "parent_id": parent_id},
                sort=[("order", 1)]
            ))
            return subchapters
        except Exception as e:
            logger.error("获取子章节失败: %s", {str(e)})
            return []

    async def update_book_info(self, book_id: str, book_data: Dict[str, Any]) -> Optional[BookInfo]:
        """更新书籍"""
        self.check_db_dependencies(needs_pg=True)
        
        update_stmt = (
            update(BookInfo)
            .where(BookInfo.id == book_id)
            .values(**book_data)
            .returning(BookInfo)
        )
        result = await self.pg_db.execute(update_stmt)
        await self.pg_db.commit()
        return result.scalars().first()
    
    '''
    async def increment_view_count(self, book_id: str) -> bool:
        self.check_db_dependencies(needs_pg=True)
        
        update_stmt = (
            update(BookInfo)
            .where(BookInfo.id == book_id)
            .values(view_count=BookInfo.view_count + 1)
        )
        await self.pg_db.execute(update_stmt)
        await self.pg_db.commit()
        return True
    '''
    