from typing import Optional, Any, Type, TypeVar, ClassVar, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.mongodb_manager import AsyncMongoDBManager
from redis.asyncio import Redis
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T', bound='BaseRepository')

class BaseRepository:
    """Repository基类，提供数据库依赖注入的基础功能"""
    
    # 类实例缓存
    _instances: ClassVar[Dict[str, Any]] = {}
    
    def __init__(
        self,
        pg_db: Optional[AsyncSession] = None,
        mongo_db: Optional[AsyncMongoDBManager] = None,
        redis_db: Optional[Redis] = None
    ):
        """初始化Repository，注入需要的数据库连接
        
        Args:
            pg_db: PostgreSQL数据库会话
            mongo_db: MongoDB数据库实例
            redis_db: Redis数据库连接
        """
        self.pg_db = pg_db
        self.mongo_db = mongo_db
        self.redis_db = redis_db
    
    @classmethod
    def get_instance(cls: Type[T], 
                    pg_db: Optional[AsyncSession] = None,
                    mongo_db: Optional[AsyncMongoDBManager] = None,
                    redis_db: Optional[Redis] = None) -> T:
        """获取Repository实例（单例模式）
        
        Args:
            pg_db: PostgreSQL数据库会话
            mongo_db: MongoDB数据库实例
            redis_db: Redis数据库连接
            
        Returns:
            Repository实例
        """
        instance_key = cls.__name__
        if instance_key not in cls._instances:
            cls._instances[instance_key] = cls(pg_db, mongo_db, redis_db)
        
        # 如果提供了新的数据库连接，则更新实例
        instance = cls._instances[instance_key]
        if pg_db is not None:
            instance.pg_db = pg_db
        if mongo_db is not None:
            instance.mongo_db = mongo_db
        if redis_db is not None:
            instance.redis_db = redis_db
            
        return instance
    
    @classmethod
    def create(cls: Type[T], 
              pg_db: Optional[AsyncSession] = None,
              mongo_db: Optional[AsyncMongoDBManager] = None,
              redis_db: Optional[Redis] = None) -> T:
        """创建新的Repository实例（非单例）
        
        Args:
            pg_db: PostgreSQL数据库会话
            mongo_db: MongoDB数据库实例
            redis_db: Redis数据库连接
            
        Returns:
            Repository实例
        """
        return cls(pg_db, mongo_db, redis_db)
    
    def check_db_dependencies(self, needs_pg: bool = False, needs_mongo: bool = False, needs_redis: bool = False) -> bool:
        """检查必要的数据库依赖是否已经注入
        
        Args:
            needs_pg: 是否需要PostgreSQL连接
            needs_mongo: 是否需要MongoDB连接
            needs_redis: 是否需要Redis连接
            
        Returns:
            依赖检查是否通过
            
        Raises:
            ValueError: 当缺少必要的数据库连接时
        """
        missing = []
        
        if needs_pg and self.pg_db is None:
            missing.append("PostgreSQL")
        if needs_mongo and self.mongo_db is None:
            missing.append("MongoDB")
        if needs_redis and self.redis_db is None:
            missing.append("Redis")
            
        if missing:
            error_msg = f"缺少必要的数据库连接: {', '.join(missing)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        return True
    
    @classmethod
    def from_app(cls: Type[T], app: Any) -> T:
        """从FastAPI应用实例创建Repository
        
        Args:
            app: FastAPI应用实例
            
        Returns:
            Repository实例
        """
        pg_db = getattr(app.state, "postgres", None)
        mongo_db = getattr(app.state, "mongodb", None)
        redis_db = getattr(app.state, "redis", None)
        
        return cls.get_instance(pg_db, mongo_db, redis_db) 