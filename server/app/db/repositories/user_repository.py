from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from sqlalchemy.future import select
from app.db.models.user import User

class UserRepository:
    def __init__(self, session: AsyncSession):
        self.db= session

    async def get_user(self, user_name: Optional[str]=None, email: Optional[str]=None, phone: Optional[str]=None) -> User | None:
        if email:
            query = select(User).where(User.email == email)
        elif phone:
            query = select(User).where(User.phone == phone)
        elif user_name:
            query = select(User).where(User.user_name == user_name)
        else:
            raise ValueError("Either email or phone or user_id must be provided")

        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_users(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """获取用户列表"""
        result = await self.db.execute(select(User).offset(skip).limit(limit))
        return result.scalars().all()

    async def create_user(self, user: User) -> User:
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user
