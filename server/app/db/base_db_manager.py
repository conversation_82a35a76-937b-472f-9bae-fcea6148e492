import logging
from abc import ABC, abstractmethod
from typing import Optional, Any
from contextlib import contextmanager, asynccontextmanager
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from app.core.config import Settings

logger = logging.getLogger(__name__)

class BaseSyncManager(ABC):
    """同步数据库管理器基类"""
    def __init__(self, config: Settings):
        self.config = config
        self.engine = None
        self.SessionLocal = None

    @abstractmethod
    def get_engine(self):
        """获取同步引擎"""
        pass

    @contextmanager
    def get_session(self):
        """同步会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise
        finally:
            session.close()

    def close(self):
        """关闭同步引擎及其连接池"""
        if hasattr(self, 'engine') and self.engine:
            self.engine.dispose()
            self.engine = None
            self.SessionLocal = None

    def __enter__(self):
        self.get_engine()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def get_pool_status(self):
        sync_pool = self.engine.pool
        logger.info(f"当前连接数: {sync_pool.checkedout()}")
        logger.info(f"空闲连接数: {sync_pool.checkedin()}")

class BaseAsyncManager(ABC):
    """异步数据库管理器基类"""
    def __init__(self, config: Settings):
        self.config = config
        self.async_engine = None
        self.AsyncSessionLocal = None

    @abstractmethod
    def get_async_engine(self):
        """获取异步引擎"""
        pass

    @asynccontextmanager
    async def get_session(self):
        """异步会话上下文管理器"""
        async with self.AsyncSessionLocal() as session: 
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise
            finally:
                await session.close()
        
    async def close(self):
        """关闭异步引擎及其连接池"""
        if hasattr(self, 'async_engine') and self.async_engine:
            await self.async_engine.dispose()
            self.async_engine = None
            self.AsyncSessionLocal = None

    async def __aenter__(self):
        self.get_async_engine()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    @abstractmethod
    async def verify_connection(self):
        """验证连接是否可用"""
        pass

    def get_pool_status(self):
        async_pool = self.async_engine.pool
        logger.info(f"当前连接数: {async_pool.checkedout()}")
        logger.info(f"空闲连接数: {async_pool.checkedin()}")
