from app.db.models.base import Base
from app.core.config import settings
from sqlalchemy import create_engine

create_engine(settings.DATABASE_URI).connect()
Base.metadata.create_all(bind=create_engine(settings.DATABASE_URI))

'''测试代码
for table_name, table in Base.metadata.tables.items():
    print(f"{table_name}: {[c.name for c in table.columns]}")
    for constraint in table.constraints:
        print(f"  - constraint: {constraint}")
'''