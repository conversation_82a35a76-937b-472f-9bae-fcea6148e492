from logging.config import fileConfig
import os
import sys
import traceback
import logging

from sqlalchemy import schema, pool, create_engine, text
from sqlalchemy.engine import Connection

from alembic import context

# 配置更详细的日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("alembic")

# 将项目根目录添加到Python路径
# 对于路径结构: /server/app/db/alembic/env.py
# 需要回退4级到server目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, BASE_DIR)

logger.debug(f"BASE_DIR: {BASE_DIR}")

# 确保导入路径正确
try:
    # 导入项目配置
    from app.core.config import settings
    # 首先导入Base
    from app.db.models.base import Base
    # 手动导入所有模型，确保它们已注册到元数据
    from app.db.models.book_info import BookInfo
    from app.db.models.book_content import BookChapter, BookReadingProgress
    logger.debug("成功导入所有模型")
except ImportError as e:
    logger.error(f"导入错误: {e}")
    logger.error(traceback.format_exc())
    raise

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# 使用项目实际的数据库连接字符串覆盖alembic.ini中的设置
# 确保使用同步版本的URL
database_url = settings.DATABASE_URI
# 如果是异步URL，转换为同步URL
if database_url.startswith("postgresql://"):
    sync_url = database_url.replace("postgresql://", "postgresql+psycopg2://")
elif database_url.startswith("postgresql+asyncpg://"):
    sync_url = database_url.replace("postgresql+asyncpg://", "postgresql+psycopg2://")
else:
    sync_url = database_url

config.set_main_option("sqlalchemy.url", sync_url)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# 将Base.metadata设置为目标元数据，以支持自动生成迁移脚本
target_metadata = Base.metadata
logger.debug(f"元数据表数量: {len(target_metadata.tables)}")
logger.debug(f"元数据表: {list(target_metadata.tables.keys())}")

# 其他上下文变量
target_schema = settings.POSTGRES_SCHEMA if hasattr(settings, 'POSTGRES_SCHEMA') else 'public'
logger.debug(f"目标schema: {target_schema}")

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    logger.debug("执行离线迁移")
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_schemas=True,
        version_table_schema=target_schema,
        version_table="alembic_version",
    )

    with context.begin_transaction():
        try:
            # 离线模式下确保schema存在
            context.execute(f'CREATE SCHEMA IF NOT EXISTS {target_schema}')
            context.execute(f'SET search_path TO {target_schema}')
            context.run_migrations()
        except Exception as e:
            logger.error(f"离线迁移错误: {e}")
            logger.error(traceback.format_exc())
            raise


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    logger.debug("执行在线迁移")
    engine_args = {
        'poolclass': pool.NullPool,
        'isolation_level': 'AUTOCOMMIT'  # 设置隔离级别为AUTOCOMMIT
    }
    connectable = create_engine(
        config.get_main_option("sqlalchemy.url"),
        **engine_args
    )

    with connectable.connect() as connection:
        try:
            # 在线模式下确保schema存在
            connection.execute(schema.CreateSchema(target_schema, if_not_exists=True))
            connection.execute(text(f'SET search_path TO {target_schema}'))
            
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                include_schemas=True,
                version_table_schema=target_schema,
                version_table="alembic_version",
            )

            context.run_migrations()
        except Exception as e:
            logger.error(f"配置迁移环境错误: {e}")
            logger.error(traceback.format_exc())
            raise


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

config_args = {
    'transaction_per_migration': True,  # 每个迁移使用单独的事务
    'as_sql': False,  # 直接执行SQL而不是输出
}

