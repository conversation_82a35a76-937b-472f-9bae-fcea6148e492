import datetime
from typing import Optional, List, TYPE_CHECKING
from sqlalchemy import Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship, Mapped, mapped_column

# 直接使用绝对导入
from app.db.models.base import Base

# 类型检查时导入，运行时不导入
if TYPE_CHECKING:
    from app.db.models.book_info import BookInfo
    from app.db.models.user import User

class BookChapter(Base):
    """书籍章节模型"""
    __tablename__ = "book_chapters"
    __table_args__ = (
        UniqueConstraint("chapter_id", name="uq_chapter_id"),  # 显式添加唯一约束, 否则SQLAlchemy在create_all时会报错
    )

    book_id : Mapped[str] = mapped_column(String(24), ForeignKey("books.book_id", ondelete="CASCADE"), nullable=False, index=True)
    title : Mapped[str] = mapped_column(String(255), nullable=False) # 章节标题
    order : Mapped[int] = mapped_column(Integer, nullable=False, index=True)  # 章节顺序
    word_count : Mapped[int] = mapped_column(Integer, default=0)
    
    # 添加MongoDB的ID字段
    chapter_id : Mapped[str] = mapped_column(String(24), nullable=False, index=True, unique=True)
    level : Mapped[int] = mapped_column(Integer, default=1, nullable=False)  # 章节级别
    parent_id : Mapped[Optional[str]] = mapped_column(String(24), ForeignKey("book_chapters.chapter_id", ondelete="CASCADE"), nullable=True)
    path : Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # 章节路径，例如 1/2/3，可以为空
    
    # 关联关系
    book : Mapped["BookInfo"] = relationship("BookInfo", back_populates="chapters")

    parent_chapter: Mapped[Optional["BookChapter"]] = relationship(
        "BookChapter",
        remote_side=[chapter_id], 
        back_populates="subchapters",
    )
    
    subchapters : Mapped[List["BookChapter"]] = relationship(
        "BookChapter", 
        back_populates="parent_chapter",  # 改名避免混淆
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<BookChapter(id={self.id}, book_id={self.book_id}, title='{self.title}', order={self.order})>"


class BookReadingProgress(Base):
    """用户阅读进度模型"""
    __tablename__ = "book_reading_progresses"
    
    # 注意: 确保users表和user_id字段存在
    user_id : Mapped[str] = mapped_column(String(24), ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    book_id : Mapped[str] = mapped_column(String(24), ForeignKey("books.book_id", ondelete="CASCADE"), nullable=False)
    chapter_id : Mapped[str] = mapped_column(String(24), ForeignKey("book_chapters.chapter_id", ondelete="SET NULL"), nullable=False)
    last_read_at : Mapped[datetime.datetime] = mapped_column(DateTime, default=datetime.datetime.now)
    position : Mapped[int] = mapped_column(Integer, default=0)  # 阅读位置（字符偏移量）
    
    __table_args__ = (
        UniqueConstraint('user_id', 'book_id', name='uq_user_book_progress'),
    )
    
    # 关联关系
    user : Mapped["User"] = relationship("User", backref="reading_progresses")
    book : Mapped["BookInfo"] = relationship("BookInfo")
    chapter : Mapped[Optional["BookChapter"]] = relationship("BookChapter")
    
    def __repr__(self):
        return f"<BookReadingProgress(user_id={self.user_id}, book_id={self.book_id}, chapter_id={self.chapter_id})>"
