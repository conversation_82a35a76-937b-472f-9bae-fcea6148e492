from datetime import datetime
from typing import Any

from sqlalchemy import DateTime, Integer
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import Mapped, mapped_column, DeclarativeBase

class Base(DeclarativeBase):
    """
    SQLAlchemy基类，所有模型都应继承自该类。
    提供自动表名生成和通用字段。
    
    设计决策:
    1. 使用自增整数作为主键 - 相比UUID更高效，占用空间更小
    2. 统一使用本地时间 - 更直观，避免时区转换问题
    3. 所有模型继承这些基本字段 - 保持一致性
    """
    
    # 类型注解
    id: Any
    created_at: datetime
    updated_at: datetime
    
    # 自动生成数据库表名（类名小写）
    @declared_attr
    def __tablename__(cls) -> str: # pylint: disable=no-self-argument
        return cls.__name__.lower()
    
    # 统一使用自增整数ID
    # 优点: 性能好，占用空间小，索引效率高
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 统一使用本地时间
    # 使用datetime.now而非datetime.utcnow，确保时间是本地时区
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, nullable=False, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment="更新时间") 
    