from typing import Optional, List, TYPE_CHECKING
from sqlalchemy import String, Boolean, Text, Integer, text
from sqlalchemy.orm import relationship, Mapped, mapped_column 
from sqlalchemy.sql.sqltypes import Enum

# 使用绝对导入
from app.db.models.base import Base
from app.schemas.enums import BookCategory

if TYPE_CHECKING:
    from app.db.models.book_content import BookChapter


class BookInfo(Base):
    """书籍数据模型"""
    __tablename__ = "books"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    # 不需要重新定义id字段，直接使用基类的自增整数id
    title : Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    author : Mapped[str] = mapped_column(String(255), nullable=False)
    cover_image : Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    description : Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 添加MongoDB的ID字段
    book_id : Mapped[str] = mapped_column(String(24), nullable=False, index=True, unique=True)
    
    # 直接使用枚举类型
    category : Mapped[BookCategory] = mapped_column(Enum(BookCategory), nullable=False, default=BookCategory.OTHER)
    
    tags : Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # 存储为逗号分隔的字符串
    is_vip : Mapped[bool] = mapped_column(Boolean, default=False)
    is_completed : Mapped[bool] = mapped_column(Boolean, default=False)
    # 以哪个章节层级为主
    main_chapter_level: Mapped[int] = mapped_column(Integer, default=1, nullable=False, server_default=text('1'))
    
    # 关联关系
    chapters : Mapped[List["BookChapter"]] = relationship("BookChapter", back_populates="book", cascade="all, delete-orphan")
    
    @property
    def word_count(self):
        """计算书籍总字数"""
        return sum(chapter.word_count for chapter in self.chapters) if self.chapters else 0
    
    @property
    def chapter_count(self):
        """获取章节数量"""
        return len(self.chapters) if self.chapters else 0
    
    def __repr__(self):
        return f"<Book(id={self.id}, title='{self.title}', author='{self.author}')>"