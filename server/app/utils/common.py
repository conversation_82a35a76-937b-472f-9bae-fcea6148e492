from uuid import uuid4

def get_uuid() -> str:
    """生成UUID"""
    return uuid4().hex[:24]

def get_time_index(time_zhi: str) -> int:
    """根据时辰获取时间索引"""
    time_index = {
        "子": 0,
        "丑": 1,
        "寅": 2,
        "卯": 3,
        "辰": 4,
        "巳": 5,
        "午": 6,
        "未": 7,
        "申": 8,
        "酉": 9,
        "戌": 10,
        "亥": 11,
    }
    return time_index[time_zhi]