import logging
from typing import AsyncGenerator
from fastapi import Request
from fastapi import HTTPEx<PERSON>, status
import pymongo.errors
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.cache import RedisCache
from app.db.mongodb_manager import AsyncMongoDBManager
logger = logging.getLogger(__name__)

async def get_postgres_session(request: Request) -> AsyncGenerator[AsyncSession, None]:
    """依赖注入：PostgreSQL会话"""
    manager = request.app.state.postgres_manager
    async with manager.get_session() as session:
        try:
            yield session
        except Exception as e:
            if isinstance(e, Exception):
                raise
            logger.error("PostgreSQL会话异常: %s", str(e))
            raise
        finally:
            await session.close()

async def get_mongodb_manager(request: Request) -> AsyncGenerator[AsyncMongoDBManager, None]:
    """依赖注入：MongoDB会话"""
    manager = request.app.state.mongodb_manager
    try:
        yield manager
    except pymongo.errors.OperationFailure as e:
        logger.error("MongoDB操作失败: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="数据库操作未授权"
        ) from e
    except Exception as e:
        if isinstance(e, Exception):
            raise
        logger.error("MongoDB连接异常: %s", str(e))
        raise

async def get_redis_cache(request: Request) -> AsyncGenerator[RedisCache, None]:
    """依赖注入：Redis缓存连接
    
    使用Redis缓存的异步客户端，适用于FastAPI异步环境
    
    Args:
        request: FastAPI请求对象
        
    Yields:
        RedisCache实例
        
    Raises:
        ValueError: 当Redis连接不可用时
    """
    cache = request.app.state.redis_cache
    if not await cache.is_connected():
        logger.error("Redis异步连接不可用")
        raise ValueError("Redis异步连接不可用")
    try:
        yield cache
    except Exception as e:
        if isinstance(e, Exception):
            raise
        logger.error("Redis会话异常: %s", str(e))
        raise

