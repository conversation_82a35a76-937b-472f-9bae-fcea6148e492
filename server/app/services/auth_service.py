# 重构后的 auth.py
import logging
from datetime import timed<PERSON><PERSON>
from fastapi import HTTP<PERSON>x<PERSON>, status
from jose import JW<PERSON><PERSON>r, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.repositories.user_repository import UserRepository
from app.core.config import settings
from app.core.security import verify_password, get_password_hash, create_access_token
from app.db.models.user import User, UserRole
from app.schemas.auth_schemas import RegisterRequest, TokenSchema, LoginRequest, CurrentUser, TokenPayloadSchema
from app.utils.common import get_uuid

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self, db: AsyncSession):
        self.user_repo = UserRepository(db) 

    async def register_user(self, user_in: RegisterRequest) -> User:
        """注册新用户"""
        if user_in.email is None and user_in.phone is None:
            raise ValueError("必须提供邮箱或手机号")

        # 检查邮箱或手机号是否已存在
        if user_in.email:
            existing_user = await self.user_repo.get_user(email=user_in.email)
            if existing_user:
                raise ValueError("邮箱已存在")  
            user_name = user_in.email  # 使用邮箱作为用户名
        if user_in.phone:
            existing_user = await self.user_repo.get_user(phone=user_in.phone)
            if existing_user:
                raise ValueError("手机号已存在")
            user_name = user_in.phone  # 使用手机号作为用户名 

        # 加密密码
        hashed_password = get_password_hash(user_in.password)

        # 生成用户数据
        user_id = get_uuid()
        user = User(
            user_id = user_id,
            email=user_in.email,
            user_name=user_name,    # pylint: disable=possibly-used-before-assignment
            hashed_password=hashed_password,
            phone=user_in.phone,
            is_active=True
        )
    
        await self.user_repo.create_user(user)
        return user

    async def authenticate_user(self, username: str, password: str, login_type: str = None) -> TokenSchema:
        """验证用户, 验证通过后返回新的token"""
        # 根据login_type获取用户，如果没有提供login_type则通过格式判断
        if login_type == 'email':
            user = await self.user_repo.get_user(email=username)
        elif login_type == 'phone':
            user = await self.user_repo.get_user(phone=username)
        else:
            # 向后兼容：如果没有提供login_type，通过格式判断
            if '@' in username:
                user = await self.user_repo.get_user(email=username)
            else:
                user = await self.user_repo.get_user(phone=username)
        
        # 如果用户不存在或密码不正确, 返回异常
        if not user or not verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码不正确",
                headers={"WWW-Authenticate": "Bearer"},
            )
        elif not getattr(user, 'is_active', False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账户已被禁用",
            )
        else:
            access_token = create_access_token(
                subject=user.user_name,
                user_id=user.user_id,
                role=user.role,
                expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            )
            return TokenSchema(access_token=access_token)
    
    def _verify_token(self, token: str) -> TokenPayloadSchema:
        """验证JWT令牌并返回payload"""
        try:
            # jose.jwt.decode 默认会验证exp字段，如果过期会抛出JWTError
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return TokenPayloadSchema(
                sub=payload.get("sub"),
                user_id=payload.get("user_id"),
                role=UserRole(payload.get("role")) if payload.get("role") else None,
                exp=payload.get("exp")
            )
        except JWTError as exc:
            # JWT解码失败，包括过期、签名无效等
            if "expired" in str(exc).lower():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="访问令牌已过期",
                    headers={"WWW-Authenticate": "Bearer"},
                ) from exc
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的访问令牌",
                    headers={"WWW-Authenticate": "Bearer"},
                ) from exc

    async def get_current_user(self, token: str) -> User:
        """获取当前用户完整信息"""
        try:
            payload = self._verify_token(token)
            user_id = payload.user_id
        except Exception as e:
            logger.error("Token 验证失败:%s", str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 获取用户信息
        user = await self.user_repo.get_user(user_id=user_id)
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not getattr(user, 'is_active', False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账户已被禁用",
            )
        return user
    
    async def get_current_user_info(self, token: str) -> CurrentUser:
        """获取当前用户基本信息（轻量级）"""
        try:
            payload = self._verify_token(token)
            user_id = payload.user_id
            user_name = payload.sub
            role = payload.role
            exp_timestamp = payload.exp
        except Exception as e:
            logger.error("Token 验证失败:%s", str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if user_id is None or user_name is None or role is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌信息不完整",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 直接从token中构建CurrentUser，避免数据库查询
        return CurrentUser(
            user_id=user_id,
            user_name=user_name,
            role=role,
            expires_at=exp_timestamp
        )
    
    async def get_current_admin_user(self, token: str) -> User:
        """获取当前管理员用户"""
        # 先获取当前用户
        current_user = await self.get_current_user(token)
        
        # 检查是否为管理员
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足",
            )
        
        return current_user

    async def refresh_token(self, token: str) -> TokenSchema:
        """刷新访问令牌"""
        try:
            # 解码token，允许过期的token用于刷新
            payload = jwt.decode(
                token, 
                settings.SECRET_KEY, 
                algorithms=[settings.ALGORITHM],
                options={"verify_exp": False}  # 跳过过期验证，专门用于刷新
            )
            user_id = payload.get("user_id")
            username = payload.get("sub")
            role_str = payload.get("role")
            
            if not user_id or not username or not role_str:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的刷新令牌",
                )
            
            # 验证用户是否仍然有效
            user = await self.user_repo.get_user(user_name=username)
            if not user or not getattr(user, 'is_active', False):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户账户无效或已被禁用",
                )
            
            role = UserRole(role_str)
            
            # 生成新的访问令牌
            new_access_token = create_access_token(
                subject=username,
                user_id=user_id,
                role=role,
                expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            )
            
            return TokenSchema(access_token=new_access_token)
            
        except JWTError as exc:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌",
            ) from exc
        except Exception as e:
            logger.error("刷新令牌失败, token: %s", token)
            logger.error("刷新令牌失败:%s", str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌失败",
            ) from e

