import logging
from typing import List, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.book_repository import BookRepository
from app.schemas.book_schemas import (
    BookInfoSchema,
    BookChapterInfoSchema,
    BookChapterSectionSchema,
    BookChapterContentSchema
)
from app.schemas.auth_schemas import CurrentUser
from app.core.cache import RedisCache
from app.db.mongodb_manager import AsyncMongoDBManager

logger = logging.getLogger(__name__)

class BookService:
    """书籍服务，提供书籍相关的所有功能"""
    def __init__(
        self,
        db: AsyncSession,
        mongodb: AsyncMongoDBManager,
        cache: RedisCache
    ):
        self.db = db
        self.repository = BookRepository(db, mongodb)
        self.cache = cache
        
    # 书籍列表功能
    async def list_books(
        self,
        skip: int = 0,
        limit: int = 10,
        category: Optional[str] = None,
        is_completed: Optional[bool] = True,
        current_user: Optional[CurrentUser] = None
    ) -> List[BookInfoSchema]:
        """获取书籍列表"""
        cache_key = f"books:list:{skip}:{limit}:{category}:{is_completed}:{current_user.user_id if current_user else 'anonymous'}"
        
        # 尝试从缓存获取
        cached_data = await self.cache.get(cache_key, model=BookInfoSchema)
        if cached_data:
            return cached_data
        try:
            book_lists = await self.repository.list_books(
                skip=skip,
                limit=limit,
                category=category,
                is_completed=is_completed,
            )
            
            result = [BookInfoSchema.model_validate(book) for book in book_lists]
            # 缓存结果
            await self.cache.set(cache_key, result)
            return result
        except Exception as e:
            logger.error("获取书籍列表失败: %s", str({e}))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取书籍列表时发生错误"
            ) from e

    async def get_book_info(self, book_id: int, current_user: Optional[CurrentUser] = None) -> BookInfoSchema:
        """获取书籍基本信息"""
        cache_key = f"books:info:{book_id}:{current_user.user_id if current_user else 'anonymous'}"
        
        # 尝试从缓存获取
        cached_data = await self.cache.get(cache_key, model=BookInfoSchema)
        if cached_data:
            return cached_data
        
        try:
            book = await self.repository.get_bookinfo_by_book_id(book_id)
            if not book:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该书籍"
                )
                
            # 缓存结果
            result = BookInfoSchema.model_validate(book)
            await self.cache.set(cache_key, result.model_dump_json())
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error("获取书籍基本信息失败: %s", {str(e)})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取书籍基本信息时发生错误"
            ) from e

    async def get_chapter_list(self, book_id: int, current_user: Optional[CurrentUser] = None) -> List[BookChapterInfoSchema]:
        """获取书籍章节列表信息"""
        cache_key = f"books:chapter_list:{book_id}:{current_user.user_id if current_user else 'anonymous'}"
        
        # 尝试从缓存获取
        cached_data = await self.cache.get(cache_key, model=BookChapterInfoSchema)
        if cached_data:
            return cached_data
            
        try:
            chapters = await self.repository.get_chapterlist_by_book_id(book_id)
            
            if not chapters:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该书籍章节列表"
                )
                
            # 缓存结果
            result = [BookChapterInfoSchema.model_validate(chapter) for chapter in chapters]
            await self.cache.set(cache_key, result)
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error("获取书籍章节列表失败: %s", {str(e)})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取书籍章节列表时发生错误"
            ) from e
        
    async def get_chapter_content( self, chapter_id: str, current_user: Optional[CurrentUser] = None, is_only_first_sub=False) -> BookChapterContentSchema:
        cache_key = f"books:content:{chapter_id}:{current_user.user_id if current_user else 'anonymous'}"
        
        # 尝试从缓存获取
        cached_data = await self.cache.get(cache_key, model=BookChapterContentSchema)
        if cached_data:
            return cached_data
        
        try:
            # 获取章节内容
            chapter_content = await self.repository.get_chapter_with_content(chapter_id)
            if not chapter_content:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该章节内容"
                )
            result = self._build_chapter_content(chapter_content.get('chapter'), 
                                                 chapter_content.get('subchapters'), 
                                                 is_only_first_sub=is_only_first_sub)  
            
            # 缓存结果
            await self.cache.set(cache_key, result)  # 缓存
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error("获取书籍内容失败: %s", {str(e)})
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取书籍内容时发生错误"
            ) from e

    # 4. 构建章节内容
    def _build_chapter_content(self, cur_chapter, all_chapters, is_only_first_sub=False) -> BookChapterContentSchema:
        subchapters = [c for c in all_chapters if c["parent_id"] == cur_chapter["chapter_id"]]
        content = []
        for item in cur_chapter.get("content"):
            if item.get("type") == "image":
                content.append(BookChapterSectionSchema(
                    type=item.get("type"),
                    content=item.get("url"),
                ))
            elif item.get("type") == "paragraph":
                content.append(BookChapterSectionSchema(
                    type=item.get("type"),
                    content=item.get("text"),
                    quote=item.get("quote"),
                    translation=item.get("trans")
                ))

        end_subchapter_index = 1 if len(subchapters) > 0 and is_only_first_sub else len(subchapters)

        return BookChapterContentSchema(
            chapter_id=cur_chapter["chapter_id"],
            book_id=cur_chapter["book_id"],
            title=cur_chapter["title"],
            level=cur_chapter["level"],
            path=cur_chapter["path"],
            parent_id=cur_chapter['parent_id'],
            content=content,
            subchapters=[self._build_chapter_content(subchapters[i], all_chapters) for i in range(0, end_subchapter_index)]
            #subchapters=[self._build_chapter_content(sub, all_chapters) for sub in subchapters]
        )

    '''    
    async def create_book(self, book_data: BookCreate, current_user: User = Depends(get_current_admin_user)) -> BookOut:
        """创建新书籍（仅管理员）"""
        try:
            # 创建书籍并获取ID
            book = await self.repository.create_book(book_data)
            
            if not book:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="创建书籍失败"
                )
                
            # 清除缓存
            await self._invalidate_book_cache()
            
            return BookOut.from_orm(book)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建书籍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建书籍时发生错误"
            )
        
    async def update_book(self, book_id: int, book_data: BookUpdate, current_user: User = Depends(get_current_admin_user)) -> BookOut:
        """更新书籍（仅管理员）"""
        try:
            # 检查书籍是否存在
            existing_book = await self.repository.get_book_by_id(book_id)
            if not existing_book:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该书籍"
                )
                
            # 更新书籍
            updated_book = await self.repository.update_book(book_id, book_data)
            
            if not updated_book:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="更新书籍失败"
                )
                
            # 清除缓存
            await self._invalidate_book_cache(book_id)
            
            return BookOut.from_orm(updated_book)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新书籍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新书籍时发生错误"
            )
    
    async def update_book_content(
        self, 
        book_id: int, 
        content: Dict[str, Any], 
        current_user: User = Depends(get_current_admin_user)
    ) -> Dict[str, Any]:
        """更新书籍内容（仅管理员）"""
        try:
            # 检查书籍是否存在
            existing_book = await self.repository.get_book_by_id(book_id)
            if not existing_book:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该书籍"
                )
                
            # 更新内容
            result = await self.repository.update_book_content(book_id, content)
            
            if not result:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="更新书籍内容失败"
                )
                
            # 清除缓存
            await self._invalidate_book_cache(book_id)
            
            return {"success": True, "message": "书籍内容已更新"}
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新书籍内容失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新书籍内容时发生错误"
            )
            
    async def delete_book(self, book_id: int, current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
        """删除书籍（仅管理员）"""
        try:
            # 检查书籍是否存在
            existing_book = await self.repository.get_book_by_id(book_id)
            if not existing_book:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到该书籍"
                )
                
            # 删除书籍
            success = await self.repository.delete_book(book_id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="删除书籍失败"
                )
                
            # 清除缓存
            await self._invalidate_book_cache(book_id)
            
            return {"success": True, "message": "书籍已删除"}
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除书籍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除书籍时发生错误"
            )

    async def _invalidate_book_cache(self, book_id: Optional[int] = None):
        """清除书籍相关缓存"""
        try:
            if book_id:
                # 清除特定书籍的缓存
                await self.cache.delete_pattern(f"books:detail:{book_id}:*")
                await self.cache.delete_pattern(f"books:content:{book_id}:*")
            
            # 清除书籍列表缓存
            await self.cache.delete_pattern("books:list:*")
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
            # 这里我们只记录错误，不抛出异常，因为缓存清除失败不应影响主要业务流程 
    '''