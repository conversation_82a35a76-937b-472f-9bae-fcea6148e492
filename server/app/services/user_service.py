from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.repositories.user_repository import UserRepository
from app.db.models.user import User
from app.schemas.auth_schemas import RegisterSchema
from app.core.security import get_password_hash
from app.utils.common import get_uuid

# 用户仓库类方法
async def get_user_by_email(user_repository: UserRepository, email: str) -> Optional[User]:
    """通过邮箱获取用户"""
    result = await user_repository.get_user(email=email)
    return result

async def get_user_by_phone(user_repository: UserRepository, phone: str) -> Optional[User]:
    """通过手机号获取用户"""
    result = await user_repository.get_user(phone=phone)
    return result

async def get_user_by_id(user_repository: UserRepository, user_id: str) -> Optional[User]:
    """通过ID获取用户"""
    result = await user_repository.get_user(user_id=user_id)
    return result

async def create_user(user_repository: UserRepository, user_in: RegisterSchema) -> User:
    """创建新用户"""
    if user_in.email is None and user_in.phone is None:
        raise ValueError("必须提供邮箱或手机号")

    if user_in.email:
        # 检查邮箱是否已存在
        existing_user = await get_user_by_email(user_repository, user_in.email)
        if existing_user:
            raise ValueError("邮箱已存在")
        user_name = user_in.email  # 使用邮箱作为用户名
    else:
        # 检查手机号是否已存在
        existing_user = await get_user_by_phone(user_repository, user_in.phone)
        if existing_user:
            raise ValueError("手机号已存在")
        user_name = user_in.phone  # 使用手机号作为用户名 

    user_id = get_uuid()
    # 创建新用户
    user = User(
        user_id = user_id,
        email=user_in.email,
        user_name=user_name,
        hashed_password=get_password_hash(user_in.password),
        phone=user_in.phone,
        is_active=True
    )
    
    await user_repository.create_user(user)
    return user
