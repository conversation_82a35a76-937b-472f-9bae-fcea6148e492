import logging
from datetime import datetime
from py_iztro import <PERSON><PERSON>, AstrolabeModel
from lunar_python import Lunar
from app.utils.common import get_time_index
from app.core.cache import RedisCache

logger = logging.getLogger(__name__)

class ZiweiService:
    """紫微斗数测算服务, 目前不可用，Astro库存在递归问题"""
    def __init__(self, astro: Astro, cache: RedisCache):
        self.astro = astro
        self.cache = cache

    async def divinate(self, birth_time_solar: datetime,  gender: str) -> AstrolabeModel:
        lunar = Lunar.fromDate(birth_time_solar)
        bazi = lunar.getEightChar()
        time_index = get_time_index(bazi.getTimeZhi())
        date_solar = birth_time_solar.strftime("%Y-%m-%d")
        logger.debug(f"紫微斗数测算参数: 公历日期={date_solar}, 时辰={time_index}, 性别={gender}")

        cache_key = f"ziwei:info:{date_solar}:{time_index}:{gender}"
        
        # 尝试从缓存获取
        # cached_data = await self.cache.get(cache_key, model=AstrolabeModel)
        # if cached_data:
            # return cached_data
        result = self.astro.by_solar(date_solar, time_index, gender)
        #await self.cache.set(cache_key, result)
        return result
