# gunicorn_conf.py
import multiprocessing
import os
from pathlib import Path

# 基本配置
bind = "127.0.0.1:8080"
# 设置进程数（推荐为 CPU 核心数 * 2 + 1，但内存限制时可酌情调整）
workers = 2  # 可以设置为 2~4 之间
worker_class = "uvicorn.workers.UvicornWorker"
timeout = 60
threads = 1

# 日志配置
loglevel = "info"
accesslog = "/var/log/fastapi/access.log"
errorlog = "/var/log/fastapi/error.log"

# PID 文件（可选）
pidfile = "/tmp/fastapi.pid"

# 是否开启访问日志（True 表示开启）
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 守护进程（由 systemd 管理时应禁用）
daemon = False

# 超时设置
timeout = 30
graceful_timeout = 10
