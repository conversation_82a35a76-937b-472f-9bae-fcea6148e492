#!/usr/bin/env python
'''
Author: <PERSON>
Description: 导入书籍数据到数据库
'''
import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path
import json
from typing import Dict, Any, List, Union
import asyncio
from bson.objectid import ObjectId
from pymongo import ASCENDING
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.mongodb_manager import init_async_mongodb, AsyncMongoDBManager
from app.db.postgres_manager import init_async_postgres, AsyncPostgresManager
from app.schemas.enums import BookCategory
from app.utils.common import get_uuid
from app.core.config import settings
from app.db.models.book_info import BookInfo
from app.db.models.book_content import BookChapter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


async def import_book_from_json(
    json_path: Union[str, Path], 
    pg_db: AsyncPostgresManager, 
    mongo_db: AsyncMongoDBManager
) -> Dict[str, Any]:
    """从JSON文件导入书籍数据到MongoDB

    Args:
        json_path: JSON文件路径

    Returns:
        包含导入结果的字典
    """
    try:
        await pg_db.verify_connection()
        await mongo_db.verify_connection()

        # 确保路径是Path对象
        if isinstance(json_path, str):
            json_path = Path(json_path)

        # 检查文件是否存在
        if not json_path.exists():
            error_msg = f"文件不存在: {json_path}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        # 读取JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            book_data = json.load(f)

        # 验证数据格式
        if not isinstance(book_data, dict) or "title" not in book_data or "chapters" not in book_data:
            error_msg = "JSON格式不正确，需要包含title和chapters字段"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        result = await insert_book_chapters(pg_db, mongo_db, book_data)
        return result
    except Exception as e:
        error_msg = f"导入书籍数据失败: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}


async def create_books_index(mongo_db: AsyncMongoDBManager):
    """
    确保 (book_id, path) 联合唯一索引存在
    """
    try:
        collection = mongo_db.get_collection(settings.MONGODB_BOOK_COLLECTION)
        existing_indexes = await collection.index_information()

        book_id_path_index_name = "book_id_path_unique_idx"
        if book_id_path_index_name not in existing_indexes:
            await collection.create_index(
                [("book_id", ASCENDING), ("path", ASCENDING)],
                unique=True,
                name=book_id_path_index_name
            )

        chapter_id_index_name = "chapter_id_unique_idx"
        if chapter_id_index_name not in existing_indexes:
            await collection.create_index(
                [("chapter_id", ASCENDING)],
                unique=True,
                name=chapter_id_index_name
            )

        parent_id_index_name = "parent_id_idx"
        if parent_id_index_name not in existing_indexes:
            await collection.create_index(
                [("parent_id", ASCENDING)],
                name=parent_id_index_name
            )
    except Exception as e:
        logger.error("创建索引失败: %s", {e})
        raise


async def import_books_from_directory(directory_path: Union[str, Path], pg_db: AsyncPostgresManager, mongo_db: AsyncMongoDBManager) -> List[Dict[str, Any]]:
    """从目录中导入所有JSON格式的书籍

    Args:
        directory_path: 包含JSON文件的目录路径

    Returns:
        所有导入结果的列表
    """
    try:
        # 确保路径是Path对象
        if isinstance(directory_path, str):
            directory_path = Path(directory_path)

        # 检查目录是否存在
        if not directory_path.exists() or not directory_path.is_dir():
            error_msg = f"目录不存在: {directory_path}"
            logger.error(error_msg)
            return [{"success": False, "error": error_msg}]

        # 获取所有JSON文件
        json_files = list(directory_path.glob("*.json"))

        if not json_files:
            logger.warning(f"目录中没有找到JSON文件: {directory_path}")
            return []

        # 导入每个文件
        results = []
        for json_file in json_files:
            logger.info(f"正在导入: {json_file}")
            result = await import_book_from_json(json_file, pg_db, mongo_db)
            result["file"] = str(json_file)
            results.append(result)

        # 汇总结果
        success_count = sum(1 for r in results if r.get("success", False))
        logger.info("导入完成: 成功 %d/%d 文件", success_count, len(results))

        return results
    except Exception as e:
        error_msg = f"批量导入书籍数据失败: {str(e)}"
        logger.error(error_msg)
        return [{"success": False, "error": error_msg}]


def extract_all_chapters(chapters: List[Dict[str, Any]], book_id: str, parent_id: str = None, path: str = "") -> List[Dict[str, Any]]:
    """递归提取所有章节"""
    result = []

    for i, chapter in enumerate(chapters):
        current_path = f"{path}/{i+1}" if path else f"/{i+1}"
        chapter_id = get_uuid()
        chapter_doc = {
            "_id": ObjectId(),
            "chapter_id": chapter_id,
            "book_id": book_id,
            "title": chapter.get("title", "未命名章节"),
            "level": chapter.get("level", 1),
            "content": chapter.get("content", []),
            "parent_id": parent_id,
            "path": current_path,
            "order": i + 1,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }

        result.append(chapter_doc)

        if "subchapters" in chapter and chapter["subchapters"]:
            subchapters = extract_all_chapters(
                chapter["subchapters"],
                book_id,
                chapter_id,
                current_path
            )
            result.extend(subchapters)

    return result


async def insert_book_info(pg_session: AsyncSession, book_data: Dict[str, Any]) -> BookInfo:
    """创建书籍"""
    book = BookInfo(**book_data)
    pg_session.add(book)
    await pg_session.commit()
    await pg_session.refresh(book)
    return book


async def insert_book_chapters(pg_db: AsyncPostgresManager, mongo_db: AsyncMongoDBManager, book_data: Dict[str, Any]) -> Dict[str, Any]:
    """插入整本书籍及其章节到数据库中"""

    try:
        book_id = get_uuid()
        book_info = {
            "title": book_data.get("title", "未命名书籍"),
            "author": book_data.get("author", "未知"),
            "category": BookCategory.get_category_from_display_name(book_data.get("category", "其他")),
            "description": book_data.get("description", ""),
            "main_chapter_level": book_data.get("main_chapter_level", 1),
            "book_id": book_id
        }
        # 生成要插入到MongoDB中的具体章节内容
        chapters = extract_all_chapters(
            book_data.get("chapters", []), book_id)

        inserted_count = 0
        if chapters:
            async with pg_db.get_session() as session:
                try:
                    # 插入书籍正文到MongoDB中
                    result = await mongo_db.insert_many(settings.MONGODB_BOOK_COLLECTION, chapters)
                    # 插入书籍信息到数据库中
                    await insert_book_info(session, book_info)
                    # 插入章节信息到数据库中
                    for chapter in chapters:
                        word_count = calculate_word_count(
                            chapter.get("content", []))
                        chapter = BookChapter(
                            book_id=book_id,
                            title=chapter.get("title", "未命名章节"),
                            order=chapter.get("order", 0),
                            chapter_id=chapter.get("chapter_id", ""),
                            level=chapter.get("level", 1),
                            parent_id=chapter.get("parent_id", None),
                            path=chapter.get("path", ""),
                            word_count=word_count,
                        )
                        session.add(chapter)
                        await session.commit()
                        await session.refresh(chapter)
                except Exception as e:
                    logger.error(f"插入章节信息到数据库失败: {str(e)}")
                    raise

                inserted_count = len(result)
                logger.info(
                    f"成功插入书籍 '{book_info['title']}' ID: {book_id} 及 {inserted_count} 个章节")

                return {
                    "book_id": str(book_id),
                    "title": book_info["title"],
                    "inserted_chapters_count": inserted_count,
                    "pg_chapters_count": inserted_count
                }
        else:
            logger.error("没有提取到章节数据")
            return None
    except Exception as e:
        logger.error(f"插入书籍数据失败: {str(e)}")
        raise

# 获取异步数据库生成器
def get_db():
    postgres_manager = init_async_postgres()
    mongo_manager = init_async_mongodb()
    return postgres_manager, mongo_manager


def calculate_word_count(content: List[Dict[str, Any]]) -> int:
    """计算内容的字数

    Args:
        content: 内容列表

    Returns:
        字数
    """
    word_count = 0
    for item in content:
        if isinstance(item, dict):
            # 计算正文
            if "text" in item and item["text"]:
                word_count += len(item["text"])

            # 计算引用
            if "quote" in item and item["quote"]:
                word_count += len(item["quote"])

    return word_count

def guess_book_category(category: str) -> BookCategory:
    """根据书籍标题猜测分类

    Args:
        title: 书籍标题

    Returns:
        猜测的分类
    """
    category_lower = category.lower()

    # 根据关键词分类
    if any(keyword in category_lower for keyword in ['紫微', '斗数']):
        return BookCategory.ZIWEI
    elif any(keyword in category_lower for keyword in ['八字', '子平']):
        return BookCategory.BAZI
    elif any(keyword in category_lower for keyword in ['梅花', '易数']):
        return BookCategory.MEIHUA
    else:
        return BookCategory.OTHER


async def run():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='导入书籍数据到MongoDB')

    # 添加命令行参数
    parser.add_argument('--file', '-f', type=str, help='要导入的JSON文件路径')
    parser.add_argument('--directory', '-d', type=str, help='包含JSON文件的目录路径')
    # parser.add_argument('--colname', '-n', type=str, help='collection名称')

    args = parser.parse_args()

    if args.file and args.directory:
        logger.error("--file和--directory参数不能同时使用")
        sys.exit(1)

    # 获取异步数据库生成器
    pg_db, mongo_db = get_db()

    if args.file:
        # 导入单个文件
        result = await import_book_from_json(args.file, pg_db, mongo_db)
        if result.get("error"):
            logger.error(f"导入失败: {result.get('error')}")
            sys.exit(1)
        elif result is None:
            logger.error(f"未读取到有效可导入的书籍数据")
        else:
            logger.info(
                f"成功导入书籍 '{result['title']}', book ID: {result['book_id']}")
            logger.info(
                f"MongoDB章节数: {result['inserted_chapters_count']}, PostgreSQL章节数: {result.get('pg_chapters_count', 0)}")

    elif args.directory:
        # 导入目录中的所有JSON文件
        results = await import_books_from_directory(args.directory, pg_db, mongo_db)
        success_count = sum(1 for r in results if r.get("success", False))

        if results and success_count > 0:
            logger.info(f"成功导入 {success_count}/{len(results)} 本书籍")

            # 打印成功导入的书籍
            for result in results:
                if result.get("success"):
                    logger.info(
                        f"- '{result['title']}', MongoDB ID: {result['book_id']}, PostgreSQL ID: {result.get('pg_book_id')}")
        else:
            logger.error("没有成功导入任何书籍")
            sys.exit(1)

    else:
        logger.error("请指定--file或--directory参数")
        parser.print_help()
        sys.exit(1)

    # 创建索引
    await create_books_index(mongo_db)
    logger.info("索引创建完成.导入任务完成")


if __name__ == "__main__":
    asyncio.run(run())
