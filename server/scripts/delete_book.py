#!/usr/bin/env python
'''
Author: <PERSON>
Description: 删除指定ID的书籍数据
'''
import argparse
import logging
import sys
from typing import Dict, Any
import asyncio
from app.db.mongodb_manager import init_async_mongodb, AsyncMongoDBManager
from app.db.postgres_manager import init_async_postgres, AsyncPostgresManager
from app.core.config import settings
from app.db.models.book_info import BookInfo
from app.db.models.book_content import BookChapter
from sqlalchemy import select

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def delete_book(book_id: str, pg_db: AsyncPostgresManager, mongo_db: AsyncMongoDBManager) -> Dict[str, Any]:
    """删除指定ID的书籍数据

    Args:
        book_id: 书籍ID

    Returns:
        包含删除结果的字典
    """
    try:
        await pg_db.verify_connection()
        await mongo_db.verify_connection()

        # 从PostgreSQL中删除书籍信息
        async with pg_db.get_session() as session:
            # 删除书籍信息
            book = await session.execute(
                select(BookInfo).where(BookInfo.book_id == book_id)
            )
            book = book.scalar_one_or_none()
            
            if not book:
                error_msg = f"未找到ID为 {book_id} 的书籍"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 删除章节信息
            chapters = await session.execute(
                select(BookChapter).where(BookChapter.book_id == book_id)
            )
            chapters = chapters.scalars().all()
            
            for chapter in chapters:
                await session.delete(chapter)
            
            # 删除书籍信息
            await session.delete(book)
            await session.commit()

        # 从MongoDB中删除章节内容
        collection = mongo_db.get_collection(settings.MONGODB_BOOK_COLLECTION)
        result = await collection.delete_many({"book_id": book_id})

        logger.info(f"成功删除书籍 '{book.title}' ID: {book_id}")
        logger.info(f"PostgreSQL: 删除1本书籍和{len(chapters)}个章节")
        logger.info(f"MongoDB: 删除{result.deleted_count}个章节内容")

        return {
            "success": True,
            "book_id": book_id,
            "title": book.title,
            "deleted_chapters_count": result.deleted_count
        }

    except Exception as e:
        error_msg = f"删除书籍数据失败: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}

# 获取异步数据库生成器
def get_db():
    postgres_manager = init_async_postgres()
    mongo_manager = init_async_mongodb()
    return postgres_manager, mongo_manager

async def run():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='删除指定ID的书籍数据')

    # 添加命令行参数
    parser.add_argument('--book-id', '-b', type=str, required=True, help='要删除的书籍ID')

    args = parser.parse_args()

    # 获取异步数据库生成器
    pg_db, mongo_db = get_db()

    # 删除书籍
    result = await delete_book(args.book_id, pg_db, mongo_db)
    
    if not result.get("success"):
        logger.error(f"删除失败: {result.get('error')}")
        sys.exit(1)
    else:
        logger.info(f"删除成功: {result}")

if __name__ == "__main__":
    asyncio.run(run()) 