# 命理学研究室 - 服务端

本目录包含命理学研究室的后端服务代码，基于FastAPI框架实现。

## 项目结构

```
server/
├── app/                    # 应用主目录
│   ├── api/               # API路由模块
│   ├── core/              # 核心配置
│   ├── db/                # 数据库相关
│   ├── dependencies/      # 依赖注入
│   ├── middleware/        # 中间件
│   ├── schemas/          # 数据模型
│   ├── services/         # 业务服务
│   ├── utils/            # 工具函数
│   ├── __init__.py      # 包初始化
│   └── main.py          # 应用入口
├── tests/                # 测试目录
├── scripts/              # 脚本工具
├── logs/                 # 日志目录
├── alembic.ini          # Alembic配置文件
├── requirements.txt     # 项目依赖
└── README.md           # 项目说明

### 核心目录说明

1. **app/api/**: 
   - API路由和端点定义
   - RESTful接口实现
   - 请求处理和响应封装

2. **app/core/**: 
   - 应用配置管理
   - 安全相关设置
   - 核心功能实现

3. **app/db/**: 
   - 数据库模型定义
   - 数据库会话管理
   - 数据访问层实现

4. **app/dependencies/**: 
   - 依赖注入组件
   - 中间件依赖
   - 共享依赖项

5. **app/middleware/**: 
   - 认证中间件
   - 日志中间件
   - 异常处理中间件

6. **app/schemas/**: 
   - Pydantic模型定义
   - 请求/响应模型
   - 数据验证模型

7. **app/services/**: 
   - 业务逻辑实现
   - 命理计算服务
   - 用户服务等

8. **app/utils/**: 
   - 通用工具函数
   - 辅助方法
   - 工具类

9. **scripts/**: 
   - 部署脚本
   - 数据迁移脚本
   - 维护工具脚本

## 技术栈

- **FastAPI**: Web框架
- **SQLAlchemy**: ORM框架
- **Pydantic**: 数据验证和设置管理
- **Alembic**: 数据库迁移
- **PyJWT**: JWT认证
- **PostgreSQL**: 关系型数据库
- **MongoDB**: 文档型数据库
- **Redis**: 缓存系统

## 功能模块

本服务端包含以下主要功能模块：

1. **用户系统**: 注册、登录、个人信息管理
2. **权限系统**: 基于JWT的身份认证与访问控制
3. **会员系统**: 会员等级、支付管理、权益管理
4. **命理计算**: 八字、紫微斗数、梅花易数的命盘计算引擎
5. **内容管理**: 课程、书籍、案例的存储与管理
6. **数据分析**: 用户行为分析与推荐系统

## 开发指南

### 环境准备

1. 安装Python 3.8+
2. 安装依赖：`pip install -r requirements.txt`
3. 配置环境变量：复制`.env.example`为`.env`并填写配置
4. 初始化数据库：`alembic upgrade head`

### 运行服务

```bash
cd server
uvicorn app.main:app --reload
```

访问 http://localhost:8000/docs 查看API文档

### 接口规范

- 所有API端点使用RESTful设计风格
- 请求和响应数据使用JSON格式
- 错误响应统一格式: `{"detail": "错误信息"}`
- 认证采用Bearer Token方式: `Authorization: Bearer {token}`

## 部署指南

### 生产环境配置

生产环境下，请注意以下事项：

1. 设置强密码和安全密钥
2. 配置HTTPS
3. 启用适当的日志级别
4. 配置数据库连接池
5. 设置合理的资源限制 