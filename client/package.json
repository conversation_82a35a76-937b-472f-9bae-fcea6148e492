{"name": "fate-explorer-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dev": "next dev -H 0.0.0.0", "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "@types/bcrypt": "^5.0.2", "axios": "^1.6.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "iztro": "^2.5.3", "lucide-react": "^0.511.0", "lunar-typescript": "^1.8.0", "next": "14.2.30", "next-auth": "^4.24.8", "next-themes": "^0.2.1", "react": "^18", "react-day-picker": "^9.7.0", "react-dom": "^18", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.47.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.0", "typescript": "^5"}}