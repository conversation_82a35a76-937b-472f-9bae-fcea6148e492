import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    accessToken?: string
    user: {
      id: string
      name?: string 
      role?: string
      image?: string
      expires?: number
    }
    expires?: number
  }

  interface User {
    id: string
    name?: string 
    accessToken?: string
    role?: string
    expires?: number
    image?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string
    userName?: string
    userId?: string
    userRole?: string
    accessTokenExpires?: number
    tokenIssuedAt?: number
    userImage?: string
  }
} 