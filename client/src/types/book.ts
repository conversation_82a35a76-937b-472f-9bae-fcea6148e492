export interface  BookInfo {
    book_id: string
    title: string
    author: string | null
    category: string
    description: string | null
    cover_image: string | null
    has_translation: boolean
    is_completed: boolean
    view_count: number
    created_at: Date
    updated_at: Date
    content_format: string
    main_chapter_level: number | 1
}

// 章节信息接口
export interface ChapterInfo {
    chapter_id: string
    title: string
    level: number
    path: string
    parent_id?: string
    book_id: string
}

// 章节内容段落接口
export interface ChapterSection {
    type: string
    content: string
    quote?: string
    translation?: string
}

// 章节内容接口
export interface ChapterContent {
    book_id: string
    chapter_id: string
    title: string
    level: number
    path: string
    parent_id?: string
    content: ChapterSection[]
    subchapters: ChapterContent[]
}

// 显示模式
export type DisplayMode = "original" | "translation" | "both"
