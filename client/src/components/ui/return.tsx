"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"

export function ReturnButton({ content, url }: { content: string, url: string }) {
    const router = useRouter()
    const handleReturn = () => {
        router.push(url)
    }

    return (
        < div className="items-start w-full" >
            <Button
                variant="ghost"
                className="flex justify-center text-base text-primary font-normal font-serif hover:text-primary hover:bg-paper-hover hover:rounded-md hover:-translate-x-1 transition-all duration-200 ease-in-out py-2"
                onClick={handleReturn}
            >
                <ArrowLeft className="h-4 w-4" />
                {content}
            </Button>
        </div >
    )
}