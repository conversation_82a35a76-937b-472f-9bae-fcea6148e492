// components/TabItem.tsx
import { cn } from '@/lib/utils'; // 使用 clsx 或 tailwind-merge

type TabItemProps = {
    variant?: 'primary' | 'secondary' | 'outline';
    size?: 'sm' | 'md';
    isActive?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

export const TabItem = ({
    variant = 'primary',
    size = 'md',
    className,
    isActive = false,
    ...props
}: TabItemProps) => {
    return (
        <button
            data-state={isActive ? 'active' : 'inactive'}
            className={cn(
                "flex justify-start text-left whitespace-normal break-words, transition-all ",
                // 尺寸系统
                size === 'md' ? 'w-28 gap-2 h-auto min-h-4 py-2 px-4 text-base' : 'w-24 px-3 py-1 text-sm h-auto min-h-3',
                // 变体系统
                variant === 'primary' && [
                'hover:text-primary focus:ring-primary',
                'data-[state=active]:underline-offset-4 data-[state=active]:bg-gradient-to-r from-primary/20 to-transparent',
                'data-[state=active]:border-l-4 data-[state=active]:border-primary data-[state=active]:text-primary'],
                variant === 'secondary' && [
                'hover:text-tertiary focus:ring-tertiary',
                'data-[state=active]:underline-offset-4 data-[state=active]:bg-gradient-to-r from-tertiary/20 to-transparent',
                'data-[state=active]:border-l-4 data-[state=active]:border-tertiary data-[state=active]:text-tertiary'],
                className
            )}
            {...props}
        />
    );
};