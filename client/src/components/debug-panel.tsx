"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { getCacheInfo, clearAllCache, forceRefresh, setupNetworkMonitoring } from '@/lib/cache-utils'

interface DebugInfo {
  userAgent: string
  url: string
  timestamp: string
  cacheInfo: {
    hasCache: boolean
    cacheSize: number
    lastCleared?: string
  }
  networkStatus: string
  errors: Array<{
    timestamp: string
    message: string
    stack?: string
  }>
}

export function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null)
  const [errors, setErrors] = useState<Array<{
    timestamp: string
    message: string
    stack?: string
  }>>([])

  // 只在开发环境显示
  const isDevelopment = process.env.NODE_ENV === 'development'

  useEffect(() => {
    if (!isDevelopment) return

    // 设置网络监控
    setupNetworkMonitoring()

    // 监听错误
    const handleError = (event: ErrorEvent) => {
      const newError = {
        timestamp: new Date().toISOString(),
        message: event.message,
        stack: event.error?.stack
      }
      setErrors(prev => [...prev.slice(-9), newError])
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const newError = {
        timestamp: new Date().toISOString(),
        message: `Unhandled Promise Rejection: ${event.reason}`,
      }
      setErrors(prev => [...prev.slice(-9), newError])
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [isDevelopment])

  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      const cacheInfo = getCacheInfo()
      
      setDebugInfo({
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        cacheInfo,
        networkStatus: navigator.onLine ? 'online' : 'offline',
        errors
      })
    }
  }, [isOpen, errors])

  // 不在开发环境中显示
  if (!isDevelopment) {
    return null
  }

  return (
    <>
      {/* 触发按钮 */}
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg"
        >
          🐛 调试
        </Button>
      </div>

      {/* 调试面板 */}
      {isOpen && (
        <div className="fixed bottom-16 right-4 z-50 w-96 max-h-96 overflow-auto">
          <Card className="shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center justify-between">
                调试信息
                <Button
                  onClick={() => setIsOpen(false)}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-xs">
              {debugInfo && (
                <>
                  {/* 基本信息 */}
                  <div>
                    <div className="font-medium mb-1">基本信息</div>
                    <div className="space-y-1 text-muted-foreground">
                      <div>时间: {new Date(debugInfo.timestamp).toLocaleString()}</div>
                      <div>网络: <Badge variant={debugInfo.networkStatus === 'online' ? 'default' : 'destructive'}>
                        {debugInfo.networkStatus}
                      </Badge></div>
                      <div>URL: {debugInfo.url}</div>
                    </div>
                  </div>

                  {/* 缓存信息 */}
                  <div>
                    <div className="font-medium mb-1">缓存信息</div>
                    <div className="space-y-1 text-muted-foreground">
                      <div>有缓存: {debugInfo.cacheInfo.hasCache ? '是' : '否'}</div>
                      <div>大小: {(debugInfo.cacheInfo.cacheSize / 1024).toFixed(2)}KB</div>
                      {debugInfo.cacheInfo.lastCleared && (
                        <div>上次清除: {new Date(debugInfo.cacheInfo.lastCleared).toLocaleString()}</div>
                      )}
                    </div>
                  </div>

                  {/* 错误列表 */}
                  {errors.length > 0 && (
                    <div>
                      <div className="font-medium mb-1 text-red-600">
                        错误记录 ({errors.length})
                      </div>
                      <div className="space-y-1 max-h-24 overflow-auto">
                        {errors.slice(-3).map((error, index) => (
                          <div key={index} className="p-1 bg-red-50 rounded text-red-700">
                            <div className="text-xs">{new Date(error.timestamp).toLocaleTimeString()}</div>
                            <div className="text-xs">{error.message}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="space-y-2 pt-2 border-t">
                    <Button
                      onClick={clearAllCache}
                      variant="outline"
                      size="sm"
                      className="w-full text-xs"
                    >
                      清除缓存
                    </Button>
                    <Button
                      onClick={forceRefresh}
                      variant="outline"
                      size="sm"
                      className="w-full text-xs"
                    >
                      强制刷新
                    </Button>
                    <Button
                      onClick={() => setErrors([])}
                      variant="ghost"
                      size="sm"
                      className="w-full text-xs"
                    >
                      清除错误记录
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
} 