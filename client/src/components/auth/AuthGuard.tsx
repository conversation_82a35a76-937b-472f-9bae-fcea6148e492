"use client"

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useAuth } from '@/hooks/use-auth'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { data: session, status } = useSession()
  const [isChecking, setIsChecking] = useState(true)
  const [isMounted, setIsMounted] = useState(false)

  // 使用 useAuth hook 处理认证逻辑
  useAuth()

  // 确保组件在客户端挂载
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 当认证状态确定后，停止检查状态
  useEffect(() => {
    if (!isMounted) {
      return
    }

    // 当状态不再是 loading 时，认证检查完成
    if (status !== 'loading') {
      // 添加小延迟，确保 useAuth 有时间处理
      const timer = setTimeout(() => {
        setIsChecking(false)
      }, 100)
      
      return () => clearTimeout(timer)
    }
  }, [status, isMounted])

  // 在服务器端或未挂载时显示加载状态
  if (!isMounted || status === 'loading' || isChecking) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
            <span className="text-gray-600">正在验证身份...</span>
          </div>
        </div>
      )
    )
  }

  // 未认证时显示跳转提示（useAuth 会处理实际跳转）
  if (status === 'unauthenticated' || !session) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-gray-600">正在跳转到登录页...</div>
        </div>
      )
    )
  }

  // 认证通过，渲染子组件
  return <>{children}</>
} 