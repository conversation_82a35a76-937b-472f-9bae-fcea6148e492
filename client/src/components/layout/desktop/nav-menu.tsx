"use client"

import Link from "next/link"
import { usePathname } from 'next/navigation'
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"

const navItems = [
  { label: "首页", href: "/" },
  { label: "八字", href: "/bazi/divinate" },
  { label: "紫微斗数", href: "/ziwei/divinate" },
  { label: "梅花易数", href: "/meihua/divinate" },
  { label: "在线课程", href: "/course" },
  { label: "经典书籍", href: "/book/list" },
  { label: "案例大全", href: "/cases" },
]

function NavItems() {
  const pathname = usePathname()

  return (
    <NavigationMenuList className="flex justify-between gap-8">
      {navItems.map((item, index) => {
        const isActive = pathname === item.href
        return (
          <NavigationMenuItem key={index}>
            <Link href={item.href} legacyBehavior passHref>
              <NavigationMenuLink className={cn(
                "relative group/link text-foreground hover:text-primary transition-all duration-200"
              )}>
                {item.label}
                <span
                  className={cn("pointer-events-none absolute left-0 -bottom-1 h-[2px] w-0 bg-primary transition-all duration-300 group-hover/link:left-0 group-hover/link:w-full group-hover/link:translate-x-0",
                    isActive ? 'w-full left-0 translate-x-0'
                    : 'group-hover/navlink:left-0 group-hover/navlink:w-full group-hover/navlink:translate-x-0'
                  )}
                />
              </NavigationMenuLink>
            </Link>
          </NavigationMenuItem>
        )
      })}
    </NavigationMenuList>
  )
}

export function NavMenu() {
  return (
    <NavigationMenu className="hidden md:flex text-foreground">
      <NavItems />
    </NavigationMenu>
  )
} 