import React from "react"
import Link from "next/link"
import { NavMenu } from "@/components/layout/desktop/nav-menu"
import { UserMenu } from "@/components/layout/desktop/user-menu"

export default function Header() {
  return (
    <nav className="flex w-full relative bg-white shadow-sm">
      <div className="container md:mx-auto w-full md:px-4 sm:px-1 bg-white">
        <div className="flex items-center h-16 relative">
          {/* 左侧标题 - 响应式宽度 */}
          <div className="flex items-center gap-2 w-48 md:w-48 sm:w-auto flex-shrink-0">
            <i className="fa-solid fa-yin-yang text-primary text-2xl"></i>
            <span className="ml-2 text-xl font-semibold text-foreground"><Link href="/">命理学研习社</Link></span>
          </div>
          
          {/* 中间菜单 - 桌面端居中显示，移动端隐藏 */}
          <div className="flex-1 flex justify-center">
            <NavMenu />
          </div>
          
          {/* 右侧用户入口 - 响应式宽度 */}
          <div className="w-32 md:w-32 sm:w-auto flex justify-end flex-shrink-0">
            <UserMenu />
          </div>
        </div>
      </div>
    </nav>
  )
} 