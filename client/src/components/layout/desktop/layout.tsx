import Header from "@/components/layout/desktop/header"
import Footer from "@/components/layout/desktop/footer"


export default function DesktopRootLayout({ children }: { children: React.ReactNode }) {
  console.log('DesktopRootLayout')
  return (
    <div className="w-full flex flex-1 flex-col justify-between items-center">
      <Header />
      <div className="w-full flex flex-1 justify-center">
        <main className="flex flex-1 min-h-full bg-white w-full items-start justify-center">
          {children}
        </main>
      </div>
      <Footer />
    </div>
  )
}