import { Card, CardContent } from "@/components/ui/card"
import { ArrowR<PERSON> } from "lucide-react"

const cards = [
  {
    title: "在线测算",
    content: "系统自动生成命理分析报告或者解卦分析，帮助您探索命运的奥秘。",
    svg: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-primary lucide lucide-calculator-icon lucide-calculator"><rect width="16" height="20" x="4" y="2" rx="2"/><line x1="8" x2="16" y1="6" y2="6"/><line x1="16" x2="16" y1="14" y2="18"/><path d="M16 10h.01"/><path d="M12 10h.01"/><path d="M8 10h.01"/><path d="M12 14h.01"/><path d="M8 14h.01"/><path d="M12 18h.01"/><path d="M8 18h.01"/></svg>, 
    link: "/bazi/divinate"
  },
  {
    title: "在线课程",
    content: "由浅入深的课程体系，文字与视频相结合的教学模式。",
    svg: <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="text-primary size-6">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
  </svg> ,  
    link: "/course"
  },
  {
    title: "经典书籍",
    content: "精选命理经典著作，附带详细注解, 提供原文与白话译文对照阅读。",
    svg: <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="text-primary size-6">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25" />
  </svg>,  
    link: "/book/list"
  },
  {
    title: "案例大全",
    content: "上千真实命理案例数据，帮助理解理论在实际中的应用。",
    svg: <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6 text-primary">
    <path strokeLinecap="round" strokeLinejoin="round" d="m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z" />
  </svg>
,  
    link: "/case"
  }
]

export default function CardGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
      {cards.map((card, index) => (
        <Card
          key={index}
          className="bg-white shadow-md hover:shadow-lg transition-all duration-300 rounded-2xl p-6"
        >
          <CardContent className="p-0 flex flex-col h-full">
            <div className="flex items-center gap-3 mb-4">
              <div className="flex bg-muted rounded-full p-3 w-12 h-12 items-center justify-center">
                {card.svg}
              </div>
              <h3 className="text-xl font-semibold text-foreground">{card.title}</h3>
            </div>
            <p className="text-foreground/80 flex-grow">{card.content}</p>
            <div className="mt-6">
              <a href={card.link} className="text-primary hover:text-accent inline-flex items-center">
                查看更多
                <ArrowRight size={18} strokeWidth={1.5} className="text-primary ml-1" />
              </a>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
