import CardGrid from "@/components/layout/desktop/card-grid"
import { BaziIcon, AstroIcon, MeihuaIcon } from "@/components/icons/basic"

// 桌面端主页内容组件
export default function DesktopHome() {
  const bg_url = 'https://images.unsplash.com/photo-1518655048521-f130df041f66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80'
  
  return (
    <div className="flex flex-1 flex-col items-center bg-background">
      <section className="relative w-full font-sans py-12 md:py-20 lg:py-24 xl:py-26">
       <div className="absolute inset-0 bg-cover bg-center"
               style={{backgroundImage: `linear-gradient(rgba(246,242,234,1.00), rgba(246,242,234,0.7)), url(${bg_url})`}}>
        </div>
        <div className="relative z-10 flex flex-col mb-12 items-center gap-4 text-center">
          <h1 className="text-2xl font-serif text-primary font-bold tracking-tighter sm:text-4xl md:text-4xl lg:text-5xl mb-6">
            探索命理学的奥秘
          </h1>
          <p className="max-w-[700px] text-muted-foreground md:text-xl">
            融合千年智慧与数字科技，提供精准的命理分析与系统学习方案。
          </p>
        </div>
      </section>

      {/* 主要功能区 */}
      <section className="flex flex-col items-center w-full py-12 md:py-20 lg:py-24 bg-paper">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center gap-4 text-center">
            <h2 className="text-2xl font-serif text-accent font-bold tracking-tighter sm:text-3xl md:text-4xl mb-6">
              三大方向
            </h2>
            <p className="max-w-[700px] text-muted-foreground md:text-xl">
            </p>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3">
            {/* 八字 */}
            <div className="flex flex-col items-start gap-3 rounded-lg bg-white p-4 shadow-md transition-all hover:shadow-md">
              <div className="rounded-full p-3">
              <BaziIcon />
              </div>
              <h3 className="text-xl font-bold font-serif">八字命理</h3>
              <p className="text-start text-sm text-muted-foreground">
                探索四柱八字的奥秘，了解天干地支的相互作用，掌握命盘解读的核心要点。
              </p>
            </div>
            {/* 紫微 */}
            <div className="flex flex-col items-start gap-3 rounded-lg bg-white p-4 shadow-md transition-all hover:shadow-md">
              <div className="rounded-full p-3 dark:bg-primary">
                <AstroIcon />
              </div>
              <h3 className="text-xl font-bold font-serif">紫微斗数</h3>
              <p className="text-start text-sm text-muted-foreground">
                解析紫微斗数的星曜配置，研究命宫宫位的影响，理解人生际遇的星象解释。
              </p>
            </div>
            {/* 梅花 */}
            <div className="flex flex-col items-start gap-3 rounded-lg bg-white p-4 shadow-md transition-all hover:shadow-md">
              <div className="rounded-lg p-2 dark:bg-primary">
              <MeihuaIcon />
              </div>
              <h3 className="text-xl font-bold font-serif">梅花易数</h3>
              <p className="text-start text-sm text-muted-foreground">
                掌握梅花易数的占卜技巧，学习卦象解读方法，预测未来走向与变化。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 学习资源区 */}
      <section className="flex flex-col w-full py-12 md:py-20 lg:py-24 bg-paper-secondary items-center justify-center">
        <div className="container max-auto px-4 md:px-6">
          <div className="flex flex-col mb-12 text-accent items-center justify-center gap-4 text-center">
            <h2 className="text-2xl mb-4 font-bold tracking-tighter sm:text-3xl md:text-4xl">
              平台功能特色
            </h2>
            <p className="max-w-2xl max-auto text-muted-foreground md:text-xl">
              提供专业的命理学习体系，从入门到精通，循序渐进。
            </p>
          </div>
        </div>
        <div className="container flex flex-col max-auto max-w-6xl items-center justify-center">
          <CardGrid />
        </div>
      </section>
    </div>
  )
}