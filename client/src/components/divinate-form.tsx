"use client"

import { useState } from "react"
import { BirthInfoForm } from "@/components/birth-info-form"
import { BirthData } from "@/types/user"

interface DivinateFormProps {
  title: string
  // 重定向模式的配置
  targetUrl?: string
  openInNewTab?: boolean
}

export default function DivinateForm({
  title,
  targetUrl,
  openInNewTab = false
}: DivinateFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFormSubmit = async (birthData: BirthData, isSaveCaseDocument: boolean) => {
    setIsLoading(true)
    setError(null)

    try {
      // 创建包含所有数据的对象
      const submitData = {
        ...birthData,
        isSaveCaseDocument
      }

        // URL跳转模式
        if (!targetUrl) {
          throw new Error('目标URL未提供')
        }
        
        // 将数据编码为 URL 参数
        const params = new URLSearchParams()
        params.append('name', submitData.name)
        params.append('gender', submitData.gender)
        params.append('birthDate', submitData.birthDate)
        params.append('birthTime', submitData.birthTime)
        params.append('birthplace', submitData.birthplace)
        params.append('longitude', submitData.longitude.toString())
        params.append('isLunar', submitData.isLunar.toString())
        params.append('isTrueSolarTime', submitData.isTrueSolarTime.toString())
        params.append('isDST', submitData.isDST.toString())
        params.append('isEarlyOrLateNight', submitData.isEarlyOrLateNight.toString())
        params.append('relationship', submitData.relationship)
        params.append('isSaveCaseDocument', isSaveCaseDocument.toString())
        
        // 跳转到结果页面
        const resultUrl = `${targetUrl}?${params.toString()}`
        
        if (openInNewTab) {
          window.open(resultUrl, '_blank')
        } else {
          window.location.href = resultUrl
        }
    } catch (err: any) {
      console.error(`${title}计算失败:`, err)
      setError(err.message || err.response?.data?.detail || "测算失败，请稍后再试")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <BirthInfoForm 
      title={title}
      onSubmit={handleFormSubmit}
      isLoading={isLoading}
      error={error}
    />
  )
} 