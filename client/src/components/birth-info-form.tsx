"use client"

import { useState, FormEvent, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { BirthData, LocationItem } from "@/types/user"
import { ComboBoxResponsive } from "@/components/ui/combobox"
import { isDST } from "@/lib/astro-utils"

interface BirthInfoFormProps {
  title: string
  onSubmit: (birthData: BirthData, isSaveCaseDocument: boolean) => Promise<void>
  isLoading?: boolean
  error?: string | null
}

export function BirthInfoForm({ title, onSubmit, isLoading = false, error = null }: BirthInfoFormProps) {
  const [birthData, setBirthData] = useState<BirthData>({
    name: "",
    gender: "male",
    birthDate: "",
    birthTime: "",
    isLunar: false,
    isTrueSolarTime: true,
    isDST: false,
    isEarlyOrLateNight: false,
    birthplace: "",
    longitude: 0,
    relationship: "other",
  })
  const location = {label: birthData.birthplace, value: birthData.longitude.toString()} as LocationItem
  const [isSaveCaseDocument, setIsSaveCaseDocument] = useState<boolean>(true)
  const [validationError, setValidationError] = useState<string>("")

  // 使用useRef来跟踪用户是否手动修改过isDST
  const hasUserModifiedDST = useRef(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setBirthData((prev) => {
      const newData = {
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }
      // 在日期或时间改变，自动更新isDST
      if ((name === "birthDate" || name === "birthTime") ) {
        newData.isDST = isDST(
          name === "birthDate" ? value : newData.birthDate,
          name === "birthTime" ? value : newData.birthTime
        )
      }

      return newData
    })
    // 清除验证错误
    if (validationError) {
      setValidationError("")
    }
  }

  const handleRadioChange = (name: string, value: string) => {
    setBirthData((prev) => ({
      ...prev,
      [name]: name === "isLunar" ? value === "true" : value,
    }))
  }

  const handleBirthplaceSelect = (location: LocationItem | null) => {
    setBirthData((prev) => ({
      ...prev,
      birthplace: location?.label || "",
      longitude: parseFloat(location?.value || "0"),
    }))
    // 清除验证错误
    if (validationError) {
      setValidationError("")
    }
  }

  const handleCategoryChange = (category: string) => {
    setBirthData((prev) => ({
      ...prev,
      relationship: category as "family" | "classmate" | "friend" | "colleague" | "other",
    }))
  }

  // 定义按钮选项数组
  const genderOptions = [
    { value: 'male', label: '男' },
    { value: 'female', label: '女' }
  ]

  const calendarOptions = [
    { value: 'false', label: '公历' },
    { value: 'true', label: '农历' }
  ]

  const relationshipOptions = [
    { value: 'other', label: '其他' },
    { value: 'family', label: '家人' },
    { value: 'friend', label: '朋友' },
    { value: 'colleague', label: '同事' },
    { value: 'classmate', label: '同学' }
  ]

  // 通用按钮渲染函数
  const renderButton = (
    option: { value: string; label: string }, 
    isSelected: boolean, 
    onClick: () => void,
    className: string = "px-8 py-1"
  ) => (
    <button
      key={option.value}
      type="button"
      aria-label={option.label}
      tabIndex={0}
      onClick={onClick}
      className={`${className} rounded-2xl md:py-1 border transition-all text-sm md:text-base focus:outline-none focus:ring-2 focus:ring-primary ${
        isSelected ? "bg-primary text-white" : "bg-white text-primary"
      }`}
    >
      {option.label}
    </button>
  )

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    
    // 验证出生地是否已选择
    if (!birthData.birthplace || birthData.longitude === 0) {
      setValidationError("请选择出生地点")
      return
    }
    // 验证姓名是否已输入
    if (!birthData.name) {
      setValidationError("请输入名称")
      return
    }
    
    // 清除验证错误并提交
    setValidationError("")
    await onSubmit(birthData, isSaveCaseDocument)
  }

  return (
    <div className="flex gap-8 justify-center items-center">
      <Card className="shadow-lg rounded-2xl px-2 border-0 bg-white py-0 max-w-2xl gap-0">
        <CardHeader className="flex border-b px-0 mx-4 rounded-t-2xl items-center">
          <CardTitle className="text-base md:text-xl font-bold py-3 md:py-6 font-serif text-primary">{title}</CardTitle>
        </CardHeader>
        <CardContent className="pb-2 mb-8 px-4 text-sm md:text-base">
          <form onSubmit={handleSubmit} className="flex flex-col flex-1 w-full gap-2 justify-center items-center">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-6 py-4 md:py-6">
              {/* 姓名 */}
              <div>
                <label htmlFor="name" className="block mb-2">名称 <span className="text-destructive">*</span></label>
                <Input
                  id="name"
                  name="name"
                  value={birthData.name}
                  onChange={handleChange}
                  placeholder="请输入名称"
                  className="rounded-xl py-2 h-10 text-sm md:text-base bg-white border focus:ring-2 focus:ring-primary"
                />
                {validationError && birthData.name === "" && (
                  <p className="text-destructive mt-1">请输入名称</p>
                )}
              </div>

              {/* 性别 */}
              <div>
                <label className="block mb-2">性别</label>
                <div className="flex gap-6 items-center">
                  {genderOptions.map(option => 
                    renderButton(
                      option,
                      birthData.gender === option.value,
                      () => handleRadioChange("gender", option.value)
                    )
                  )}
                </div>
              </div>

              {/* 出生日期和时间 */}
              <div className="flex gap-4 ">
                <div className="flex-1">
                  <label htmlFor="birthDate" className="block mb-2">出生日期 <span className="text-destructive">*</span></label>
                  <Input
                    id="birthDate"
                    name="birthDate"
                    type="date"
                    value={birthData.birthDate}
                    onChange={handleChange}
                    required
                    className="rounded-xl h-8 md:h-10 w-40 bg-white border focus:ring-2 focus:ring-primary text-sm md:text-base"
                  />
                </div>
                <div className="flex-1">
                  <label htmlFor="birthTime" className="block mb-2">出生时间 <span className="text-destructive">*</span></label>
                  <Input
                    id="birthTime"
                    name="birthTime"
                    type="time"
                    value={birthData.birthTime}
                    onChange={handleChange}
                    required
                    className="rounded-xl h-8 md:h-10 bg-white border focus:ring-2 focus:ring-primary text-sm md:text-base"
                  />
                </div>
              </div>

              {/* 出生地点 */}
              <div>
                <label htmlFor="birthplace" className="block mb-2">出生地址 <span className="text-destructive">*</span></label>
              <ComboBoxResponsive
                placeholder="请选择出生地"
                jsonUrl="/assets/location.json"
                onSelect={handleBirthplaceSelect}
                value={location}
              />
              {validationError && birthData.birthplace === "" && (
                <p className="text-sm text-destructive mt-1">请选择出生地</p>
              )}
              </div>

              {/* 历法选择 */}
              <div>
                <label className="block mb-2">历法</label>
                <div className="flex gap-6">
                  {calendarOptions.map(option => 
                    renderButton(
                      option,
                      (option.value === 'true') === birthData.isLunar,
                      () => handleRadioChange("isLunar", option.value)
                    )
                  )}
                </div>
              </div>

              {/* 复选框组和Switch */}
              <div>
                <label className="block mb-2"> 特殊时制</label>
                <div className="flex flex-wrap gap-4 items-center h-8">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      id="isDST"
                      name="isDST"
                      type="checkbox"
                      checked={birthData.isDST}
                      onChange={handleChange}
                      className="h-4 w-4 accent-primary border-primary rounded focus:ring-primary"
                    />
                    <span className="text-muted-foreground">夏令时</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      id="isTrueSolarTime"
                      name="isTrueSolarTime"
                      type="checkbox"
                      checked={birthData.isTrueSolarTime}
                      onChange={handleChange}
                      className="h-4 w-4 accent-primary border-primary rounded focus:ring-primary"
                    />
                    <span className="text-muted-foreground">真太阳时</span>
                  </label>
                  {/* 早晚子时暂时不支持 */}
                  {/* <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      id="isEarlyOrLateNight"
                      name="isEarlyOrLateNight"
                      type="checkbox"
                      checked={birthData.isEarlyOrLateNight}
                      onChange={handleChange}
                      className="h-4 w-4 accent-primary border-primary rounded focus:ring-primary"
                    />
                    <span className="text-muted-foreground">早晚子时</span>
                  </label> */}
                </div>
              </div>

            </div>
            <div className="flex flex-col flex-1 w-full gap-4 md:gap-6 justify-center items-center mb-4 md:mb-6">
              {/* 案例分类 */}
                <div className="flex flex-col flex-1 w-full gap-2 md:gap-6 justify-center items-start">
                <div>
                  <label className="block mb-2">案例分类</label>
                  <div className="flex flex-wrap gap-2 md:gap-6">
                    {relationshipOptions.map(option => 
                      renderButton(
                        option,
                        birthData.relationship === option.value,
                        () => handleCategoryChange(option.value),
                        "px-6 py-1"
                      )
                    )}
                  </div>
                </div>
              </div>
              {/* 保存档案 */}
              <div className="flex items-center gap-4 w-full">
                <Label className="text-base font-normal">保存档案</Label>
                <Switch id="isSaveCaseDocument" checked={isSaveCaseDocument} onCheckedChange={setIsSaveCaseDocument} />
              </div>
            </div>

            {/* 开始测算按钮 */}
            <Button type="submit" disabled={isLoading} className="w-32 mt-2 rounded-2xl h-10 text-base">
              {isLoading ? "计算中..." : "开始测算"}
            </Button>
            {error && <p className="text-sm text-destructive">{error}</p>}
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 