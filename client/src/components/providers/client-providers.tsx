"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/client-error-handler"
import { SyntaxErrorDetector } from "@/components/syntax-error-detector"
import { ResourceMonitor, NetworkMonitor } from "@/components/resource-monitor"
import { DebugPanel } from "@/components/debug-panel"

export function ClientProviders() {
  return (
    <>
      <ClientErrorHandler />
      <SyntaxErrorDetector />
      <ResourceMonitor />
      <NetworkMonitor />
      {/* <DebugPanel /> */}
    </>
  )
} 