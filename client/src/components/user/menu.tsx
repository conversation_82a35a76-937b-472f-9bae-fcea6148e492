"use client"
import { Avatar, AvatarImage } from "@/components/ui/avatar"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useRouter } from "next/navigation"
import { signOut } from "next-auth/react"

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' })
  }


export function DropdownMenuDemo({ image }: { image: string | null }) {
    const router = useRouter()
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Avatar className="hover:scale-125 transition-transform duration-150 cursor-pointer data-[state=open]:scale-125">
                    <AvatarImage src={image || `/avatars/default-avatar.png`} alt="用户头像" />
                </Avatar>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-24" align="start" side="bottom">
                <DropdownMenuItem className="text-base" onClick={() => router.push('/user/profile')}>
                    我的账户
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base" onClick={handleSignOut}>
                    退出登录
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
