import * as React from "react"

import { useEffect, useState } from 'react'

export function useIsMobile(breakpoint = 768) {
  // 初始值设为false，避免SSR时的window未定义错误
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia(`(max-width: ${breakpoint - 1}px)`)

    const handleChange = () => {
      setIsMobile(mediaQuery.matches)
    }

    // 立即设置初始值
    setIsMobile(mediaQuery.matches)

    // Prefer matchMedia if available
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    } else {
      // Fallback for older browsers
      const resizeHandler = () => {
        setIsMobile(window.innerWidth < breakpoint)
      }
      window.addEventListener('resize', resizeHandler)
      return () => window.removeEventListener('resize', resizeHandler)
    }
  }, [breakpoint])

  return isMobile
}
