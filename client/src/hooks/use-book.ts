"use client"

import { useState, useEffect } from "react"
import { booksApi } from "@/lib/api-client"
import { useError<PERSON>and<PERSON> } from "@/lib/error-handler"
import { BookInfo, ChapterContent, ChapterInfo } from "@/types/book"

// 书籍列表Hook
export function useBookList({
  page = '1',
  size = '10',
  is_completed = 'true',
  category,
}: {
  page?: string
  size?: string
  is_completed?: string
  category?: string
} = {}) {
  const { handleError } = useErrorHandler()
  
  const [data, setData] = useState<{
    books: BookInfo[]
    total: number
    totalPages: number
  }>({
    books: [],
    total: 0,
    totalPages: 0,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBooks = async () => {
    setLoading(true)

    try {
      const response = await booksApi.getBooks({
        page,
        size,
        is_completed,
        category,
      }) as {
        items: BookInfo[]
        total: number
        page: number
        page_size: number
        total_pages: number
      }

      setData({
        books: response.items,
        total: response.total,
        totalPages: response.total_pages,
      })
    } catch (err) {
      // 使用统一的错误处理
      const errorResult = handleError(err, '获取书籍列表')
      setError(errorResult.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBooks()
  }, [page, size, is_completed, category])

  return {
    books: data.books,
    total: data.total,
    totalPages: data.totalPages,
    loading,
    error,
    refetch: fetchBooks,
  }
}

// 书籍详情Hook
export function useBookDetail(book_id: string) {
  const { handleError } = useErrorHandler()
  
  const [bookInfo, setBookInfo] = useState<BookInfo | null>(null)
  const [chapters, setChapters] = useState<ChapterInfo[]>([])
  const [firstChapterContent, setFirstChapterContent] = useState<ChapterContent | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBookDetail = async () => {
    if (!book_id) return

    setLoading(true)
    setError(null)

    try {
      const response = await booksApi.getBookDetail(book_id) as {
        chapter_list: ChapterInfo[]
        content?: ChapterContent
      }
      
      setChapters(response.chapter_list)
      setFirstChapterContent(response.content || null)
    } catch (err) {
      // 使用统一的错误处理
      const errorResult = handleError(err, `获取书籍详情: ${book_id}`)
      setError(errorResult.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBookDetail()
  }, [book_id])

  return {
    bookInfo,
    chapters,
    firstChapterContent,
    loading,
    error,
    refetch: fetchBookDetail,
  }
}

// 章节内容Hook
export function useChapterContent(chapter_id: string) {
  const { handleError } = useErrorHandler()
  
  const [content, setContent] = useState<ChapterContent | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchChapterContent = async () => {
    if (!chapter_id) return

    setLoading(true)
    setError(null)

    try {
      const response = await booksApi.getChapterContent(chapter_id) as {
        chapters: ChapterContent
      }
      
      setContent(response.chapters)
    } catch (err) {
      // 使用统一的错误处理
      const errorResult = handleError(err, `获取章节内容: ${chapter_id}`)
      setError(errorResult.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchChapterContent()
  }, [chapter_id])

  return {
    content,
    loading,
    error,
    refetch: fetchChapterContent,
  }
} 