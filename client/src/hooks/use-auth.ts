// hooks/use-auth.ts
"use client"
import { useEffect, useCallback, useRef, useState, useMemo } from "react"
import { signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { usePathname, useSearchParams } from "next/navigation"

// 认证状态检查 Hook
export function useAuth() {
  const { data: session, status } = useSession()
  const pathname = usePathname()
  const router = useRouter()
  const hasRedirectedRef = useRef(false)
  const [isMounted, setIsMounted] = useState(false)

  // 只在客户端挂载后才获取 searchParams，避免 SSR 错误
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null)

  // 使用 useMemo 优化 callbackUrl 计算
  const callbackUrl = useMemo(() => {
    if (!searchParams || !pathname) return encodeURIComponent('/')
    
    const currentUrl = searchParams.toString() 
      ? `${pathname}?${searchParams.toString()}`
      : pathname
    return encodeURIComponent(currentUrl)
  }, [pathname, searchParams])

  // 确保在客户端环境
  useEffect(() => {
    setIsMounted(true)
    // 安全地获取 searchParams
    if (typeof window !== 'undefined') {
      try {
        setSearchParams(new URLSearchParams(window.location.search))
      } catch (error) {
        console.warn('获取 searchParams 失败:', error)
        setSearchParams(new URLSearchParams())
      }
    }
  }, [])

  // 使用 useCallback 稳定化函数，避免无限循环
  const handleTokenExpiry = useCallback(async (callbackUrl: string) => {
    if (hasRedirectedRef.current) return // 防止重复执行
    hasRedirectedRef.current = true

    try {
      console.log("Token已过期，执行登出")
      
      // 清理本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('nextauth.message')
        sessionStorage.clear()
      }

      // 执行登出操作
      await signOut({
        redirect: false,
      })

      // 跳转到登录页
      router.push(`/login?callbackUrl=${callbackUrl}`)
    } catch (error) {
      console.error('登出失败:', error)
      // 如果登出失败，强制跳转
      router.push(`/login?callbackUrl=${callbackUrl}`)
    }
  }, [router])

  // 优化认证检查逻辑，减少不必要的处理
  useEffect(() => {
    // 只在客户端挂载后执行
    if (!isMounted) {
      return
    }

    // 如果还在加载中，等待
    if (status === 'loading') {
      return
    }

    // 如果已经重定向过，不再执行
    if (hasRedirectedRef.current) {
      return
    }

    // 如果当前已经在登录页，不需要重复跳转
    if (pathname === '/login') {
      return
    }

    // 如果未认证，跳转到登录页
    if (status === 'unauthenticated' || !session) {
      hasRedirectedRef.current = true
      router.push(`/login?callbackUrl=${callbackUrl}`)
      return
    }

    // 如果已认证，检查token是否过期（优化：减少频繁检查）
    if (session && (session as any).expires) {
      const expires = new Date((session as any).expires).getTime()
      const now = Date.now()
      
      // 提前5分钟判断为过期，减少频繁的过期检查
      const isExpired = now >= (expires - 5 * 60 * 1000)

      if (isExpired) {
        handleTokenExpiry(callbackUrl)
        return
      }
    }

    // 重置重定向标志（当认证状态正常时）
    hasRedirectedRef.current = false
  }, [session, status, pathname, router, handleTokenExpiry, isMounted, callbackUrl])

  // 当路由发生变化时，重置重定向标志
  useEffect(() => {
    hasRedirectedRef.current = false
  }, [pathname])

  // 返回认证状态，方便组件使用
  return {
    isAuthenticated: status === 'authenticated' && !!session,
    isLoading: status === 'loading' || !isMounted,
    session
  }
}
