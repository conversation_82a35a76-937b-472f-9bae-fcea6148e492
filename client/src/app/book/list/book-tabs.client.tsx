"use client"

import { ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"
import { VTab<PERSON>, VTabsList, VTabsTrigger } from "@/components/ui/tabs-vertical"

interface BookTabsClientProps {
  categories: { key: string, label: string }[]
  children: ReactNode
  defaultCategory: string
}

export default function BookTabsClient({ categories, children, defaultCategory }: BookTabsClientProps) {
  const router = useRouter()
  const pathname = usePathname()

  const handleValueChange = (value: string) => {
    // 只在客户端构建新的URL
    if (typeof window !== 'undefined') {
      // 构建新的URL，保留其他查询参数
      const searchParams = new URLSearchParams(window.location.search)
      searchParams.set('category', value)
      const newUrl = `${pathname}?${searchParams.toString()}`
      
      // 使用 replace 而不是 push，这样不会在历史记录中创建新条目
      router.replace(newUrl, { scroll: false })
    }
  }

  return (
    <VTabs 
      defaultValue={defaultCategory} 
      orientation="vertical" 
      className="container flex flex-1 min-h-full gap-8 py-8"
      onValueChange={handleValueChange}
    >
      <VTabsList className="flex flex-col items-center px-4 py-4">
        {categories.map(cat => (
          <VTabsTrigger key={cat.key} value={cat.key} className="w-full text-lg px-4 justify-start capitalize">
            {cat.label}
          </VTabsTrigger>
        ))}
      </VTabsList>
      <div className="flex flex-1 py-4 justify-start bg-white rounded-lg shadow-sm items-start">
        {children}
      </div>
    </VTabs>
  )
}