import { Suspense } from "react"
import BookTabsClient from "./book-tabs.client"
import { BookTabsSkeleton, BookPanelSkeleton } from "./book-list-skeleton"
import { BookPanel } from "./book-list-panel"
import { VTabsContent } from "@/components/ui/tabs-vertical"

const CATEGORY_MAP = [
  { key: "bazi", label: "八字" },
  { key: "ziwei", label: "紫微斗数" },
  { key: "meihua", label: "梅花易数" },
]

interface BookListPageProps {
  searchParams: { category?: string }
}

export default function BookListPage({ searchParams }: BookListPageProps) {
  // 从URL参数获取分类，如果没有则使用第一个分类
  const categoryFromUrl = searchParams.category || CATEGORY_MAP[0].key

  return (
    <div className="flex flex-col flex-1 items-center justify-center bg-paper-secondary min-h-full w-full">
          <Suspense fallback={<BookTabsSkeleton />}>
            <BookTabsClient
              categories={CATEGORY_MAP}
              defaultCategory={categoryFromUrl}
            >
              {CATEGORY_MAP.map(category => (
                <VTabsContent key={category.key} value={category.key}>
                  <Suspense fallback={<BookPanelSkeleton />}>
                    <BookPanel category={category.key} />
                  </Suspense>
                </VTabsContent>
              ))}
            </BookTabsClient>
          </Suspense>
    </div>
  )
}
