"use client"

import { BookCard } from "./book-card"
import { VTabsContent } from "@/components/ui/tabs-vertical"
import { useBookList } from "@/hooks/use-book"
import { BookPanelSkeleton } from "./book-list-skeleton"

interface BookPanelProps {
  category: string
}

export function BookPanel({ category }: BookPanelProps) {
  // 使用新的Hook获取指定分类的书籍
  const { books, loading, error } = useBookList({
    page: '1',
    size: '100',
    is_completed: 'false',
    category: category,
  })

  return (
    <VTabsContent value={category}>
      {loading ? (
        <BookPanelSkeleton />
      ) : error ? (
        <div className="text-center text-red-500 py-20">
          加载失败: {error}
        </div>
      ) : books.length === 0 ? (
        <div className="text-center text-gray-400 py-20">暂无书籍</div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 py-4 px-8">
          {books.map(book => (
            <BookCard key={book.book_id} {...book} />
          ))}
        </div>
      )}
    </VTabsContent>
  )
}