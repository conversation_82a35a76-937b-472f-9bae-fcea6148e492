"use client"

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import BookIntroduction from "@/app/book/detail/book-intro"
import { ChapterInfo, ChapterContent, DisplayMode } from "@/types/book"

interface BookContentProps {
    title: string
    author: string | null
    description: string | null
    chapterList: ChapterInfo[]
    currentChapter: ChapterContent | null
    displayMode: DisplayMode
    setDisplayMode: (mode: DisplayMode) => void
}

export function BookContent({ 
    title, 
    author = "佚名", 
    description = "暂无简介", 
    currentChapter, 
    displayMode, 
    setDisplayMode 
}: BookContentProps) {
    return (
        <div className="flex-1">
            <div className="bg-white rounded-lg shadow-sm p-8">
                {!currentChapter ? (
                    <BookIntroduction title={title} author={author} description={description} />
                ) : (
                    <>
                        {/* 显示模式切换 */}
                        <RadioGroup
                            value={displayMode}
                            onValueChange={(value) => setDisplayMode(value as DisplayMode)}
                            className="flex gap-2 mb-8"
                        >
                            <div className="flex items-center">
                                <RadioGroupItem value="original" id="original">
                                    显示原文
                                </RadioGroupItem>
                            </div>
                            <div className="flex items-center">
                                <RadioGroupItem value="translation" id="translation">
                                    显示译文
                                </RadioGroupItem>
                            </div>
                            <div className="flex items-center">
                                <RadioGroupItem value="both" id="both">
                                    同时显示
                                </RadioGroupItem>
                            </div>
                        </RadioGroup>

                        {/* 内容展示 */}
                        <article className="space-y-6 leading-relaxed">
                            {displayMode === "both" ? (
                                <div className="space-y-8">
                                    {currentChapter.content.map((section, index) => (
                                        <div key={index} className="space-y-4">
                                            <div className="space-y-2 px-4">
                                                <div className="text-foreground font-kai">
                                                    <p className="text-lg">{section.content}</p>
                                                    {section.quote && (
                                                        <p className="text-base text-foreground mt-2">{section.quote}</p>
                                                    )}
                                                </div>
                                            </div>
                                            {section.translation && (
                                                <div className="space-y-2 pl-4 bg-paper-secondary/70 rounded-lg p-4 border-primary/20">
                                                    <div className="text-foreground">
                                                        <p className="text-base">{section.translation}</p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : displayMode === "original" ? (
                                <div className="space-y-4">
                                    <h3 className="font-serif text-lg text-secondary">原文</h3>
                                    <div className="text-foreground font-kai space-y-6">
                                        {currentChapter.content.map((section, index) => (
                                            <div key={index}>
                                                <p className="text-lg">{section.content}</p>
                                                {section.quote && (
                                                    <p className="text-base text-foreground mt-2">{section.quote}</p>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <h3 className="font-serif text-lg text-secondary">译文</h3>
                                    <div className="text-foreground space-y-6">
                                        {currentChapter.content.map((section, index) => (
                                            <div key={index}>
                                                {section.translation && <p>{section.translation}</p>}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </article>
                    </>
                )}
            </div>
        </div>
    )
}