"use client"
import BookDetailClient from "@/app/book/detail/book-detail.client"
import { AuthProtected } from "@/components/auth/AuthProtected"

interface BookDetailPageProps {
    searchParams: { book_id?: string }
}

export default function BookDetailPage({ searchParams }: BookDetailPageProps) {
    const book_id = searchParams.book_id || ""
    return (
            <AuthProtected>
                <BookDetailClient book_id={book_id} />
            </AuthProtected>
    )
}