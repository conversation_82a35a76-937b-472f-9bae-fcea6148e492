import '@/styles/globals.css'
import type { Metadata } from 'next'
import { cn } from "@/lib/utils"
import { fontSans } from '@/app/fonts'
import { AuthProvider } from '@/components/providers/auth-provider'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { ClientProviders } from '@/components/providers/client-providers'
import HeadMeta from '../components/layout/head-meta'
import DesktopLayout from '../components/layout/desktop/layout'
import { headers } from 'next/headers'

export const metadata: Metadata = {
  title: '命理学研习社 - 命理爱好者的研究与学习平台',
  description: '专业的命理学教育平台，提供八字、紫微斗数、梅花易数在线测算、课程学习、经典书籍和案例解析',
  keywords: '命理学,玄学,算命,八字,紫微斗数,梅花易数,在线测算,命理学课程,古籍阅读',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
   const deviceType = headers().get('x-device-type') || 'desktop'

  return (
    <html lang="zh" suppressHydrationWarning className={fontSans.variable}>
      <head>
        <HeadMeta />
      </head>
      <body className={cn(
        'min-h-screen h-full bg-background text-foreground',
        deviceType === 'mobile'
          ? 'overflow-x-hidden supports-[height:100dvh]:min-h-[100dvh]'
          : 'flex bg-white'
      )}>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <DesktopLayout>{children}</DesktopLayout>
          </ThemeProvider>
        </AuthProvider>
        <ClientProviders />
      </body>
    </html>
  )
} 