import BaziDivinate from "./page.client"
import { checkAuth } from "@/lib/auth/check-auth"

export default async function BaziDivinatePage() {
  await checkAuth("/bazi/divinate")
  return (
    <div className="flex w-full bg-paper-secondary justify-center">
      <div className="container flex mx-auto px-4 py-4 md:py-8 items-start md:items-center justify-center">
        <BaziDivinate />
      </div>
    </div>
  )
} 