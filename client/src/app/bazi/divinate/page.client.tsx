"use client"

import { useState } from "react"
import { BaziResult } from "@/types/divinate_result"
import DivinateForm from "@/components/divinate-form"

export default function BaziDivinate() {
  const [result, setResult] = useState<BaziResult | null>(null)

  const handleResult = (resultData: BaziResult) => {
    setResult(resultData)
  }

  return (
    // <>
    //   <DivinateForm 
    //     title="八字命盘测算"
    //     targetUrl="/bazi/result"
    //     openInNewTab={false}
    //   />
    //   {/* 这里可以添加结果显示组件 */}
    //   {result && (
    //     <div className="mt-8">
    //       <h3 className="text-lg font-semibold mb-4">测算结果</h3>
    //       <pre className="bg-gray-100 p-4 rounded-lg overflow-auto">
    //         {JSON.stringify(result, null, 2)}
    //       </pre>
    //     </div>
    //   )}
    // </>
    <div >敬请期待</div>
  )
} 