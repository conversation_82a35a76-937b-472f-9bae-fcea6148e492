"use client"
import { UserProfile } from "@/components/user/profile"
import { AuthProtected } from "@/components/auth/AuthProtected"


export default function ProfilePage() {
  // const session = await getServerSession(authOptions)
  // if (!session) {
  //   redirect('/login?callbackUrl=/user/profile')
  // }
  
  return (
    <div className="flex flex-col flex-1 items-center justify-center bg-paper-secondary min-h-full w-full">
      <AuthProtected>
            <UserProfile />
      </AuthProtected>
    </div>
  )
} 