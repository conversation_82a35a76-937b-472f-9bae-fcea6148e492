"use client"

import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { User, Settings, History, Heart, BookOpen, Star, LogOut, LogIn } from "lucide-react"
import Link from 'next/link'
import { signOut } from 'next-auth/react'

const menuItems = [
  {
    icon: History,
    label: '历史记录',
    href: '/user/history',
    description: '查看测算和学习记录'
  },
  {
    icon: Heart,
    label: '我的收藏',
    href: '/user/favorites',
    description: '收藏的课程和案例'
  },
  {
    icon: BookOpen,
    label: '学习进度',
    href: '/user/progress',
    description: '课程学习进度跟踪'
  },
  {
    icon: Star,
    label: '我的评价',
    href: '/user/reviews',
    description: '对课程和内容的评价'
  },
  {
    icon: Settings,
    label: '设置',
    href: '/user/settings',
    description: '个人设置和偏好'
  }
]

export default function UserPage() {
  const { data: session, status } = useSession()

  const handleSignOut = async () => {
    await signOut({ redirect: false })
    window.location.href = '/login'
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">正在加载...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="flex flex-col min-h-screen p-4">
        <div className="flex-1 flex flex-col items-center justify-center">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center mb-4 mx-auto">
              <User className="w-10 h-10 text-muted-foreground" />
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-2">欢迎来到命理学研习社</h2>
            <p className="text-sm text-muted-foreground">登录后可以保存学习记录和个人偏好</p>
          </div>
          
          <div className="w-full max-w-sm space-y-3">
            <Link href="/login" className="block">
              <Button className="w-full" size="lg">
                <LogIn className="w-4 h-4 mr-2" />
                立即登录
              </Button>
            </Link>
            <Link href="/register" className="block">
              <Button variant="outline" className="w-full" size="lg">
                注册账号
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-background overflow-auto">
      {/* 用户信息卡片 */}
      <div className="p-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <Avatar className="w-16 h-16">
                <AvatarImage 
                  src={session.user.image || '/avatars/default-avatar.png'} 
                  alt={session.user.name || '用户头像'} 
                />
                <AvatarFallback>
                  <User className="w-8 h-8" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-foreground truncate">
                  {session.user.name || '用户'}
                </h3>
                                 <p className="text-sm text-muted-foreground truncate">
                   命理爱好者
                 </p>
                <div className="flex items-center mt-2 space-x-2">
                  <Badge variant="secondary">初学者</Badge>
                  <Badge variant="outline">已学习 7 天</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 功能菜单 */}
      <div className="flex flex-col p-4 gap-2">
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <Link key={item.href} href={item.href}>
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-foreground">{item.label}</h4>
                      <p className="text-sm text-muted-foreground">{item.description}</p>
                    </div>
                    <div className="flex-shrink-0">
                      <i className="fa-solid fa-chevron-right text-muted-foreground/40 text-sm"></i>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}

        {/* 退出登录 */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleSignOut}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-destructive/10 rounded-lg flex items-center justify-center">
                <LogOut className="w-5 h-5 text-destructive" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-destructive">退出登录</h4>
                <p className="text-sm text-muted-foreground">安全退出当前账号</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 