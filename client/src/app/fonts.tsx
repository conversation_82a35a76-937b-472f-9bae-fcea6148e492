
import { Inter } from 'next/font/google'
//import { Noto_Sans_SC } from 'next/font/google'

//export const fontSans = Noto_Sans_SC({
//  subsets: ['latin'],
//  display: 'swap',
//  weight: ['400', '600'],
//  variable: '--font-sans',
//});

// 主字体（现代简约）
export const fontSans = Inter({
  weight: ['400', '600'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-sans',
})

// 古籍阅读专用字体
import { Cormoran<PERSON>_Garamond } from 'next/font/google'
export const fontSerif = Cormorant_Garamond({
  weight: ['400', '600'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-serif',
})