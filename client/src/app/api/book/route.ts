export const dynamic = 'force-dynamic'; // 不要尝试静态生成

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth/next-auth'

const API_BASE_URL = process.env.INTERNAL_API_URL || 'http://localhost:8000/api'

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证状态
    //const session = await getServerSession(authOptions)
    //if (!session?.accessToken) {
    //  return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    //}

    // 获取查询参数
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const size = searchParams.get('size') || '10'
    const is_completed = searchParams.get('is_completed') || 'true'
    const category = searchParams.get('category')

    // 构建后端API请求URL
    const backendUrl = new URL(`${API_BASE_URL}/book/list`)
    backendUrl.searchParams.set('page', page)
    backendUrl.searchParams.set('size', size)
    backendUrl.searchParams.set('is_completed', is_completed)
    if (category) {
      backendUrl.searchParams.set('category', category)
    }

    // 向后端发送请求
    const response = await fetch(backendUrl.toString(), {
      headers: {
        'Content-Type': 'application/json',
        //'Authorization': `Bearer ${session.accessToken}`,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json(
        { error: errorData.detail || `后端错误: ${response.status}` }, 
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error) {
    console.error('获取书籍列表失败:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' }, 
      { status: 500 }
    )
  }
} 