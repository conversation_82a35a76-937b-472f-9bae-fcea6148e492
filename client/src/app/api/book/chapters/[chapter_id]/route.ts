export const dynamic = 'force-dynamic'; // 不要尝试静态生成
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth/next-auth'

const API_BASE_URL = process.env.INTERNAL_API_URL || 'http://localhost:8000/api'

export async function GET(
  request: NextRequest,
  { params }: { params: { chapter_id: string } }
) {
  try {
    // 验证用户认证状态
    const session = await getServerSession(authOptions)
    if (!session?.accessToken) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { chapter_id } = params

    // 向后端发送请求
    const response = await fetch(`${API_BASE_URL}/book/chapter/${chapter_id}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json(
        { error: errorData.detail || `后端错误: ${response.status}` }, 
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error) {
    console.error('获取章节内容失败:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' }, 
      { status: 500 }
    )
  }
} 