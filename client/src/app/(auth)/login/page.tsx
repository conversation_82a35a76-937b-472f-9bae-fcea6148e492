import { Metadata } from "next"
import { Suspense } from "react"
import { LoginForm } from "@/components/auth/login-form"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/next-auth"

export const metadata: Metadata = {
  title: "登录 - 命理学研习社",
  description: "登录您的命理学研习社账号",
}

interface LoginPageProps {
  searchParams: {
    callbackUrl?: string
  }
}

export default async function LoginPage({ searchParams }: LoginPageProps) {
  console.log("login page start, searchParams:", searchParams)
  const callbackUrl = searchParams.callbackUrl || '/'
  const session = await getServerSession(authOptions)
  console.log("session at login page:", session)
  if (session) {
    // 如果用户已登录，直接重定向到编码后的回调URL
    redirect(callbackUrl)
  }

  return (
    <div className="min-h-full flex items-center justify-center p-4 bg-paper-secondary w-full">
        <div className="bg-white max-w-md w-full rounded-2xl overflow-hidden shadow-xl">
          <div className="p-8">
            <h2 className="text-2xl font-serif text-primary text-center mb-6"> 用户登录</h2>
            <Suspense fallback={<div>加载中...</div>}>
              <LoginForm callbackUrl={callbackUrl} />
            </Suspense>
          </div>
        </div>
    </div>
  )
} 