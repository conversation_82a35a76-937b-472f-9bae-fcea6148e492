// middleware.ts
import { NextResponse, NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

//const PROTECTED_PATHS = ['/dashboard', '/user', '/book/detail']

export async function middleware(req: NextRequest) {
  const ua = req.headers.get('user-agent') || ''
  const isMobile = /Mobile|Android|iPhone|iPad/i.test(ua)
  const res = NextResponse.next()
  // 设置自定义 header，供 layout.tsx 中读取
  res.headers.set('x-device-type', isMobile ? 'mobile' : 'desktop')

  return res

}

export const config = {
  //matcher: ['/dashboard/:path*', '/user/:path*', '/book/detail/:path*', '/meihua/:path*'],
  matcher: ['/((?!api|_next/static|_next/image|.*\\.png$).*)', '/'],
}
