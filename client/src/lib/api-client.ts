"use client"
import { logError } from './error-handler';

// 统一的API错误处理
export class ApiError extends Error {
    constructor(public status: number, message: string) {
        super(message);
        this.name = 'ApiError';
    }
}

// 统一的API请求封装
class ApiClient {
    private baseURL: string;

    constructor(baseURL: string = '/api') {
        this.baseURL = baseURL;
    }

    async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const context = `API请求: ${options.method || 'GET'} ${endpoint}`;
        
        try {

            // 发送请求
            const url = `${this.baseURL}${endpoint}`;
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
            });

            // 处理响应
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const message = errorData.error || `HTTP ${response.status}: ${response.statusText}`;
                
                throw new ApiError(response.status, message);
            }
            

            return response.json();
            
        } catch (error) {
            // 使用统一错误处理进行日志记录
            logError(error, context);
            
            // 重新抛出错误，让调用方处理
            throw error;
        }
    }

    // GET请求
    async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
        const searchParams = params ? `?${new URLSearchParams(params)}` : '';
        return this.request<T>(`${endpoint}${searchParams}`);
    }

    // POST请求
    async post<T>(endpoint: string, data?: any): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    // PUT请求
    async put<T>(endpoint: string, data?: any): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    // DELETE请求
    async delete<T>(endpoint: string): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'DELETE',
        });
    }
}

// 导出单例实例
export const apiClient = new ApiClient();

// 书籍相关API的具体实现
export const booksApi = {
    // 获取书籍列表
    async getBooks(params: {
        page?: string;
        size?: string;
        is_completed?: string;
        category?: string;
    } = {}) {
        return apiClient.get('/book', {
            page: params.page || '1',
            size: params.size || '10',
            is_completed: params.is_completed || 'true',
            ...(params.category && { category: params.category }),
        });
    },

    // 获取书籍详情
    async getBookDetail(book_id: string) {
        return apiClient.get(`/book/${book_id}`);
    },

    // 获取章节内容
    async getChapterContent(chapter_id: string) {
        return apiClient.get(`/book/chapters/${chapter_id}`);
    },
};

export const baziAPI = {
    async calculate(data: any) {
        return apiClient.post('/bazi/divinate', data);
    },
};