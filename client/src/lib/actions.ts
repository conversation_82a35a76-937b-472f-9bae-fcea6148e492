'use server';

import { registerSchema, getContactType } from './schemas';
import { registerUser } from './api/auth';
import { redirect } from 'next/navigation';

export async function registerAction(
  prevState: { message: string; errors?: Record<string, string[]> } | undefined,
  formData: FormData,
) {
  try {
    const rawFormData = {
      contact: formData.get('contact') as string,
      password: formData.get('password') as string,
      confirmPassword: formData.get('confirmPassword') as string,
      agreeToTerms: formData.get('agreeToTerms') === 'on',
    };

    // 验证表单数据
    const validatedFields = registerSchema.safeParse(rawFormData);
    if (!validatedFields.success) {
      return {
        message: '提交的数据有误',
        errors: validatedFields.error.flatten().fieldErrors,
      };
    }

    const { contact, password } = validatedFields.data;
    const contactType = getContactType(contact);

    if (!contactType) {
      return {
        message: '联系方式格式错误',
        errors: {
          contact: ['请输入正确的邮箱或手机号格式']
        }
      };
    }

    // 调用注册 API
    await registerUser({
      email: contactType === 'email' ? contact : undefined,
      phone: contactType === 'phone' ? contact : undefined,
      password,
    });

    // 注册成功，重定向到登录页面
    redirect('/login?message=register_success');

  } catch (error) {
    // Next.js的redirect()会抛出特殊错误，需要重新抛出以触发重定向
    if (error instanceof Error && (error.message.includes('NEXT_REDIRECT') || error.name === 'RedirectError')) {
      throw error;
    }
    
    console.error('Registration error:', error);
    return {
      message: error instanceof Error ? error.message : '注册失败，请重试',
      errors: {}
    };
  }
}