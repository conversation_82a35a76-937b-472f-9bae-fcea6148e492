import { z } from 'zod'

// 联系方式类型判断
export function getContactType(contact: string): 'email' | 'phone' | null {
  const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact)
  const isPhone = /^1[3-9]\d{9}$/.test(contact)
  
  if (isPhone) return 'phone'
  if (isEmail) return 'email'
  return null
}

// 登录表单验证 schema
export const loginSchema = z.object({
  loginId: z.string().min(1, '请输入邮箱或手机号'),
  password: z.string().min(6, '密码至少6位'),
})

// next-auth credentials 验证 schema
// 使用 .pick() 只提取需要验证的字段，忽略NextAuth自动添加的字段
export const credentialsSchema = z.object({
  username: z.string().min(1, '请输入邮箱或手机号'),
  password: z.string().min(6, '密码至少6位'), 
  login_type: z.enum(['email', 'phone']),
  // NextAuth会自动添加这些字段，我们需要允许它们存在
  csrfToken: z.string().optional(),
  callbackUrl: z.string().optional(),
}).pick({
  username: true,
  password: true,
  login_type: true,
})

// 注册表单验证 schema
export const registerSchema = z.object({
  contact: z.string().min(1, '请输入邮箱或手机号'),
  password: z.string().min(6, '密码至少6位'),
  confirmPassword: z.string().min(6, '请确认密码'),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "请阅读并同意《用户协议》和《隐私政策》",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "两次输入的密码不一致",
  path: ["confirmPassword"],
})

// 注册请求类型
export interface RegisterRequest {
  email?: string
  phone?: string
  password: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
  login_type: 'email' | 'phone'
} 