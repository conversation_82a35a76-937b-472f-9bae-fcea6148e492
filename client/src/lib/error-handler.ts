// 错误类型枚举
export enum ErrorType {
  AUTH = 'auth',              // 认证问题（未登录、token无效等）
  PERMISSION = 'permission',  // 权限问题（403等）
  CLIENT = 'client',          // 客户端问题（400等）
  SERVER = 'server',          // 服务器问题（500等）
  NETWORK = 'network',        // 网络问题
  UNKNOWN = 'unknown'         // 未知问题
}

// 用户友好的错误信息
export interface UserError {
  type: ErrorType
  message: string
  shouldShow: boolean  // 是否应该显示给用户
  shouldLog: boolean   // 是否应该记录日志
}

// 错误分类函数
export function classifyError(error: any): UserError {
  // 网络错误
  if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
    return {
      type: ErrorType.NETWORK,
      message: '网络连接失败，请检查网络状态后重试',
      shouldShow: true,
      shouldLog: true
    }
  }

  // API错误
  if (error.status) {
    const status = error.status
    
    // 认证问题
    if (status === 401) {
      return {
        type: ErrorType.AUTH,
        message: '登录已过期，请重新登录',
        shouldShow: true,
        shouldLog: false // 正常的过期，不需要记录错误日志
      }
    }
    
    // 权限问题
    if (status === 403) {
      return {
        type: ErrorType.PERMISSION,
        message: '您没有权限访问此内容',
        shouldShow: true,
        shouldLog: true // 权限问题需要记录，可能是配置问题
      }
    }
    
    // 客户端问题
    if (status >= 400 && status < 500) {
      const clientErrors: Record<number, string> = {
        404: '请求的内容不存在',
        429: '请求过于频繁，请稍后再试',
        422: '请求数据格式有误',
      }
      
      return {
        type: ErrorType.CLIENT,
        message: clientErrors[status] || '请求参数有误，请检查后重试',
        shouldShow: true,
        shouldLog: true
      }
    }
    
    // 服务器问题
    if (status >= 500) {
      return {
        type: ErrorType.SERVER,
        message: '服务器暂时繁忙，请稍后重试',
        shouldShow: true,
        shouldLog: true // 服务器错误必须记录
      }
    }
  }
  
  // 其他未知错误
  return {
    type: ErrorType.UNKNOWN,
    message: '发生了未知错误，请稍后重试',
    shouldShow: true,
    shouldLog: true
  }
}

// 错误日志记录函数
export function logError(error: any, context?: string) {
  const userError = classifyError(error)
  
  if (userError.shouldLog) {
    const logData = {
      timestamp: new Date().toISOString(),
      context,
      errorType: userError.type,
      originalError: {
        message: error.message,
        status: error.status,
        stack: error.stack,
        name: error.name
      },
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    }
    
    // 根据错误级别选择不同的日志方法
    if (userError.type === ErrorType.SERVER || userError.type === ErrorType.UNKNOWN) {
      console.error('[严重错误]', logData)
    } else if (userError.type === ErrorType.PERMISSION) {
      console.warn('[权限问题]', logData)
    } else if (userError.type !== ErrorType.AUTH) {
      console.info('[客户端错误]', logData)
    }
    
    // 在生产环境中，这里可以发送到错误监控服务
    // 例如：Sentry, LogRocket, 或自定义的错误收集服务
    if (process.env.NODE_ENV === 'production') {
      // sendToErrorService(logData)
    }
  }
}

// 获取用户友好的错误信息
export function getUserErrorMessage(error: any, context?: string): string {
  logError(error, context)
  const userError = classifyError(error)
  return userError.shouldShow ? userError.message : '发生了未知错误'
}

// React Hook: 统一的错误处理
export function useErrorHandler() {
  const handleError = (error: any, context?: string) => {
    const userError = classifyError(error)
    logError(error, context)
    
    return {
      message: userError.shouldShow ? userError.message : '发生了未知错误',
      type: userError.type,
      shouldShow: userError.shouldShow
    }
  }
  
  return { handleError }
} 