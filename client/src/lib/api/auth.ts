import type { RegisterRequest, LoginRequest } from '../schemas'

const API_BASE_URL = process.env.INTERNAL_API_URL || 'http://localhost:8080/api'

// 调用后端 API 进行用户验证
export async function authenticateUser(credentials: LoginRequest) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login/json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    })
    
    if (!response.ok) {
      // 尝试获取后端返回的具体错误信息
        const errorData = await response.json()
        const errorMessage = errorData.detail || errorData.message || '登录失败'
        
        // 根据HTTP状态码提供更友好的错误信息
        switch (response.status) {
          case 401:
            throw new Error('用户名或密码错误')
          case 403:
            throw new Error('账户已被禁用')
          case 500:
            throw new Error('服务器内部错误，请稍后重试')
          default:
            throw new Error(errorMessage)
      }
    }

    const result = await response.json()
    
    // 检查是否有access_token
    if (!result || !result.access_token) {
      throw new Error('服务返回值异常，请稍后重试')
    }
    
    // 解析JWT token获取用户信息
    try {
      const tokenPayload = JSON.parse(atob(result.access_token.split('.')[1]))
      
      // 返回符合 next-auth User 类型的对象
      return {
        id: tokenPayload.user_id || 'unknown',
        name: tokenPayload.sub || 'user',
        accessToken: result.access_token,
        role: tokenPayload.role,
      }
    } catch (tokenError) {
      console.error('解析token失败:', tokenError)
      throw new Error('服务器响应格式错误')
    }
    
  } catch (error: any) {
    // 如果已经是我们抛出的错误，直接传递
    if (error.message) {
      throw error
    }
    
    // 处理网络错误等其他异常
    if (error.name === 'TypeError' && error.message?.includes('fetch')) {
      throw new Error('网络连接失败，请检查网络状态')
    }
    
    console.error('Authentication failed:', error)
    throw new Error('登录服务暂时不可用，请稍后重试')
  }
}

// 调用后端 API 进行用户注册
export async function registerUser(userData: RegisterRequest) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || '注册失败')
    }

    return await response.json()
  } catch (error) {
    console.error('Registration failed:', error)
    throw error
  }
} 

// 调用后端 API 进行用户刷新token
export async function refreshToken(token: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || '刷新token失败')
    }

    const result = await response.json()
    return result.access_token
  } catch (error) {
    console.error('Refresh token failed:', error)
    throw error
  }
} 