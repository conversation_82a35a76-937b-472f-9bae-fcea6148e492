// lib/auth/check-auth.ts
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/next-auth"
import { redirect } from "next/navigation"
import { refreshToken } from "@/lib/api/auth"

export async function checkAuth(callbackUrl: string) {
  const session = await getServerSession(authOptions)
  if (!session) {
    const loginUrl = `/login?callbackUrl=${encodeURIComponent(callbackUrl)}`
    redirect(loginUrl)
  }
  else {
    return session
  }
}

export async function checkToken(accessToken: string, accessTokenExpires: number) {
  // 检查token是否过期, 过期后刷新获得新的 token
  if (accessToken) {
    const expiresAt = accessTokenExpires as number;
    //const isExpired = expiresAt && expiresAt * 1000 <= Date.now();
    const isRefreshable = expiresAt && expiresAt * 1000 - Date.now() < 1 * 60 * 1000;
    if (isRefreshable) {
      try {
        const refreshedToken = await refreshToken(accessToken as string);
        if (refreshedToken) {
          const newTokenPayload = JSON.parse(atob(refreshedToken.split('.')[1]));
          const newAccessToken = refreshedToken;
          const newAccessTokenExpires = newTokenPayload.exp;
          const newTokenIssuedAt = Date.now();
          return { isRefreshed: true, accessToken: newAccessToken, accessTokenExpires: newAccessTokenExpires, tokenIssuedAt: newTokenIssuedAt }
        }
      } catch (error) {
        console.error('Token刷新失败:', error);
        return null
      }
    }
    else {
      return { isRefreshed: false, accessToken: accessToken }
    }
  }
  else {
    return null
  }
}
