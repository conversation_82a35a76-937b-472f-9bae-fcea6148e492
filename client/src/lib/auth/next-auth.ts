import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { credentialsSchema } from '../schemas';
import { authenticateUser, refreshToken } from '../api/auth';
import { NextAuthOptions } from 'next-auth';
import { checkToken } from './check-auth';

export const authOptions: NextAuthOptions = {
  secret: process.env.AUTH_SECRET,
  debug: false,
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'credentials',
      credentials: {
        username: { label: '邮箱或手机号', type: 'text' },
        password: { label: '密码', type: 'password' },
        login_type: { label: '登录类型', type: 'text' },
      },
      async authorize(credentials) {
        try {
          // 验证凭据格式
          const parsedCredentials = credentialsSchema.safeParse(credentials);
          
          if (!parsedCredentials.success) {
            const firstError = parsedCredentials.error.errors[0];
            //console.log("Schema验证失败:", firstError.message);
            return null;
          }

          const { username, password, login_type } = parsedCredentials.data;
          
          // 调用后端验证用户
          const user = await authenticateUser({
            username,
            password,
            login_type,
          });
          return user;
        } catch (error: any) {
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 3 * 60 * 60, 
    //maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  //jwt: {
  //  maxAge: 3 * 60, // 3 minutes, 
  //},
  callbacks: {
    async jwt({ token, user, account }) {
      if (user && account && (user as any).accessToken) {
        try {
          const accessToken = (user as any).accessToken;
          const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]));
          
          token.accessToken = accessToken;
          token.userId = user.id;
          token.userRole = (user as any).role;
          token.userName = user.name;
          token.accessTokenExpires = tokenPayload.exp as number; 
          token.tokenIssuedAt = Date.now();
          // console.log("token in jwt callback setted")
        } catch (error) {
          console.error('解析token失败:', error);
        }
      }
      
      // 检查token是否即将过期, 如果过期则刷新token
      const refresh_result = await checkToken(token.accessToken as string, token.accessTokenExpires as number);
      if (refresh_result && refresh_result.isRefreshed) {
        token.accessToken = refresh_result.accessToken;
        token.accessTokenExpires = refresh_result.accessTokenExpires;
        token.tokenIssuedAt = refresh_result.tokenIssuedAt;
      }
      // console.log("token in jwt callback is:", token)
      return token;
    },
    async session({ session, token }) {
      // 将JWT中的信息传递到session
      if (token && token.accessToken) {
        (session as any).accessToken = token.accessToken as string;
        session.user.id = (token.userId as string) || '';
        (session.user as any).role = token.userRole as string;
        session.user.name = (token.userName as string) || session.user.name || '';
      }
      return session;
    },
  },
};

export default  NextAuth(authOptions);
