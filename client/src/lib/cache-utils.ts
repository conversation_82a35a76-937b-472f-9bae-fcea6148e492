/**
 * 缓存管理工具
 * 用于处理浏览器缓存相关问题
 */

export interface CacheInfo {
  hasCache: boolean
  cacheSize: number
  lastCleared?: string
}

/**
 * 获取缓存信息
 */
export function getCacheInfo(): CacheInfo {
  try {
    const localStorage_size = JSON.stringify(localStorage).length
    const sessionStorage_size = JSON.stringify(sessionStorage).length
    const lastCleared = localStorage.getItem('cache_last_cleared')
    
    return {
      hasCache: localStorage_size > 0 || sessionStorage_size > 0,
      cacheSize: localStorage_size + sessionStorage_size,
      lastCleared: lastCleared || undefined
    }
  } catch (error) {
    console.error('获取缓存信息失败:', error)
    return {
      hasCache: false,
      cacheSize: 0
    }
  }
}

/**
 * 清除所有缓存
 */
export function clearAllCache(): void {
  try {
    // 清除 localStorage
    localStorage.clear()
    
    // 清除 sessionStorage  
    sessionStorage.clear()
    
    // 记录清除时间
    localStorage.setItem('cache_last_cleared', new Date().toISOString())
    
    // 清除 Service Worker 缓存
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        registrations.forEach(registration => {
          registration.unregister()
        })
      })
    }
    
    // 清除 IndexedDB（如果有使用）
    if ('indexedDB' in window) {
      // 这里可以添加具体的 IndexedDB 清理逻辑
    }
    
    console.log('缓存已清除')
  } catch (error) {
    console.error('清除缓存失败:', error)
  }
}

/**
 * 检测是否需要清除缓存
 * 基于上次清除时间和当前时间
 */
export function shouldClearCache(): boolean {
  try {
    const lastCleared = localStorage.getItem('cache_last_cleared')
    if (!lastCleared) return false
    
    const lastClearedTime = new Date(lastCleared).getTime()
    const now = Date.now()
    const dayInMs = 24 * 60 * 60 * 1000 // 24小时
    
    // 如果超过24小时未清除缓存，建议清除
    return (now - lastClearedTime) > dayInMs
  } catch (error) {
    console.error('检测缓存时间失败:', error)
    return false
  }
}

/**
 * 智能缓存清理
 * 在检测到问题时自动清理
 */
export function smartCacheClear(reason: string = 'unknown'): void {
  console.log(`执行智能缓存清理，原因: ${reason}`)
  
  // 记录清理原因
  const clearHistory = JSON.parse(localStorage.getItem('cache_clear_history') || '[]')
  clearHistory.push({
    timestamp: new Date().toISOString(),
    reason
  })
  
  // 只保留最近10次记录
  if (clearHistory.length > 10) {
    clearHistory.splice(0, clearHistory.length - 10)
  }
  
  // 清除缓存
  clearAllCache()
  
  // 保存清理历史
  localStorage.setItem('cache_clear_history', JSON.stringify(clearHistory))
}

/**
 * 强制刷新页面
 * 带缓存清理的页面刷新
 */
export function forceRefresh(): void {
  clearAllCache()
  
  // 使用 location.replace 而不是 reload 来避免缓存
  if (typeof window !== 'undefined') {
    window.location.replace(window.location.href)
  }
}

/**
 * 检测语法错误并处理
 */
export function handleSyntaxError(error: Error): void {
  if (error.message.includes('Unexpected token') || 
      error.message.includes('SyntaxError') ||
      error.name === 'SyntaxError') {
    
    console.error('检测到语法错误:', error)
    
    // 自动清理缓存
    smartCacheClear('syntax_error')
    
    // 提示用户
    if (typeof window !== 'undefined') {
      const shouldRefresh = window.confirm(
        '页面遇到了语法错误，这通常是由缓存问题引起的。\n' +
        '是否要清除缓存并重新加载页面？'
      )
      
      if (shouldRefresh) {
        forceRefresh()
      }
    }
  }
}

/**
 * 监听网络状态变化
 */
export function setupNetworkMonitoring(): void {
  if (typeof window !== 'undefined') {
    // 监听在线状态变化
    window.addEventListener('online', () => {
      console.log('网络已连接')
      // 可以在这里触发重新加载或重试逻辑
    })
    
    window.addEventListener('offline', () => {
      console.log('网络已断开')
      // 可以在这里显示离线提示
    })
  }
} 