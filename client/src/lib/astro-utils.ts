import { BirthD<PERSON>, ZiweiDivinateRequest } from "@/types/user"
import { Lunar } from "lunar-typescript"
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import FunctionalAstrolabe from "iztro/lib/astro/FunctionalAstrolabe";
import { Decadal }  from "iztro/lib/data/types";
import { Mutagen } from "@/types/divinate_result";
dayjs.extend(utc);
dayjs.extend(timezone);

// 获取真太阳时
export function getTrueSolarTime(date: Date, longitude: number): Date {
  // 计算平太阳时差（秒）
  const mean_solar_time_delta = (longitude - 120) * 4 * 60;
  // 计算平太阳时
  const mean_solar_time = new Date(date.getTime() + mean_solar_time_delta * 1000);
  // 计算一年中的第几天
  const start_of_year = new Date(date.getFullYear(), 0, 1);
  const n = Math.floor((date.getTime() - start_of_year.getTime()) / (24 * 60 * 60 * 1000)) + 1;
  // 计算d值
  const d = 6.24004077 + 0.01720197 * (365.25 * (date.getFullYear() - 2000) + n);
  // 计算均时差e（分钟）
  const e = -7.659 * Math.sin(d) + 9.863 * Math.sin(2 * d + 3.5932);
  // 计算总时差（秒）
  const delta = mean_solar_time_delta + e * 60;
  // 计算真太阳时
  const solar_time = new Date(date.getTime() + delta * 1000);
  return solar_time;
}


// 五行类型定义
export type WuXingElement = '木' | '火' | '土' | '金' | '水';
export type YinYangType = '阳' | '阴';

// 天干五行阴阳属性映射（更详细的分类）
const TIANGAN_WUXING_MAP: Record<string, { element: WuXingElement, yinyang: YinYangType }> = {
  '甲': { element: '木', yinyang: '阳' },
  '乙': { element: '木', yinyang: '阴' },
  '丙': { element: '火', yinyang: '阳' },
  '丁': { element: '火', yinyang: '阴' },
  '戊': { element: '土', yinyang: '阳' },
  '己': { element: '土', yinyang: '阴' },
  '庚': { element: '金', yinyang: '阳' },
  '辛': { element: '金', yinyang: '阴' },
  '壬': { element: '水', yinyang: '阳' },
  '癸': { element: '水', yinyang: '阴' }
};

/**
 * 根据天干获取五行和阴阳属性
 * @param tiangan 天干（甲、乙、丙、丁...）
 * @returns 包含五行元素和阴阳属性的对象，如果天干无效则返回null
 */
export function getTianGanWuXingYinYang(tiangan: string): { element: WuXingElement, yinyang: YinYangType } | null {
  return TIANGAN_WUXING_MAP[tiangan] || null;
}

/**
 * 判断两个五行元素的生克关系
 * @param element1 第一个五行元素
 * @param element2 第二个五行元素  
 * @returns 生克关系（生、克、被生、被克、同、无关）
 */
export function getWuXingRelation(element1: WuXingElement, element2: WuXingElement): string {
  // 五行相生：木生火，火生土，土生金，金生水，水生木
  const shengMap: Record<WuXingElement, WuXingElement> = {
    '木': '火',
    '火': '土', 
    '土': '金',
    '金': '水',
    '水': '木'
  };
  
  // 五行相克：木克土，土克水，水克火，火克金，金克木
  const keMap: Record<WuXingElement, WuXingElement> = {
    '木': '土',
    '土': '水',
    '水': '火', 
    '火': '金',
    '金': '木'
  };
  
  if (element1 === element2) {
    return '同';
  } else if (shengMap[element1] === element2) {
    return '生';
  } else if (keMap[element1] === element2) {
    return '克';
  } else if (shengMap[element2] === element1) {
    return '被生';
  } else if (keMap[element2] === element1) {
    return '被克';
  } else {
    return '无关';
  }
}

// 获取四化颜色
export function getMutagenColor(mutagen: string): string {
  const mutagenColorMap: Record<string, string> = {
    '权': 'text-yellow-600',
    '忌': 'text-red-600',
    '科': 'text-blue-600',
    '禄': 'text-green-600',
  }
  return mutagenColorMap[mutagen] || 'bg-foreground'
}

export function getTimeIndex(hour: number): number {
  return Math.ceil(hour / 2)
}

export function getTimeList(): [string, string, number][] {
  return [
    ['早子时', '00:00-00:59', 0],
    ['丑时', '01:00-02:59', 1],
    ['寅时', '03:00-04:59', 2],
    ['卯时', '05:00-06:59', 3],
    ['辰时', '07:00-08:59', 4],
    ['巳时', '09:00-10:59', 5],
    ['午时', '11:00-12:59', 6],
    ['未时', '13:00-14:59', 7],
    ['申时', '15:00-16:59', 8],
    ['酉时', '17:00-18:59', 9],
    ['戌时', '19:00-20:59', 10],
    ['亥时', '21:00-22:59', 11],
    ['晚子时', '23:00-23:59', 12],
  ]
}

const zone = 'Asia/Shanghai';

export function isDST(date: string | null | undefined, dateTime: string | null | undefined): boolean {
  let result = false;
  if (date === "" || date === null || date === undefined) {
    result = false;
  }
  else {
    const dt = dayjs.tz(date, zone);
    const janOffset = dayjs.tz(`${dt.year()}-01-01 12:00`, zone).utcOffset();
    result = dt.utcOffset() !== janOffset;
  }
  return result;
}

export function setDivinateRequestFromBirthData(birthData: BirthData): ZiweiDivinateRequest {
  // const [year, month, day] = birthData.birthDate.split('-').map(Number);
  // const [hour, minute] = birthData.birthTime.split(':').map(Number);
  // console.log('hour', hour)
  // 注意：JavaScript 的月份是从 0 开始（即 0 表示1月）
  //let dateInBeijing = new Date(Date.UTC(year, month - 1, day, hour - 8, minute)); 
  let dateInBeijing = dayjs.tz(birthData.birthDate + ' ' + birthData.birthTime, zone);
  // 如果是夏令时
  if (birthData.isDST) {
    //dateInBeijing = new Date(dateInBeijing.getTime() - 60 * 60 * 1000);
    dateInBeijing = dateInBeijing.subtract(1, 'hour');
    // console.log('dateInBeijing after isSummerTime', dateInBeijing)
  }
  const t = getTrueSolarTime(dateInBeijing.toDate(), birthData.longitude)
  const trueSolarTime = dayjs(t).format('YYYY-MM-DD HH:mm')
  let solarTime = dateInBeijing.toDate()
  // 如果是真太阳时
  if (birthData.isTrueSolarTime) {
    solarTime = t
  }

  // 获取时辰
  let timeIndex = 0
  const lunar = Lunar.fromDate(solarTime)
  const bazi = lunar.getBaZi()
  // 如果是早晚子时
  if (birthData.isEarlyOrLateNight) {
    // 先获取真实的八字四柱
    if (solarTime.getHours() >= 23) {
      // 如果是23点，则要往前算一天，因为astro默认会算作第二天
      solarTime = new Date(solarTime.getTime() - 24 * 60 * 60 * 1000)
    }
    timeIndex = getTimeIndex(solarTime.getHours())
    //console.log('早晚子时的timeIndex', timeIndex)
  }
  else {
    timeIndex = getTimeIndex(solarTime.getHours())
    //console.log('真实timeIndex', timeIndex)
  }
  //console.log('lunar', bazi)
  return {
    name: birthData.name,
    gender: birthData.gender,
    solarTime: solarTime.toLocaleString(),
    trueSolarTime: trueSolarTime,
    timeIndex: timeIndex,
  }
}

//从宫位中获取所有的大限时间
export function getFortuneTimeZiwei(ziweiResult: FunctionalAstrolabe): Decadal[] {
  const result: Decadal[] = []
  for (let i = 0; i < ziweiResult.palaces.length; i++) {
    const palace = ziweiResult.palaces[i]
    const decadal = palace.decadal
    result.push(decadal)
  }
  return result
}

export function getFortunePalaceName(palace: string, index: number) {
  if (palace === '仆役') {
    palace= "交友"
  }
  if (index === 0) {
    return "运" + palace[0]
  }
  else if (index === 1) {
    return "年" + palace[0]
  }
  else if (index === 2) {
    return "月" + palace[0]
  }
  else if (index === 3) {
    return "日" + palace[0]
  }
  else if (index === 4) {
    return "时" + palace[0]
  }
  console.log(index)
  return palace
}

// 获取四化星及对应的类别名称
export function getMutagenMap(mutagen: string[]): Record<string, string> {
  const mutagenMap: Record<string, string> = {}
  mutagen.forEach((item, index) => {
    mutagenMap[item] = Mutagen[index]
  })
  return mutagenMap
}

export function isPalaceTriangularPositions(palace: number, selectedPalace: number): boolean {
  const triangularPositions = [
    (palace + 4) % 12,
    (palace + 6) % 12,
    (palace + 8) % 12
  ]
  return triangularPositions.includes(selectedPalace)
}