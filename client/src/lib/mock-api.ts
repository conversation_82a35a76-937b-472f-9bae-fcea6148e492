// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 天干
const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
// 地支
const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
// 纳音五行
const nayinWuxing = [
  '海中金', '炉中火', '大林木', '路旁土', '剑锋金', '山头火',
  '涧下水', '城头土', '白腊金', '杨柳木', '井泉水', '屋上土',
  '霹雳火', '松柏木', '长流水', '沙中金', '山下火', '平地木',
  '壁上土', '金箔金', '覆灯火', '天河水', '大驿土', '钗环金',
  '桑拓木', '大溪水', '沙中土', '天上火', '石榴木', '大海水'
];

// 随机获取数组中的元素
const getRandomElement = (arr: any[]) => arr[Math.floor(Math.random() * arr.length)];

// 生成随机五行数量
const generateRandomWuxing = () => {
  return {
    wood: Math.floor(Math.random() * 4),
    fire: Math.floor(Math.random() * 4),
    earth: Math.floor(Math.random() * 4),
    metal: Math.floor(Math.random() * 4),
    water: Math.floor(Math.random() * 4)
  };
};

// 生成随机柱子
const generatePillar = () => {
  return {
    heavenlyStem: getRandomElement(heavenlyStems),
    earthlyBranch: getRandomElement(earthlyBranches)
  };
};

// 模拟八字API
export const mockBaziAPI = {
  // 计算八字命盘
  calculate: async (birthData: any) => {
    // 模拟服务器延迟
    await delay(1500);
    
    // 随机生成八字数据
    const yearPillar = generatePillar();
    const monthPillar = generatePillar();
    const dayPillar = generatePillar();
    const hourPillar = generatePillar();
    
    // 生成纳音
    const nayin = [
      getRandomElement(nayinWuxing),
      getRandomElement(nayinWuxing),
      getRandomElement(nayinWuxing),
      getRandomElement(nayinWuxing)
    ];
    
    // 生成模拟数据
    return {
      yearPillar,
      monthPillar,
      dayPillar,
      hourPillar,
      lunarDate: `${Math.floor(Math.random() * 60 + 1960)}年${Math.floor(Math.random() * 12 + 1)}月${Math.floor(Math.random() * 28 + 1)}日`,
      solarDate: new Date(birthData.birthDate).toLocaleDateString('zh-CN'),
      nayin,
      dayMaster: dayPillar.heavenlyStem,
      wuxing: generateRandomWuxing(),
      analysis: {
        summary: "综合八字命盘分析，此命盘具有较为平衡的五行结构，但以金水为主。日主为" + dayPillar.heavenlyStem + "，" + (birthData.gender === "male" ? "男命" : "女命") + "。",
        luck: "近期运势平稳，2024年有财运加持，事业发展可期。",
        personality: "性格沉稳内敛，做事认真负责，具有良好的思考能力和分析力。在与人交往上比较谨慎，但一旦建立信任，会非常忠诚。",
        career: birthData.gender === "male" 
          ? "适合从事需要精确和耐心的工作，如金融分析、工程技术、研究工作或精密制造业。职业发展中要注意提升自己的沟通表达能力。" 
          : "适合从事需要细心和专注的工作，如会计、设计、教育或医疗保健领域。注重自身专业知识的积累，将有助于职业发展。",
        relationships: birthData.gender === "male"
          ? "感情方面较为理性，需要一个能理解其内心世界的伴侣。婚姻关系中偏向稳定，但需要学会表达情感。"
          : "在感情上比较专一，希望得到安全感和稳定的关系。适合晚婚，与性格温和、有责任感的伴侣相配。",
        health: "体质较好，但需注意" + (birthData.gender === "male" ? "肺部和大肠" : "肺部和皮肤") + "方面的健康。建议保持规律作息，适当参加户外活动，增强体质。"
      }
    };
  },
  
  // 获取八字课程列表
  getCourses: async (params?: any) => {
    await delay(800);
    
    return {
      courses: [
        {
          id: 1,
          title: "八字入门基础",
          description: "了解八字命理的基本概念和理论框架",
          level: "初级",
          duration: "10课时",
          price: 99
        },
        {
          id: 2,
          title: "天干地支详解",
          description: "深入解析天干地支的特性及组合规律",
          level: "初级",
          duration: "8课时",
          price: 129
        },
        {
          id: 3,
          title: "命盘分析实战",
          description: "通过实际案例学习八字命盘的分析方法",
          level: "中级",
          duration: "12课时",
          price: 199
        }
      ],
      total: 3,
      page: params?.page || 1,
      pageSize: params?.pageSize || 10
    };
  },
  
  // 获取八字书籍列表
  getBooks: async (params?: any) => {
    await delay(800);
    
    return {
      books: [
        {
          id: 1,
          title: "滴天髓阐微",
          author: "任铁樵",
          description: "对《滴天髓》的详细注解，是研究八字的重要典籍",
          coverImage: "https://example.com/cover1.jpg",
          price: 58
        },
        {
          id: 2,
          title: "三命通会",
          author: "万民英",
          description: "集合了明代之前的八字命理理论，系统完整",
          coverImage: "https://example.com/cover2.jpg",
          price: 68
        },
        {
          id: 3,
          title: "渊海子平",
          author: "徐子平",
          description: "八字命理的经典著作，内容丰富精辟",
          coverImage: "https://example.com/cover3.jpg",
          price: 45
        }
      ],
      total: 3,
      page: params?.page || 1,
      pageSize: params?.pageSize || 10
    };
  }
};

// 模拟紫微斗数API
export const mockZiweiAPI = {
  // 计算紫微斗数命盘
  calculate: async (birthData: any) => {
    // 模拟服务器延迟
    await delay(1500);
    
    // 紫微斗数模拟数据
    // ... 紫微斗数相关逻辑
    
    return {
      // 返回模拟数据
    };
  }
};

// 模拟梅花易数API
export const mockMeihuaAPI = {
  // 计算梅花易数
  calculate: async (questionData: any) => {
    // 模拟服务器延迟
    await delay(1500);
    
    // 梅花易数模拟数据
    // ... 梅花易数相关逻辑
    
    return {
      // 返回模拟数据
    };
  }
}; 