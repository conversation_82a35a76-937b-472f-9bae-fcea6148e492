/* 备用样式 - 当外部资源加载失败时使用 */

/* 系统字体备用方案 */
@font-face {
  font-family: 'Inter-Fallback';
  src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'), 
       local('Segoe UI'), local('Roboto'), local('Helvetica Neue'), local('Arial');
  font-display: swap;
}

/* 字体图标备用方案 */
.fa-fallback,
.fas-fallback,
.far-fallback,
.fal-fallback,
.fab-fallback {
  font-family: system-ui, -apple-system, sans-serif;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
}

/* 常用图标的 emoji 备用方案 */
.fa-home-fallback:before,
.fa-home.fallback:before { content: "🏠"; }

.fa-user-fallback:before,
.fa-user.fallback:before { content: "👤"; }

.fa-search-fallback:before,
.fa-search.fallback:before { content: "🔍"; }

.fa-menu-fallback:before,
.fa-bars.fallback:before { content: "☰"; }

.fa-times-fallback:before,
.fa-times.fallback:before { content: "✕"; }

.fa-chevron-down-fallback:before,
.fa-chevron-down.fallback:before { content: "▼"; }

.fa-chevron-up-fallback:before,
.fa-chevron-up.fallback:before { content: "▲"; }

.fa-chevron-left-fallback:before,
.fa-chevron-left.fallback:before { content: "◀"; }

.fa-chevron-right-fallback:before,
.fa-chevron-right.fallback:before { content: "▶"; }

.fa-check-fallback:before,
.fa-check.fallback:before { content: "✓"; }

.fa-plus-fallback:before,
.fa-plus.fallback:before { content: "+"; }

.fa-minus-fallback:before,
.fa-minus.fallback:before { content: "−"; }

.fa-edit-fallback:before,
.fa-edit.fallback:before { content: "✎"; }

.fa-trash-fallback:before,
.fa-trash.fallback:before { content: "🗑"; }

.fa-save-fallback:before,
.fa-save.fallback:before { content: "💾"; }

.fa-download-fallback:before,
.fa-download.fallback:before { content: "⬇"; }

.fa-upload-fallback:before,
.fa-upload.fallback:before { content: "⬆"; }

.fa-settings-fallback:before,
.fa-cog.fallback:before { content: "⚙"; }

.fa-info-fallback:before,
.fa-info.fallback:before { content: "ℹ"; }

.fa-warning-fallback:before,
.fa-exclamation-triangle.fallback:before { content: "⚠"; }

.fa-error-fallback:before,
.fa-times-circle.fallback:before { content: "❌"; }

.fa-success-fallback:before,
.fa-check-circle.fallback:before { content: "✅"; }

/* 加载动画备用方案 */
.spinner-fallback {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin-fallback 1s ease-in-out infinite;
}

@keyframes spin-fallback {
  to { transform: rotate(360deg); }
}

/* 无障碍备用方案 */
.sr-only-fallback {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 响应式备用网格 */
.grid-fallback {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 640px) {
  .grid-fallback {
    grid-template-columns: 1fr;
  }
}

/* 按钮备用样式 */
.btn-fallback {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s;
  cursor: pointer;
}

.btn-fallback:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-fallback:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn-primary-fallback {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.btn-primary-fallback:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}
