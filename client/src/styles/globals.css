@tailwind base;
@tailwind components;
@tailwind utilities;

/* 引入备用样式 */
@import './fallback.css';

@layer base {
  :root {
    /* 基本背景色和字体颜色gray-800 */
    --background: 210 40% 98%;
    /*--background: 45 22% 96%;*/
    --foreground: 215 28% 27%; 

    /* 主色调 blue-600/white */
    /* --primary: 221 83% 53%; */
    --primary: 136 24% 38%;
    --primary-foreground: 0,0%,100%;
 
    /* 次要色调 - 金色/gray-100 */
    --secondary: 46 65% 45%;
    /* --secondary: 40 36% 53%; */
    --secondary-dark: 46 65% 30%;
    --secondary-foreground: 220 14% 96%;
 
    /* 辅助色：blue-100/黑色， Muted backgrounds such as <TabsList />, <Skeleton /> and <Switch /> */
    /* --muted: 214 95% 93%;  */
    --muted: 138 25% 88%;
    --muted-foreground: 215 14% 15%;
   
    /* Zinc-50/Zinc-800 Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
    --popover: 0 0% 98%;
    --popover-foreground: 240 4% 16%;

    /* blue-800/gray-100 Used for accents such as hover effects on <DropdownMenuItem>, <SelectItem>...etc */
    /* --accent: 226 71% 40%; */
    --accent: 136 24% 30%;
    --accent-foreground: 220 14% 96%;

    /* Zinc-50/Zinc-800 Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
    --card: 214 100% 99%; 
    --card-foreground: 240 4% 16%;

    /*blue-100*/
    /* --border: 214 95% 83%; */
    --border: 40 21% 89%;
    --input: 40 21% 89%;

    /* 警告/错误色 红色*/
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    /* 书本背景色 */
    /* --book-background: 36 71% 98%; */
    --book-background: 46 65% 30%;
    --book-foreground: 215 28% 27%;
    --book-border: 36 71% 80%;
    
    /* focus ring */
    --ring: 40 38% 80%;

    /* 纸张色 */
    --paper: 38 36% 96%;
    --paper-secondary: 40 38% 94%;

    --tertiary: 136 24% 38%;
    --tertiary-light: 138 25% 90%;
    --tertiary-foreground: 215 28% 27%;

    --radius: 0.5rem;
    
    /* 字体配置：Next.js优化优先，备用中文字体 */
    --font-sans: -apple-system, "PingFang SC", BlinkMacSystemFont, "Noto Sans SC", "Microsoft YaHei", "Heiti SC", sans-serif;

    --content-width: 1440px;
    
    /* 移动端底部导航栏高度变量 */
    --mobile-bottom-nav-height: 3.25rem;

  }
}
 
@layer base {
  /* html {
    scrollbar-gutter: stable both-edges;
  }  */
  body {
    overflow-y: scroll;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: var(--font-sans);
    }
    
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 15px;
  height: 8px;
  background: transparent;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/20 hover:bg-primary/40 rounded-full transition-colors;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary;
}

/* 中国传统元素样式 */
.chinese-pattern {
  @apply bg-primary;
  background-image: 
    radial-gradient(circle at 100% 150%, #f5f5f5 24%, #fff 24%, #fff 28%, #f5f5f5 28%, #f5f5f5 36%, #fff 36%, #fff 40%, transparent 40%, transparent),
    radial-gradient(circle at 0 150%, #f5f5f5 24%, #fff 24%, #fff 28%, #f5f5f5 28%, #f5f5f5 36%, #fff 36%, #fff 40%, transparent 40%, transparent),
    radial-gradient(circle at 50% 100%, #fff 10%, #f5f5f5 10%, #f5f5f5 23%, #fff 23%, #fff 30%, #f5f5f5 30%, #f5f5f5 43%, #fff 43%, #fff 50%, #f5f5f5 50%, #f5f5f5 63%, #fff 63%, #fff 71%, transparent 71%, transparent),
    radial-gradient(circle at 100% 50%, #fff 5%, #f5f5f5 5%, #f5f5f5 15%, #fff 15%, #fff 20%, #f5f5f5 20%, #f5f5f5 29%, #fff 29%, #fff 34%, #f5f5f5 34%, #f5f5f5 44%, #fff 44%, #fff 49%, transparent 49%, transparent),
    radial-gradient(circle at 0 50%, #fff 5%, #f5f5f5 5%, #f5f5f5 15%, #fff 15%, #fff 20%, #f5f5f5 20%, #f5f5f5 29%, #fff 29%, #fff 34%, #f5f5f5 34%, #f5f5f5 44%, #fff 44%, #fff 49%, transparent 49%, transparent);
  background-size: 100px 50px;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .writing-mode-vertical-rl {
    writing-mode: vertical-rl;
    text-orientation: upright;
  }
  
  .font-song {
    font-family: "Songti SC", "STSong", "华文宋体", serif;
  }
  
  .font-kai {
    font-family: "Kaiti SC", "STKaiti", "华文楷体", serif;
  }
} 


/* 文本截断样式 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  button, a {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
} 