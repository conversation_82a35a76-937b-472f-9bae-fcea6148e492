/** @type {import('tailwindcss').Config} */
const {heroui} = require("@heroui/theme");
module.exports = {
  darkMode: ["class"],
  content: [
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/styles/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/components/(date-input|select|form|listbox|divider|popover|button|ripple|spinner|scroll-shadow).js"
  ],
  theme: {
  	extend: {
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
			tertiary: {
				DEFAULT: 'hsl(var(--tertiary))',
				light: 'hsl(var(--tertiary-light))',
				foreground: 'hsl(var(--tertiary-foreground))'
			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			book: {
  				DEFAULT: 'hsl(var(--book-background))',
  				foreground: 'hsl(var(--book-foreground))',
  				border: 'hsl(var(--book-border))',
  			},
			paper: {
				DEFAULT: 'hsl(var(--paper))',
				secondary: 'hsl(var(--paper-secondary))',
			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: 0
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: 0
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))'
  		},
  		maxWidth: {
  			content: 'var(--content-width)'
  		},
		width: {
			content: '1440px'
		},
		height: {
			'mobile-top-nav': 'var(--mobile-top-nav-height)',
			'mobile-bottom-nav': 'var(--mobile-bottom-nav-height)'
		},
		fontFamily: {
			sans: ['var(--font-sans)', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
			inter: ['var(--font-sans)', 'Inter', 'system-ui', 'sans-serif'],
		}
  	}
  },
  darkMode: "class",
  plugins: [require("tailwindcss-animate"), heroui()],
  safelist: [
    'text-green-600',
    'text-yellow-600',
    'text-blue-600',
    'text-red-600',
	'text-purple-600',
  ]
} 