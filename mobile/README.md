# 命理学研习社

这是一个命理学在线学习平台，专注于八字、紫微斗数和梅花易数三种命理学术的学习与实践。

## 项目概述

命理学研习社旨在为命理学爱好者提供全面的学习平台，包括在线测算、课程学习、经典书籍阅读和案例解析功能。项目支持PC端和移动端网页展示，并为未来的移动应用提供迁移可能性。

## 主要功能

1. **在线测算**：用户提供出生年月、出生地点等信息，生成对应的命盘/星盘及分析结果
2. **课程学习**：提供系统化的文字和视频教学课程
3. **经典书籍**：提供古籍阅读，含古文与白话文对照功能
4. **案例解析**：提供分类案例及解析，支持多条件搜索和命盘生成

## 技术栈

- **前端**：React, Next.js, TypeScript, TailwindCSS
- **后端**：Python, FastAPI
- **数据库**：PostgreSQL, MongoDB
- **部署**：Docker, Nginx

## 项目结构

```
fate_explorer/
├── client/                       # 前端目录
│   ├── public/                   # 静态资源
│   ├── src/                      # 源代码
│   │   ├── app/                  # 主应用目录 (Next.js App Router)
│   │   │   ├── (auth)/           # 认证相关页面
│   │   │   ├── (marketing)/      # 营销/介绍页面
│   │   │   ├── dashboard/        # 用户仪表板
│   │   │   │   ├── profile/      # 用户资料
│   │   │   │   ├── history/      # 历史记录
│   │   │   │   └── settings/     # 用户设置
│   │   │   ├── bazi/             # 八字相关页面
│   │   │   │   ├── divinate/     # 八字测算
│   │   │   │   ├── courses/      # 八字课程
│   │   │   │   ├── books/        # 八字书籍
│   │   │   │   └── cases/        # 八字案例
│   │   │   ├── ziwei/            # 紫微斗数相关页面
│   │   │   │   ├── divinate/     # 紫微测算
│   │   │   │   ├── courses/      # 紫微课程
│   │   │   │   ├── books/        # 紫微书籍
│   │   │   │   └── cases/        # 紫微案例
│   │   │   ├── meihua/           # 梅花易数相关页面
│   │   │   │   ├── divinate/     # 梅花测算
│   │   │   │   ├── courses/      # 梅花课程
│   │   │   │   ├── books/        # 梅花书籍
│   │   │   │   └── cases/        # 梅花案例
│   │   │   ├── api/              # API路由
│   │   │   └── layout.tsx        # 根布局
│   │   ├── components/           # 可复用组件
│   │   │   ├── ui/               # UI组件
│   │   │   │   ├── button.tsx
│   │   │   │   ├── card.tsx
│   │   │   │   └── ...
│   │   │   ├── layout/           # 布局组件
│   │   │   │   ├── header.tsx
│   │   │   │   ├── footer.tsx
│   │   │   │   ├── sidebar.tsx
│   │   │   │   └── ...
│   │   │   ├── forms/            # 表单组件
│   │   │   │   ├── birth-info-form.tsx
│   │   │   │   └── ...
│   │   │   ├── charts/           # 图表组件
│   │   │   │   ├── bazi-chart.tsx
│   │   │   │   ├── ziwei-chart.tsx
│   │   │   │   └── meihua-chart.tsx
│   │   │   └── shared/           # 共享组件
│   │   ├── lib/                  # 工具库
│   │   │   ├── api.ts            # API客户端
│   │   │   ├── auth.ts           # 认证工具
│   │   │   ├── date-utils.ts     # 日期工具
│   │   │   └── validators.ts     # 验证工具
│   │   ├── hooks/                # 自定义Hooks
│   │   │   ├── use-auth.ts
│   │   │   ├── use-divinate.ts
│   │   │   └── ...
│   │   ├── context/              # 上下文
│   │   │   ├── auth-context.tsx
│   │   │   └── theme-context.tsx
│   │   ├── types/                # 类型定义
│   │   │   ├── user.ts
│   │   │   ├── bazi.ts
│   │   │   ├── ziwei.ts
│   │   │   └── meihua.ts
│   │   └── styles/               # 样式
│   │       └── globals.css
│   ├── next.config.js            # Next.js配置
│   ├── tailwind.config.js        # Tailwind配置
│   ├── tsconfig.json             # TypeScript配置
│   └── package.json              # 依赖管理
│
├── server/                       # 后端目录 (已存在)
│   └── ...
│
├── docs/                         # 文档目录
│   ├── api/                      # API文档
│   ├── architecture/             # 架构文档
│   └── guides/                   # 开发指南
│
└── README.md                     # 项目说明
```

## 使用说明

### 前端开发

1. 安装依赖：
```bash
cd client
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 构建生产版本：
```bash
npm run build
```

## 项目特性

1. **响应式设计**：支持PC端和移动端的统一体验
2. **性能优化**：使用Next.js的SSR/SSG/ISR特性实现高性能渲染
3. **类型安全**：全栈TypeScript确保类型一致性和开发质量
4. **可扩展性**：模块化设计便于未来扩展新的命理学类型
5. **国际化支持**：多语言支持潜力
6. **离线能力**：支持PWA特性供移动用户离线使用
7. **数据安全**：全面的身份验证和授权系统
