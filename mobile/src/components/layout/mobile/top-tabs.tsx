"use client"
import { useTopTabActive } from '@/hooks/use-top-tab-active'
import { usePathname } from 'next/navigation'

const showPaths = [
    '/case/',
    '/course/',
    '/book/',
    '/meihua/',
    '/ziwei/',
    '/bazi/',
]

export default function TopTabs({ currentCategory }: { currentCategory: string }) {
    const { activeTab, handleTabClick } = useTopTabActive(currentCategory);
    const pathname = usePathname()
    const isShow = showPaths.some(path => 
        pathname === path || pathname.endsWith(path)
      )


    const tabs = [
        { id: "case", label: '案例大全', href: '/case' },
        { id: "course", label: '在线课程', href: '/course' },
        { id: "book", label: '经典书籍', href: '/book/list/m' },
    ]
    if (!isShow) {
        return null
    }
    return (
        <div className="flex w-full h-mobile-top-nav justify-around bg-background backdrop-blur-sm supports-backdrop-filter:bg-background/60">
                {tabs.map((tab) => (
                    <button
                        key={tab.id}
                        onClick={() => handleTabClick(tab.id as any)}
                        className={`flex-1 py-2 px-2 text-sm font-medium font-serif transition-colors border-b-2 ${activeTab === tab.id
                                ? "text-primary border-primary bg-muted/30"
                                : "text-gray-600 border-transparent hover:text-primary"
                            }`}
                    >
                        {tab.label}
                    </button>
                ))}
        </div>
    )
}