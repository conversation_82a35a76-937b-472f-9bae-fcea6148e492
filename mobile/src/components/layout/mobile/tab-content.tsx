"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

type TabType = 'cases' | 'courses' | 'books'

interface TabContentProps {
  category: 'bazi' | 'ziwei' | 'meihua'
  defaultTab?: TabType
}

const categoryNames = {
  bazi: '八字',
  ziwei: '紫微斗数', 
  meihua: '梅花易数'
}

// Mock数据 - 实际使用时应该从API获取
const mockData = {
  cases: [
    { id: 1, title: '事业发展案例', description: '事业发展解析', difficulty: '初级', date: '2024-01-15' },
    { id: 2, title: '婚姻感情案例', description: '夫妻合盘解读', difficulty: '中级', date: '2024-01-12' },
    { id: 3, title: '财运分析实例', description: '投资理财指导', difficulty: '高级', date: '2024-01-10' },
    { id: 4, title: '子女分析实例', description: '子女分析示例', difficulty: '高级', date: '2024-01-10' },
    { id: 5, title: '健康分析实例', description: '健康分析指导', difficulty: '高级', date: '2024-01-10' }
  ],
  courses: [
    { id: 1, title: '基础入门课程', description: '从零开始学习基本概念', duration: '2小时', level: '入门' },
    { id: 2, title: '进阶实战技巧', description: '掌握实用分析方法', duration: '4小时', level: '进阶' },
    { id: 3, title: '高级综合应用', description: '复杂命盘综合解读', duration: '6小时', level: '高级' }
  ],
  books: [
    { id: 1, title: '经典古籍原文', description: '传统典籍精选章节', pages: '120页', type: '古典' },
    { id: 2, title: '现代白话解析', description: '古文现代化翻译对照', pages: '89页', type: '现代' },
    { id: 3, title: '实战案例集锦', description: '历代名师案例汇编', pages: '200页', type: '案例' }
  ]
}

export default function TabContent({ category, defaultTab = 'cases' }: TabContentProps) {
  const [activeTab, setActiveTab] = useState<TabType>(defaultTab)
  
  const categoryName = categoryNames[category]

  const renderCaseCard = (item: typeof mockData.cases[0]) => (
    <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-foreground line-clamp-1">{item.title}</h3>
          <Badge variant="secondary" className="text-xs">{item.difficulty}</Badge>
        </div>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{item.description}</p>
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>{categoryName}案例</span>
          <span>{item.date}</span>
        </div>
      </CardContent>
    </Card>
  )

  const renderCourseCard = (item: typeof mockData.courses[0]) => (
    <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-foreground line-clamp-1">{item.title}</h3>
          <Badge variant="outline" className="text-xs">{item.level}</Badge>
        </div>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{item.description}</p>
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>{categoryName}课程</span>
          <span>{item.duration}</span>
        </div>
      </CardContent>
    </Card>
  )

  const renderBookCard = (item: typeof mockData.books[0]) => (
    <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-foreground line-clamp-1">{item.title}</h3>
          <Badge variant="default" className="text-xs">{item.type}</Badge>
        </div>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{item.description}</p>
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>{categoryName}书籍</span>
          <span>{item.pages}</span>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="flex flex-col h-full">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabType)} className="flex flex-col h-full">
        {/* 顶部分段控件 */}
        <div className="sticky top-0 z-10 bg-background border-b border-border/40 backdrop-blur-sm supports-backdrop-filter:bg-background/60">
          <TabsList className="grid w-full grid-cols-3 m-4 h-10">
            <TabsTrigger value="cases" className="text-sm">案例</TabsTrigger>
            <TabsTrigger value="courses" className="text-sm">课程</TabsTrigger>
            <TabsTrigger value="books" className="text-sm">书籍</TabsTrigger>
          </TabsList>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto pb-4">
          <TabsContent value="cases" className="mt-0 p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-foreground">{categoryName}案例</h2>
              <Badge variant="secondary">{mockData.cases.length}个案例</Badge>
            </div>
            <div className="space-y-3">
              {mockData.cases.map(renderCaseCard)}
            </div>
          </TabsContent>

          <TabsContent value="courses" className="mt-0 p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-foreground">{categoryName}课程</h2>
              <Badge variant="secondary">{mockData.courses.length}门课程</Badge>
            </div>
            <div className="space-y-3">
              {mockData.courses.map(renderCourseCard)}
            </div>
          </TabsContent>

          <TabsContent value="books" className="mt-0 p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-foreground">{categoryName}书籍</h2>
              <Badge variant="secondary">{mockData.books.length}本书籍</Badge>
            </div>
            <div className="space-y-3">
              {mockData.books.map(renderBookCard)}
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
} 