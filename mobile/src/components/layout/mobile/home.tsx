"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import LandingScreen from '@/components/layout/mobile/landing-page'

export default function MobileHome() {
  const router = useRouter()
  const [isFirstVisit, setIsFirstVisit] = useState(true)

  // useEffect(() => {
  //   // 检查用户是否之前访问过（仅在客户端）
  //   if (typeof window !== 'undefined') {
  //     const hasVisited = localStorage.getItem('hasVisited')
  //     const lastVisitedTab = localStorage.getItem('lastVisitedTab')

  //     if (hasVisited && lastVisitedTab) {
  //       // 如果不是第一次访问，直接跳转到用户上次选择的tab
  //       setIsFirstVisit(false)
  //       router.replace(`/${lastVisitedTab}`)
  //     } else {
  //       // 第一次访问，显示欢迎页面
  //       setIsFirstVisit(true)
  //     }
  //   }
  // }, [router])

  // if (!isFirstVisit) {
  //   // 如果不是第一次访问，显示加载状态
  //   return (
  //     <div className="flex items-center justify-center min-h-screen">
  //       <div className="text-center">
  //         <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  //         <p className="mt-2 text-sm text-muted-foreground">正在加载...</p>
  //       </div>
  //     </div>
  //   )
  // }

  // 第一次访问，显示新的Landing页面
  return <LandingScreen />
}