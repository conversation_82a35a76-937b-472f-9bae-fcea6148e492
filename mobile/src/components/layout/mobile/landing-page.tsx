"use client"

import { useRouter } from 'next/navigation'
import { AstroIcon, BaziIcon, MeihuaIcon } from "@/components/icons/basic"
import { cn } from "@/lib/utils"

const categories = [
  {
    key: 'ziwei',
    title: '紫微斗数',
    subtitle: '东方星盘，洞悉人生格局',
    icon: AstroIcon,
    href: '/ziwei/case',
    cardStyle: 'full-width',
    bgColor: 'bg-white/50 dark:bg-white/30',
    iconBg: 'bg-purple-50',
    bgImg: '/images/astro_bg.png',
    iconColor: 'text-purple-600'
  },
  {
    key: 'bazi',
    title: '八字命理',
    subtitle: '四柱八字，探索命运奥秘',
    icon: BaziIcon,
    href: '/bazi/case',
    cardStyle: 'half-width',
    bgColor: 'bg-white/50 dark:bg-gray-800/50',
    iconBg: 'bg-amber-50',
    bgImg: '/images/bazi_bg.png',
    iconColor: 'text-amber-600'
  },
  {
    key: 'meihua',
    title: '梅花易数',
    subtitle: '万物皆数，解读卦象吉凶',
    icon: MeihuaIcon,
    href: '/meihua/case',
    cardStyle: 'half-width',
    bgColor: 'bg-white/50 dark:bg-white/30',
    iconBg: 'bg-green-50',
    bgImg: '/images/meihua_bg.png',
    iconColor: 'text-green-600'
  }
]

export default function LandingScreen() {
  const router = useRouter()

  const handleCategorySelect = (category: typeof categories[0]) => {
    // 保存用户选择到localStorage（仅在客户端）
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastVisitedTab', category.key)
      localStorage.setItem('hasVisited', 'true')
    }

    // 导航到对应页面
    router.push(category.href)
  }

  const fullWidthCard = categories.find(cat => cat.cardStyle === 'full-width')
  const halfWidthCards = categories.filter(cat => cat.cardStyle === 'half-width')

  return (
    <div className="h-full relative overflow-hidden">
      {/* 背景层 */}
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `url('/images/mobile_home_bg.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          filter: 'brightness(0.75) contrast(1.1)',
        }}
      />


      {/* 主要内容 */}
      <div className="relative z-10 min-h-screen px-6 pb-bottom-nav flex flex-col max-w-md mx-auto">
        {/* 标题部分 - 靠近顶部 */}
        <div className="text-center pt-16 pb-8">
          <h1 className="text-3xl font-light text-white dark:text-gray-200 mb-3">
            探索命理学的奥秘
          </h1>
        </div>

        {/* 卡片容器 - 垂直居中 */}
        <div className="flex-1 flex flex-col justify-center">
          <div className="space-y-4">
          {/* 全宽卡片 - 紫微斗数 */}
          {fullWidthCard && (
            <div
              onClick={() => handleCategorySelect(fullWidthCard)}
              className={cn(
                "glass-card backdrop-blur-xs rounded-3xl py-8 px-4 shadow-lg border border-white/50 dark:border-purple-700/30",
                "cursor-pointer transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]",
                fullWidthCard.bgColor
              )}
              style={{
                backgroundImage: `
            linear-gradient(to bottom right, rgba(255,255,255,1) 0%, rgba(255,255,255,0.6) 100%),
            url(${fullWidthCard.bgImg})
          `,
                backgroundSize: 'cover, cover',
                backgroundPosition: 'center, center',
                backgroundRepeat: 'no-repeat, no-repeat',
                filter: 'brightness(0.95) contrast(1.1)',
              }}
            >
              <div className="flex items-start space-x-3" >
                <div className="shrink-0">
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-2">
                    {fullWidthCard.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {fullWidthCard.subtitle}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 半宽卡片容器 */}
          <div className="flex gap-2">
            {halfWidthCards.map((category) => (
              <div
                key={category.key}
                onClick={() => handleCategorySelect(category)}
                className={cn(
                  "flex-1 glass-card backdrop-blur-xs rounded-3xl py-8 px-4 shadow-lg border",
                  "cursor-pointer transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]",
                  category.bgColor,
                  category.key === 'bazi'
                    ? "border-gray-100 dark:border-gray-700/50"
                    : "border-white/50 dark:border-green-700/30"
                )}
                style={{
                  backgroundImage: `
            linear-gradient(to bottom right, rgba(255,255,255,1) 0%, rgba(255,255,255,0.6) 100%),
            url(${category.bgImg})
          `,
                  backgroundSize: 'cover, cover',
                  backgroundPosition: 'center, center',
                  backgroundRepeat: 'no-repeat, no-repeat',
                  filter: 'brightness(0.95) contrast(1.1)',
                }}
              >
                <div className="flex flex-col items-start gap-6">
                  <div className="flex flex-col">
                    <h3 className="text-xl font-medium text-start text-gray-800 dark:text-gray-200 mb-2">
                      {category.title}
                    </h3>
                    <p className="text-start text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                      {category.subtitle}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          </div>
        </div>

      </div>
    </div>
  )
} 