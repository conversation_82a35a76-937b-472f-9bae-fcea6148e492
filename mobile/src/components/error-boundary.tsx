"use client"

import React from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>
}

// 错误UI组件，用于显示默认错误界面
function DefaultErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-red-600">
            页面加载出错
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-muted-foreground">
            抱歉，页面遇到了一些问题。这可能是由于网络不稳定或缓存问题导致的。
          </p>
          
          <div className="space-y-2">
            <Button 
              onClick={reset}
              className="w-full"
            >
              刷新页面
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/')}
              className="w-full"
            >
              返回首页
            </Button>
          </div>
          
          {process.env.NODE_ENV === 'development' && error && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm text-muted-foreground">
                查看错误详情
              </summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                {error.toString()}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // 检查是否是语法错误
    if (error.message.includes('Unexpected token') || error.message.includes('SyntaxError')) {
      console.warn('检测到JavaScript语法错误，可能是文件加载问题')
      
      // 尝试清除缓存并重新加载
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => registration.unregister())
        })
      }
    }
    
    this.setState({ error, errorInfo })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
    
    // 清除缓存并重新加载页面
    if (typeof window !== 'undefined') {
      // 清除localStorage和sessionStorage
      localStorage.clear()
      sessionStorage.clear()
      
      // 硬刷新页面
      window.location.reload()
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果有自定义的错误UI组件，使用它
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} reset={this.handleReset} />
      }

      // 使用默认错误UI
      return <DefaultErrorFallback error={this.state.error!} reset={this.handleReset} />
    }

    return this.props.children
  }
}

// Hook版本的错误边界（用于函数组件）
export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('应用错误:', error, errorInfo)
    
    // 如果是语法错误，尝试清除缓存
    if (error.message.includes('Unexpected token') || error.message.includes('SyntaxError')) {
      console.warn('检测到语法错误，建议清除缓存')
      
      // 显示用户友好的错误提示
      if (typeof window !== 'undefined') {
        const shouldReload = window.confirm(
          '页面加载出现问题，可能是缓存导致的。是否要清除缓存并重新加载？'
        )
        
        if (shouldReload) {
          localStorage.clear()
          sessionStorage.clear()
          window.location.reload()
        }
      }
    }
  }
} 