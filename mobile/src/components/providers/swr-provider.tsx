"use client"

import { ReactNode } from 'react'
import { SWRConfig } from 'swr'

interface SWRProviderProps {
  children: ReactNode
}

export function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig
      value={{
        // 全局SWR配置
        revalidateOnFocus: false,
        revalidateOnReconnect: true,
        shouldRetryOnError: false,
        errorRetryCount: 2,
        dedupingInterval: 2000,
        
        // 错误处理
        onError: (error, key) => {
          console.error('SWR Error:', { error, key })
        },
        
        // 成功回调
        onSuccess: (data, key, config) => {
          // 可以在这里添加全局成功处理逻辑
        }
      }}
    >
      {children}
    </SWRConfig>
  )
} 