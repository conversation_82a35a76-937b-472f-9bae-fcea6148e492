'use client'

import React from 'react'
import dynamic from 'next/dynamic'
import { HeroUIProvider } from '@heroui/system'
import { SpinLoading } from 'antd-mobile'

// 使用 dynamic import 来导入 HeroUIProvider
const DynamicHeroUIProvider = dynamic(
    () => import('@heroui/system').then((mod) => mod.HeroUIProvider),
    {
      // 关键：禁止在服务器端渲染这个组件
      ssr: false,
      
      // 可选但推荐：在组件加载时显示一个占位符，防止页面抖动
      loading: () => (
        <div style={{ position: 'fixed', top: '50%', left: '50%' }}>
          <SpinLoading />
        </div>
      ),
    }
  );
  
  export function HeroUIWrapper({ children }: { children: React.ReactNode }) {
    // 使用这个被动态导入的组件
    return <DynamicHeroUIProvider>{children}</DynamicHeroUIProvider>
  }