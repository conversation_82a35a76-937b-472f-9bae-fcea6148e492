"use client"

import { useState, FormEvent, useRef } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { BirthData, LocationItem } from "@/types/user"
import { ComboBoxResponsive } from "@/components/ui/combobox"
import { DateTimePicker } from "@/components/ui/datetime-picker"
import { isDST } from "@/lib/astro-utils"
import { Toast } from 'antd-mobile'

interface BirthInfoFormProps {
  title: string
  onSubmit: (birthData: BirthData, isSaveCaseDocument: boolean) => Promise<void>
  isLoading?: boolean
  error?: string | null
}

export function BirthInfoForm({ title, onSubmit, isLoading = false, error = null }: BirthInfoFormProps) {
  const [birthData, setBirthData] = useState<BirthData>({
    name: "",
    gender: "male",
    birthTime: "",
    isLunar: false,
    useTrueSolarTime: true,
    isDST: false,
    isEarlyOrLateNight: false,
    birthplace: "",
    longitude: 0,
    relationship: "other",
  })
  const location = { label: birthData.birthplace, value: birthData.longitude.toString() } as LocationItem
  const [isSaveCaseDocument, setIsSaveCaseDocument] = useState<boolean>(true)
  const [validationError, setValidationError] = useState<string>("")

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setBirthData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
    // 清除验证错误
    if (validationError) {
      setValidationError("")
    }
  }

  // 处理日期时间变化
  const handleDateTimeChange = (value: string) => {
    setBirthData((prev) => {
      const newData = { ...prev, birthTime: value }

      // 如果有有效的日期时间，自动更新isDST
      if (value) {
        try {
          const [datePart, timePart] = value.split(' ')
          if (datePart && timePart) {
            newData.isDST = isDST(datePart, timePart)
          }
        } catch (error) {
          // 如果解析失败，保持原状
        }
      }
      return newData
    })

    // 清除验证错误
    if (validationError) {
      setValidationError("")
    }
  }

  const handleRadioChange = (name: string, value: string) => {
    setBirthData((prev) => ({
      ...prev,
      [name]: name === "isLunar" ? value === "true" : value,
    }))
  }

  const handleBirthplaceSelect = (location: LocationItem | null) => {
    setBirthData((prev) => ({
      ...prev,
      birthplace: location?.label || "",
      longitude: parseFloat(location?.value || "0"),
    }))
    // 清除验证错误
    if (validationError) {
      setValidationError("")
    }
  }

  const handleCategoryChange = (category: string) => {
    setBirthData((prev) => ({
      ...prev,
      relationship: category as "family" | "classmate" | "friend" | "colleague" | "other",
    }))
  }

  // 定义按钮选项数组
  const genderOptions = [
    { value: 'male', label: '男' },
    { value: 'female', label: '女' }
  ]

  const calendarOptions = [
    { value: 'false', label: '公历' },
    { value: 'true', label: '农历' }
  ]

  const relationshipOptions = [
    { value: 'other', label: '其他' },
    { value: 'family', label: '家人' },
    { value: 'friend', label: '朋友' },
    { value: 'colleague', label: '同事' },
    { value: 'classmate', label: '同学' }
  ]

  // 通用按钮渲染函数
  const renderButton = (
    option: { value: string; label: string },
    isSelected: boolean,
    onClick: () => void,
    className: string = "px-8 py-1"
  ) => (
    <button
      key={option.value}
      type="button"
      aria-label={option.label}
      tabIndex={0}
      onClick={onClick}
      className={`${className} rounded-2xl md:py-1 border transition-all text-sm md:text-base focus:outline-hidden focus:ring-2 focus:ring-primary ${isSelected ? "bg-primary text-white" : "bg-white text-primary"
        }`}
    >
      {option.label}
    </button>
  )

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    // 验证姓名是否已输入
    if (!birthData.name) {
      setValidationError("请输入名称")
      Toast.show({
        content: "请输入名称",
        icon: 'fail',
      })
      return
    }
    // 验证出生时间是否已输入
    if (!birthData.birthTime) {
      setValidationError("请输入出生时间")
      Toast.show({
        content: "请输入出生时间",
        icon: 'fail',
      })
      return
    }
    // 验证出生地是否已选择
    if (!birthData.birthplace || birthData.longitude === 0) {
      setValidationError("请选择出生地点")
      Toast.show({
        content: "请选择出生地点",
        icon: 'fail',
      })
      return
    }

    // 清除验证错误并提交
    setValidationError("")
    await onSubmit(birthData, isSaveCaseDocument)
  }

  return (
    <div className="flex w-full gap-8 justify-center items-center">
      <Card className="flex w-full shadow-lg rounded-2xl px-0.5 md:px-2 border-0 bg-white py-0 max-w-xl gap-0">
        <CardHeader className="flex border-b px-0 mx-4 [.border-b]:pb-1 rounded-t-2xl items-center">
          <CardTitle className="text-base md:text-xl font-bold py-3 md:py-6 font-serif text-primary">{title}</CardTitle>
        </CardHeader>
        <CardContent className="pb-2 mb-8 px-3 text-sm md:text-base">
          <form onSubmit={handleSubmit} className="flex flex-col w-full pt-4 gap-4">

            {/* 姓名 */}
            <div className="flex flex-col gap-0.5">
              <div className="flex items-center md:gap-2 input-container">
                <label htmlFor="name" className="w-20 md:w-24 text-sm md:text-base shrink-0">
                  名称 <span className="text-destructive">*</span>
                </label>
                <div className="flex-1">
                  <Input
                    id="name"
                    name="name"
                    value={birthData.name}
                    onChange={handleChange}
                    placeholder="请输入名称"
                    className="rounded-xl py-1.5 h-9 bg-white border focus:ring-2 focus:ring-primary"
                    style={{ fontSize: '16px' }}
                  />
                </div>
              </div>
              {/* {validationError && birthData.name === "" ? (
                <p className="flex text-destructive justify-center text-xs">请输入名称</p>
              ) : null} */}
            </div>

            {/* 性别 */}
            <div className="flex items-center md:gap-2">
              <label className="w-20 md:w-24 text-sm md:text-base shrink-0">性别</label>
              <div className="flex gap-3 items-center flex-1">
                {genderOptions.map(option =>
                  renderButton(
                    option,
                    birthData.gender === option.value,
                    () => handleRadioChange("gender", option.value),
                    "px-5 py-1"
                  )
                )}
              </div>
            </div>

            {/* 出生时间 */}
            <div className="flex flex-col gap-0.5">
              <div className="flex items-center md:gap-2 input-container">
                <label className="w-20 md:w-24 text-sm md:text-base shrink-0">
                  出生时间 <span className="text-destructive">*</span>
                </label>
                 <div className="flex-1">
                  <DateTimePicker
                    value={birthData.birthTime}
                    onSelect={handleDateTimeChange}
                  // placeholder="例: 1991-05-30 14:30"
                  /> 
                </div>
              </div>
              {/* {validationError && birthData.birthTime === "" ? (
                <p className="text-destructive text-xs flex justify-center">请输入出生时间</p>
              ) : null} */}
            </div>

            {/* 历法选择 */}
            <div className="flex items-center md:gap-2">
              <label className="w-20 md:w-24 text-sm md:text-base shrink-0">历法</label>
              <div className="flex gap-3 flex-1">
                {calendarOptions.map(option =>
                  renderButton(
                    option,
                    (option.value === 'true') === birthData.isLunar,
                    () => handleRadioChange("isLunar", option.value),
                    "px-5 py-1"
                  )
                )}
              </div>
            </div>

            {/* 特殊时制 */}
            <div className="flex items-center md:gap-2">
              <label className="w-20 md:w-24 text-sm md:text-base shrink-0">特殊时制</label>
              <div className="flex flex-wrap gap-3 items-center flex-1">
                <label className="flex items-center gap-1.5 cursor-pointer">
                  <input
                    id="isDST"
                    name="isDST"
                    type="checkbox"
                    checked={birthData.isDST}
                    onChange={handleChange}
                    className="h-4 w-4 accent-primary border-primary rounded focus:ring-primary"
                  />
                  <span className="text-muted-foreground text-sm">夏令时</span>
                </label>
                <label className="flex items-center gap-1.5 cursor-pointer">
                  <input
                    id="useTrueSolarTime"
                    name="useTrueSolarTime"
                    type="checkbox"
                    checked={birthData.useTrueSolarTime}
                    onChange={handleChange}
                    className="h-4 w-4 accent-primary border-primary rounded focus:ring-primary"
                  />
                  <span className="text-muted-foreground text-sm">真太阳时</span>
                </label>
              </div>
            </div>

            {/* 出生地点 */}
            <div className="flex flex-col gap-0.5">
              <div className="flex items-center md:gap-2 input-container">
                <label htmlFor="birthplace" className="w-20 md:w-24 text-sm md:text-base shrink-0">
                  出生地址 <span className="text-destructive">*</span>
                </label>
                <div className="flex-1">
                  <ComboBoxResponsive
                    placeholder="请选择出生地"
                    jsonUrl="/assets/location.json"
                    onSelect={handleBirthplaceSelect}
                    value={location}
                  />
                </div>
              </div>
              {/* {validationError && birthData.birthplace === "" && (
                <p className="flex justify-center text-xs text-destructive mt-1">请选择出生地</p>
              )} */}
            </div>

            {/* 案例分类 */}
            <div className="flex items-start md:gap-2">
              <label className="w-20 md:w-24 text-sm md:text-base shrink-0 pt-1">案例分类</label>
              <div className="flex flex-wrap gap-1.5 flex-1">
                {relationshipOptions.map(option =>
                  renderButton(
                    option,
                    birthData.relationship === option.value,
                    () => handleCategoryChange(option.value),
                    "px-3 py-1"
                  )
                )}
              </div>
            </div>

            {/* 保存档案 */}
            <div className="flex items-center md:gap-2">
              <label className="w-20 md:w-24 text-sm md:text-base shrink-0">保存档案</label>
              <div className="flex items-center flex-1">
                <Switch id="isSaveCaseDocument" checked={isSaveCaseDocument} onCheckedChange={setIsSaveCaseDocument} />
              </div>
            </div>

            {/* 开始测算按钮 */}
            <div className="flex justify-center mt-4">
              <Button type="submit" disabled={isLoading} className="w-32 rounded-2xl h-10 text-base">
                {isLoading ? "计算中..." : "开始测算"}
              </Button>
            </div>

            {/* {validationError && Toast.show({
              content: validationError,
              icon: 'fail',
            })} 
            {!validationError && error && Toast.show({
              content: error,
              icon: 'fail',
            })}  */}
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 