import { Orbit, Vault, Flower, User} from 'lucide-react';
interface IconProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | number
  className?: string
}

// 将 FontAwesome 尺寸映射到对应的类名
const getFontAwesomeSize = (size: IconProps['size']) => {
  if (typeof size === 'number') {
    return `fa-${size}x`
  }
  
  switch (size) {
    case 'sm': return 'fa-sm'
    case 'md': return 'fa-lg'
    case 'lg': return 'fa-xl'
    case 'xl': return 'fa-2xl'
    case '2xl': return 'fa-2xl'
    default: return 'fa-2xl'
  }
}

const getLucideSize = (size: IconProps['size']) => {
  if (typeof size === 'number') {
    return size
  }
  switch (size) {
    case 'sm': return 16
    case 'md': return 24
    case 'lg': return 32
    case 'xl': return 40
    case '2xl': return 48
    default: return 24
  }
}

// 将尺寸映射到 Tailwind CSS 类名
const getTailwindSize = (size: IconProps['size']) => {
  if (typeof size === 'number') {
    return `w-${size} h-${size}`
  }
  
  switch (size) {
    case 'sm': return 'w-4 h-4'
    case 'md': return 'w-6 h-6'
    case 'lg': return 'w-8 h-8'
    case 'xl': return 'w-10 h-10'
    case '2xl': return 'w-12 h-12'
    default: return 'w-8 h-8'
  }
}

const MeihuaLightIcon = ({ size = 'lg', className = '' }: IconProps) => (
  <Flower size={getLucideSize(size)} className={className} strokeWidth={1.25}/>
)

const MeihuaIcon = ({ size = 'lg', className = '' }: IconProps) => (
    <svg 
      className={`${getTailwindSize(size)} text-primary ${className}`} 
      viewBox="0 0 100 100" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
        <rect x="10" y="15" width="90" height="15" rx="2" fill="currentColor" />
        <rect x="10" y="40" width="40" height="15" rx="2" fill="currentColor" />
        <rect x="60" y="40" width="40" height="15" rx="2" fill="currentColor" />
        <rect x="10" y="65" width="90" height="15" rx="2" fill="currentColor" />
    </svg>
)

const BaziIcon = ({ size = '2xl', className = '' }: IconProps) => (
    <i className={`fa-solid fa-star-and-crescent ${getFontAwesomeSize(size)} text-primary ${className}`}></i>
)

const BaziLightIcon = ({ size = '2xl', className = '' }: IconProps) => (
  <Orbit size={getLucideSize(size)} className={className} strokeWidth={1.25}/>
)

const AstroIcon = ({ size = '2xl', className = '' }: IconProps) => (
    <i className={`fas fa-star-of-david text-primary ${getFontAwesomeSize(size)} ${className}`}></i>
)

const AstroLightIcon = ({ size = '2xl', className = '' }: IconProps) => (
    <Vault size={getLucideSize(size)} className={className} strokeWidth={1.25}/>
)

const UserLightIcon = ({ size = '2xl', className = '' }: IconProps) => (
  <User size={getLucideSize(size)} className={className} strokeWidth={1.25}/>
)

export { BaziIcon, BaziLightIcon, AstroIcon, AstroLightIcon, MeihuaLightIcon, MeihuaIcon, UserLightIcon };