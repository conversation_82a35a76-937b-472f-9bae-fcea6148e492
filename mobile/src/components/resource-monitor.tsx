"use client"

import { useEffect } from 'react'

/**
 * 资源加载监控组件
 * 监控外部资源的加载状态，提供降级方案
 */
export function ResourceMonitor() {
  useEffect(() => {
    // 检查 Font Awesome 是否加载成功
    const checkFontAwesome = () => {
      const testElement = document.createElement('i')
      testElement.className = 'fas fa-home'
      testElement.style.display = 'none'
      document.body.appendChild(testElement)
      
      const computedStyle = window.getComputedStyle(testElement, ':before')
      const content = computedStyle.getPropertyValue('content')
      
      document.body.removeChild(testElement)
      
      // 如果 content 不是期望的图标，说明 Font Awesome 没有加载
      if (!content || content === 'none' || content === '""') {
        console.warn('Font Awesome 未正确加载，启用备用方案')
        
        // 添加备用样式
        const fallbackStyle = document.createElement('style')
        fallbackStyle.textContent = `
          .fa, .fas, .far, .fal, .fab {
            font-family: system-ui, -apple-system, sans-serif !important;
          }
          .fa-home:before { content: "🏠"; }
          .fa-user:before { content: "👤"; }
          .fa-search:before { content: "🔍"; }
          .fa-menu:before { content: "☰"; }
          .fa-times:before { content: "✕"; }
        `
        document.head.appendChild(fallbackStyle)
      }
    }
    
    // 检查 Next.js 字体是否加载成功
    const checkNextJSFonts = () => {
      document.fonts.ready.then(() => {
        // 检查 CSS 变量是否存在（Next.js 字体优化方式）
        const rootStyles = window.getComputedStyle(document.documentElement)
        const fontSansVar = rootStyles.getPropertyValue('--font-sans')
        
        if (fontSansVar) {
          return
        }
        
        // 回退检查：检查 Inter 字体是否可用
        const hasInter = document.fonts.check('16px Inter, system-ui')
        if (!hasInter) {
          console.warn('⚠️ Inter 字体未正确加载，使用系统字体备用方案')
          
          // 添加系统字体备用方案
          const fallbackStyle = document.createElement('style')
          fallbackStyle.textContent = `
            :root {
              --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            body, .font-sans {
              font-family: var(--font-sans) !important;
            }
          `
          document.head.appendChild(fallbackStyle)
        } 
      }).catch(() => {
        console.warn('❌ 字体检查失败，使用系统字体')
      })
    }
    
    // 延迟检查，确保资源有足够时间加载
    const timer = setTimeout(() => {
      checkFontAwesome()
      checkNextJSFonts()
    }, 2000)
    
    return () => clearTimeout(timer)
  }, [])

  // 这个组件不渲染任何内容
  return null
}

/**
 * 网络状态监控组件
 */
export function NetworkMonitor() {
  useEffect(() => {
    const handleOnline = () => {
      console.log('网络已连接')
      // 可以在这里触发重新加载失败的资源
    }
    
    const handleOffline = () => {
      console.log('网络已断开')
      // 可以在这里显示离线提示
    }
    
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
  
  return null
} 