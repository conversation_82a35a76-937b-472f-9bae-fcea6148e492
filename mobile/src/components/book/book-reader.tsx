import { useState, useEffect, useCallback } from 'react';
import { useBookDetail, useChapterContent } from '@/hooks/use-api';
import { DisplayMode } from '@/types/book';
import { PaginatedView } from '@/components/book/paginated-view';
import { BookInfo } from '@/types/book';
import { STORAGE_KEYS } from '@/lib/constant';
import MobileNav from "@/components/layout/mobile/nav"
import BookDetailBottom from "@/components/book/book-detail-bottom"

//封面页
const CoverPage = ({ bookInfo, onStartReading, onToggleBars, showBars, setShowBars }: {
    bookInfo: BookInfo;
    onStartReading: () => void;
    onToggleBars: () => void;
    showBars: boolean;
    setShowBars: (bool: boolean) => void;
}) => {
    // 实现三区域点击逻辑，与PaginatedView保持一致
    const handleCoverClick = (e: React.MouseEvent<HTMLDivElement>) => {

        const { clientWidth } = e.currentTarget;
        const clickX = e.clientX - e.currentTarget.getBoundingClientRect().left;

        if (clickX > clientWidth / 3 * 2) {
            if (showBars) {
                setShowBars(false)
                return
            }
            // 右侧点击：开始阅读（翻到下一页）
            onStartReading();
        } else if (clickX < clientWidth / 3) {
            // 左侧点击：无动作（已经是第一页）
            return;
        } else {
            // 中部点击：弹出顶部导航栏和底部标签栏
            onToggleBars();
        }
    };

    return (
        <div
            className="w-full h-full flex flex-col justify-between p-8 bg-background select-none cursor-pointer hover:bg-muted/20"
            onClick={handleCoverClick}
        >
            <div className="text-center">
                <h1 className="text-4xl font-bold font-serif text-foreground pb-6">{bookInfo.title}</h1>
                <p className="text-lg text-muted-foreground">{bookInfo.author}</p>
            </div>
            <div className="flex-grow py-6 overflow-y-auto">
                <p className="text-base text-foreground leading-loose text-justify">{bookInfo.description}</p>
            </div>
        </div>
    );
};

export const BookReader = ({ bookId }: { bookId: string | null }) => {
    //基础状态
    const [bookInfo, setBookInfo] = useState<BookInfo | null>(null)
    const [error, setError] = useState<string | null>(null);
    const [isReading, setIsReading] = useState(false);

    const [selectedChapterId, setSelectedChapterId] = useState<string | null>(null);
    const [selectedChapterTitle, setSelectedChapterTitle] = useState<string | null>(null);
    const [displayMode, setDisplayMode] = useState<DisplayMode>('original');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const [jumpToLastPage, setJumpToLastPage] = useState(false); // **新增**: 用于向前翻章的协调状态
    const [showBars, setShowBars] = useState(false);    // 控制是否展示顶部导航栏和底部功能标签栏

    //获取书籍内容
    const { chapters, isLoading: isBookLoading, isError, error: detailError } = useBookDetail(bookId)
    // 获取当前选中章节的内容
    const { content: chapterContent, isLoading: isChapterLoading } = useChapterContent(selectedChapterId)

    // 初始化书籍信息
    useEffect(() => {
        if (!bookId) {
            setError("缺少书籍ID");
            return;
        }

        if (typeof window !== 'undefined') {
            console.log("初始化书籍信息 - bookId:", bookId);
            const str = localStorage.getItem(STORAGE_KEYS.CURRENT_BOOK);
            if (str) {
                try {
                    const parsed = JSON.parse(str);
                    if (parsed.book_id === bookId) {
                        setBookInfo(parsed);
                        // 加载阅读进度
                        const progressStr = localStorage.getItem(`${STORAGE_KEYS.READING_PROGRESS}_${bookId}`);
                        if (progressStr) {
                            try {
                                const progress = JSON.parse(progressStr);
                                // 恢复章节相关状态并直接进入阅读模式
                                setSelectedChapterId(progress.chapterId);
                                setSelectedChapterTitle(progress.chapterTitle);
                                setDisplayMode(progress.displayMode);
                                setCurrentPage(progress.page);
                                if (progress.page > 0) {
                                    setIsReading(true);
                                } else {
                                    setIsReading(false);
                                }
                                console.log("恢复阅读进度:", progress);
                            } catch (e) {
                                console.warn("解析阅读进度失败:", e);
                            }
                        }
                        return;
                    }
                } catch (e) {
                    console.warn("解析书籍信息失败:", e);
                }
            }
            setError("请从书籍列表进入");
        }
    }, [bookId])

    //处理向前翻页时，在新章节渲染并计算完总页数后，跳转到最后一页
    useEffect(() => {
        if (jumpToLastPage && totalPages > 0) {
            setCurrentPage(totalPages);
            console.log("跳转到最后一页:", totalPages);
            setJumpToLastPage(false); // 重置标志
        }
    }, [totalPages, jumpToLastPage]);

    // 保存阅读进度到 localStorage
    useEffect(() => {
        if (selectedChapterId) {
            const progress = {
                chapterId: selectedChapterId,
                chapterTitle: selectedChapterTitle,
                displayMode: displayMode,
                page: currentPage,
            };
            localStorage.setItem(`${STORAGE_KEYS.READING_PROGRESS}_${bookId}`, JSON.stringify(progress));
            console.log("保存阅读进度:", progress);
        }
    }, [bookId, isReading, selectedChapterId, selectedChapterTitle, displayMode, currentPage]);

    //开始阅读，设置第一章节信息
    const handleStartReading = () => {
        // 防止重复点击
        if (isReading) return;

        // 如果没有加载到进度，则返回书籍封面
        if (chapters.length > 0 && bookInfo) {
            // 更健壮的查找第一章逻辑
            let firstChapter = null

            // 首先尝试按照 main_chapter_level 查找
            if (bookInfo.main_chapter_level) {
                firstChapter = chapters.find(c => c.level === bookInfo.main_chapter_level)
            }

            // 如果没找到，尝试查找最小级别的章节
            if (!firstChapter) {
                const minLevel = Math.min(...chapters.map(c => c.level))
                firstChapter = chapters.find(c => c.level === minLevel)
            }

            // 最后的备选方案：使用第一个章节
            if (!firstChapter) {
                firstChapter = chapters[0]
            }

            if (firstChapter) {
                console.log('从首页进入第一章:', firstChapter.title)
                handleSelectChapter(firstChapter.chapter_id, firstChapter.title)
                // 直接设置阅读状态，与正文翻页方式一致
                setIsReading(true);
            } else {
                console.error('书籍内容出错,无法找到第一章，chapters:', chapters, 'bookInfo:', bookInfo)
            }
        } else {
            console.log('chapters未加载或bookInfo不存在:', { chaptersLength: chapters.length, bookInfo })
        }
    };

    // 处理章节选择
    const handleSelectChapter = (chapterId: string, chapterTitle: string) => {
        console.log('切换章节:', chapterId)

        // 清除当前状态，防止新章节加载期间显示旧内容
        setCurrentPage(1)
        setTotalPages(0)
        setSelectedChapterId(chapterId)
        setSelectedChapterTitle(chapterTitle)
    };

    const goToNext = () => {
        if (showBars) {
            setShowBars(false)
            return
        }
        if (currentPage < totalPages) {
            setCurrentPage(c => c + 1);
        } else {
            const currentIndex = chapters.findIndex(c => c.chapter_id === selectedChapterId);
            if (currentIndex !== -1 && currentIndex < chapters.length - 1 && bookInfo) {
                // 从当前章节后面开始查找，返回第一个符合main_chapter_level的章节

                const nextChapter = chapters.slice(currentIndex + 1).find(
                    chapter => chapter.level === bookInfo.main_chapter_level
                );
                if (nextChapter) {
                    handleSelectChapter(nextChapter.chapter_id, nextChapter.title);
                }
            }
        }
    };

    const goToPrev = () => {
        if (showBars) {
            setShowBars(false)
            return
        }
        if (currentPage > 1) {
            setCurrentPage(c => c - 1);
        } else {
            //当前章节的第一页，找前一章节
            const currentIndex = chapters.findIndex(c => c.chapter_id === selectedChapterId);
            if (currentIndex !== -1 && currentIndex > 0 && bookInfo) {
                // 从当前章节前面开始查找，返回最后一个符合main_chapter_level的章节
                const prevChapter = chapters.slice(0, currentIndex).reverse().find(
                    chapter => chapter.level === bookInfo.main_chapter_level
                );
                if (prevChapter) {
                    handleSelectChapter(prevChapter.chapter_id, prevChapter.title);
                    setJumpToLastPage(true);
                    return
                }
            }

            // 如果当前是第一章的第一页，回到封面页
            console.log('回到封面页');
            setIsReading(false);
            setShowBars(false); // 同时隐藏导航栏和标签栏
            setCurrentPage(0); // 重置页面状态，确保下次进入时从封面页开始
        }
    };

    const handleChapterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedChapterId(e.target.value);
        setCurrentPage(1);
        setTotalPages(0);
        setIsReading(true);
    };

    const handleViewModeChange = (mode: DisplayMode) => {
        if (mode !== displayMode) {
            setDisplayMode(mode);
            setCurrentPage(1);
            setTotalPages(0);
        }
    };

    const handleToggleBars = () => {
        setShowBars(s => !s);
    }

    if (error) {
        return <div className="w-full h-screen flex items-center justify-center bg-red-100 text-red-700">{error}</div>;
    }

    if (!bookInfo || isBookLoading) {
        return <div className="w-full h-screen flex items-center justify-center bg-stone-100"><p className="animate-pulse">正在加载书籍...</p></div>;
    }

    return (
        <div className="w-full max-w-2xl mx-auto h-full bg-background flex flex-col relative overflow-hidden">
            {/* 封面页 - 直接显示/隐藏切换，与正文翻页方式一致 */}
            {!isReading && (
                <div className="absolute inset-0">
                    <CoverPage
                        bookInfo={bookInfo}
                        onStartReading={handleStartReading}
                        onToggleBars={handleToggleBars}
                        showBars={showBars}
                        setShowBars={setShowBars}
                    />
                </div>
            )}

            {/* 阅读页 - 直接显示/隐藏切换，与正文翻页方式一致 */}
            {isReading && (
                <div className="absolute inset-0">
                    <div className="flex flex-col h-full w-full overflow-hidden bg-background">
                        {isChapterLoading || !chapterContent ? (
                            <div className="w-full h-full flex items-center justify-center">
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4 mx-auto"></div>
                                    <p className="text-foreground text-base font-medium mb-2">
                                        {isChapterLoading ? '正在加载章节内容' : '无法加载内容'}
                                    </p>
                                    {isChapterLoading && (
                                        <p className="text-muted-foreground text-sm">请稍候...</p>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <PaginatedView
                                chapterId={selectedChapterId!}
                                chapterTitle={selectedChapterTitle!}
                                content={chapterContent}
                                displayMode={displayMode}
                                currentPage={currentPage}
                                setTotalPages={setTotalPages}
                                onGoToNext={goToNext}
                                onGoToPrev={goToPrev}
                                onToggleBars={handleToggleBars}
                            />
                        )}
                        <div className="flex-shrink-0 flex justify-end text-sm text-foreground px-4 py-2">
                            {totalPages > 0 ? `第 ${currentPage} / ${totalPages} 页` : '\u00A0'}
                        </div>
                    </div>
                </div>
            )}

            <header className={`fixed flex top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm shadow-md transition-all duration-300 ease-in-out ${showBars ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-full'}`}>
                <MobileNav title={bookInfo.title} />
            </header>
            <footer className={`fixed bottom-0 left-0 right-0 z-10 backdrop-blur-sm shadow-[0_-2px_5px_rgba(0,0,0,0.1)] transition-all duration-300 ease-in-out ${showBars ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-full'}`}>
                <BookDetailBottom chapters={chapters} main_chapter_level={bookInfo.main_chapter_level}/>
            </footer>

        </div>

    );
};