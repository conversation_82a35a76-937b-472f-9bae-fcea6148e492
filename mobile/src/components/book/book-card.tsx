"use client"

import { Card } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { useCallback } from "react"
import { STORAGE_KEYS } from "@/lib/constant"

interface BookCardProps {
  title: string
  author: string | null
  description: string | null
  book_id: string
  category: string
  main_chapter_level: number
}

export function BookCard({ title, author = "佚名", description = "暂无简介", book_id, category, main_chapter_level }: BookCardProps) {
  const router = useRouter()
  const handleClick = useCallback(async () => {
    // 只在客户端访问localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.CURRENT_BOOK)
      localStorage.setItem(STORAGE_KEYS.CURRENT_BOOK, JSON.stringify({
        book_id,
        title,
        author,
        description,
        main_chapter_level,
      }))
    }

    // 使用 setTimeout 确保状态更新完成后再跳转
    setTimeout(() => {
      router.push(`/book/detail?book_id=${book_id}&category=${category}`)
    }, 0)
  }, [title, author, description, main_chapter_level, book_id, category, router])

  return (
    <div onClick={handleClick}>
      <Card className="group flex flex-col p-0 cursor-pointer aspect-[3/4.2] gap-0 overflow-hidden relative rounded-lg bg-white shadow-[0_2px_8px_rgba(0,0,0,0.04),0_1px_2px_rgba(0,0,0,0.08)] transition-all duration-300">
        {/* 左侧：古籍封面 */}
        <div className="relative h-full shrink-0 bg-linear-to-br from-[#F5EFE6] to-[#E8DFD3]">
          {/* 红色印章效果 */}
          <div className="absolute top-2 left-2 w-8 h-8 rounded-full bg-red-800/10 
            flex items-center justify-center transform -rotate-12 opacity-80 z-10">
            <div className="text-red-800 text-xs font-kai">典藏</div>
          </div>

          {/* 书籍封面主体 */}
          <div className="h-full flex justify-between px-4 py-6">
            {/* 作者区域 */}
            <div className="flex-col writing-mode-vertical-rl text-end text-sm font-kai text-stone-600">
              {author} 著
            </div>

            {/* 书名区域 */}
            <div className="writing-mode-vertical-rl text-base font-bold font-song tracking-wider text-stone-800">
              {title}
            </div>
          </div>

          {/* 书脊装饰效果 */}
          <div className="absolute right-0 top-0 bottom-0 w-2">
            <div className="h-full bg-linear-to-l from-stone-200/50 to-transparent"></div>
            <div className="absolute right-0 top-0 bottom-0 w-[1px] bg-stone-300/50"></div>
          </div>
        </div>
      </Card>
    </div>
  )
}