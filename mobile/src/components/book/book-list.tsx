"use client"
import { useSearchParams } from "next/navigation"
import { BookCard } from "./book-card"
import { BookPanelSkeleton } from "./book-list-skeleton"
import { useBookList } from "@/hooks/use-api"
import { Suspense } from "react"


export function BookList() {
  const searchParams = useSearchParams()
  // 从URL参数获取分类，如果没有则使用第一个分类
  const categoryFromUrl = searchParams.get('category') || null

  const { books, isLoading, error } = useBookList({
    page: '1',
    size: '100',
    is_completed: 'false',
    category: categoryFromUrl,
  })
  if (isLoading) {
    return <BookPanelSkeleton />
  }
  if (error) {
    return <div className="text-center text-red-500 py-20">加载失败: {error}</div>
  }
  return (
    <div className="grid grid-cols-3 md:grid-cols-4 py-4 px-4">
      <Suspense fallback={<BookPanelSkeleton />}>
        {books.map(book => (
          <BookCard key={book.book_id} {...book} />
        ))}
      </Suspense>
    </div>
  )
}