import { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import { ChapterContent } from '@/types/book';
import { DisplayMode } from '@/types/book';

interface PaginatedViewProps {
    chapterId: string;
    chapterTitle: string;
    content: ChapterContent;
    displayMode: DisplayMode;
    currentPage: number;
    setTotalPages: (count: number) => void;
    onGoToNext: () => void;
    onGoToPrev: () => void;
    onToggleBars: () => void;
}


export function PaginatedView({ chapterId, chapterTitle, content, displayMode, currentPage, setTotalPages, onGoToNext, onGoToPrev, onToggleBars }: PaginatedViewProps) {
    const [paginatedContent, setPaginatedContent] = useState<string[]>([]);
    const [isPaginating, setIsPaginating] = useState(false); // 初始为false，避免不必要的loading状态
    const [previousContent, setPreviousContent] = useState<string>(''); // 保存上一次的内容，避免闪现

    const containerRef = useRef<HTMLDivElement>(null);
    const measurerRef = useRef<HTMLDivElement>(null);
    // 使用 useRef 来持久化缓存，避免在重渲染时丢失
    const paginationCache = useRef<Record<string, string[]>>({});

    // 核心分页算法：基于元素测量, 返回page[]
    const paginate = useCallback(async () => {
        if (!containerRef.current || !measurerRef.current || !content) return [];

        const container = containerRef.current;
        const measurer = measurerRef.current;
        const containerHeight = container.clientHeight;

        // 确保测量器与容器宽度一致
        measurer.style.width = `${container.clientWidth}px`;
        measurer.innerHTML = ''; // 清空测量器

        // 1. 根据 viewMode 生成所有需要渲染的 DOM 元素节点
        const elementsToPaginate: HTMLElement[] = [];

        // 添加章节标题作为第一个元素（仅在第一页显示）
        const titleElement = document.createElement('div');
        titleElement.className = 'text-center pb-8 mb-6';
        const titleH2 = document.createElement('h2');
        titleH2.className = 'text-2xl font-bold text-foreground font-serif';
        titleH2.textContent = chapterTitle;
        titleElement.appendChild(titleH2);
        elementsToPaginate.push(titleElement);

        content.content.forEach(section => {
            const sectionWrapper = document.createElement('div');
            sectionWrapper.className = 'pb-4'; // section 之间的间距

            if (displayMode === 'original' || displayMode === 'both') {
                const pContent = document.createElement('p');
                pContent.className = 'text-foreground';
                pContent.textContent = section.content;
                sectionWrapper.appendChild(pContent);

                if (section.quote) {
                    const pQuote = document.createElement('p');
                    pQuote.className = 'text-foreground bg-paper-secondary py-2';
                    pQuote.textContent = section.quote;
                    sectionWrapper.appendChild(pQuote);
                }
            }

            if (displayMode === 'translation' || displayMode === 'both') {
                if (section.translation) {
                    const pTranslation = document.createElement('p');
                    pTranslation.className = 'text-blue-700 mt-1';
                    pTranslation.textContent = section.translation;
                    sectionWrapper.appendChild(pTranslation);
                }
            }
            elementsToPaginate.push(sectionWrapper);
        });

        // 按字符分页的辅助函数
        const paginateElementByCharacter = (element: HTMLElement): HTMLElement[] => {
            const elements: HTMLElement[] = [];
            const tempMeasurer = measurer.cloneNode(false) as HTMLDivElement;
            tempMeasurer.style.position = 'absolute';
            tempMeasurer.style.top = '-9999px';
            tempMeasurer.style.width = `${container.clientWidth}px`;
            document.body.appendChild(tempMeasurer);

            try {
                // 获取所有文本节点和元素
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT,
                    null
                );

                let currentElement = element.cloneNode(false) as HTMLElement;
                let currentNode: Node | null;
                let textBuffer = '';

                while (currentNode = walker.nextNode()) {
                    if (currentNode.nodeType === Node.TEXT_NODE) {
                        const text = currentNode.textContent || '';

                        // 逐字符添加
                        for (let i = 0; i < text.length; i++) {
                            const char = text[i];
                            const testElement = currentElement.cloneNode(true) as HTMLElement;

                            // 更新文本内容
                            if (testElement.lastChild && testElement.lastChild.nodeType === Node.TEXT_NODE) {
                                testElement.lastChild.textContent = textBuffer + char;
                            } else {
                                testElement.appendChild(document.createTextNode(textBuffer + char));
                            }

                            tempMeasurer.innerHTML = '';
                            tempMeasurer.appendChild(testElement);

                            if (tempMeasurer.scrollHeight > containerHeight) {
                                // 当前字符会导致超高，保存当前页面
                                if (textBuffer.length > 0) {
                                    const pageElement = currentElement.cloneNode(true) as HTMLElement;
                                    if (pageElement.lastChild && pageElement.lastChild.nodeType === Node.TEXT_NODE) {
                                        pageElement.lastChild.textContent = textBuffer;
                                    } else {
                                        pageElement.appendChild(document.createTextNode(textBuffer));
                                    }
                                    elements.push(pageElement);
                                }

                                // 开始新页面
                                currentElement = element.cloneNode(false) as HTMLElement;
                                textBuffer = char;
                            } else {
                                textBuffer += char;
                            }
                        }
                    } else if (currentNode.nodeType === Node.ELEMENT_NODE) {
                        // 处理元素节点（如 br, span 等）
                        const elementNode = currentNode as HTMLElement;
                        const testElement = currentElement.cloneNode(true) as HTMLElement;
                        testElement.appendChild(elementNode.cloneNode(true));

                        tempMeasurer.innerHTML = '';
                        tempMeasurer.appendChild(testElement);

                        if (tempMeasurer.scrollHeight > containerHeight) {
                            // 元素导致超高，保存当前页面并开始新页面
                            if (currentElement.children.length > 0 || textBuffer.length > 0) {
                                if (textBuffer.length > 0 && currentElement.lastChild && currentElement.lastChild.nodeType === Node.TEXT_NODE) {
                                    currentElement.lastChild.textContent = textBuffer;
                                } else if (textBuffer.length > 0) {
                                    currentElement.appendChild(document.createTextNode(textBuffer));
                                }
                                elements.push(currentElement);
                            }

                            currentElement = element.cloneNode(false) as HTMLElement;
                            currentElement.appendChild(elementNode.cloneNode(true));
                            textBuffer = '';
                        } else {
                            if (textBuffer.length > 0) {
                                currentElement.appendChild(document.createTextNode(textBuffer));
                                textBuffer = '';
                            }
                            currentElement.appendChild(elementNode.cloneNode(true));
                        }
                    }
                }

                // 添加最后一页的剩余内容
                if (currentElement.children.length > 0 || textBuffer.length > 0) {
                    if (textBuffer.length > 0) {
                        if (currentElement.lastChild && currentElement.lastChild.nodeType === Node.TEXT_NODE) {
                            currentElement.lastChild.textContent += textBuffer;
                        } else {
                            currentElement.appendChild(document.createTextNode(textBuffer));
                        }
                    }
                    elements.push(currentElement);
                }

            } finally {
                document.body.removeChild(tempMeasurer);
            }

            return elements;
        };

        // 2. 逐个元素添入测量器，判断是否超出一页
        const pages: string[] = [];
        for (const el of elementsToPaginate) {
            measurer.appendChild(el);
            // 如果添加当前元素后超高
            if (measurer.scrollHeight > containerHeight) {
                // 从测量器中移除导致超高的元素
                measurer.removeChild(el);

                // 检查当前元素本身是否就超过页面高度
                const tempMeasurer = document.createElement('div');
                tempMeasurer.style.position = 'absolute';
                tempMeasurer.style.top = '-9999px';
                tempMeasurer.style.width = `${container.clientWidth}px`;
                tempMeasurer.className = measurer.className;
                document.body.appendChild(tempMeasurer);
                tempMeasurer.appendChild(el.cloneNode(true));

                const elementExceedsPage = tempMeasurer.scrollHeight > containerHeight;
                document.body.removeChild(tempMeasurer);

                if (elementExceedsPage) {
                    // 元素本身超高，需要按字符分页
                    // 先保存当前页面内容（如果有的话）
                    if (measurer.innerHTML.trim()) {
                        pages.push(measurer.innerHTML);
                        measurer.innerHTML = '';
                    }

                    // 按字符分页处理超长元素
                    const paginatedElements = paginateElementByCharacter(el);
                    for (const paginatedEl of paginatedElements) {
                        pages.push(paginatedEl.outerHTML);
                    }
                } else {
                    // 元素本身不超高，按原逻辑处理
                    // 此时测量器的内容就是完整的一页
                    pages.push(measurer.innerHTML);
                    // 清空测量器，并将刚导致超高的元素作为下一页的第一个元素
                    measurer.innerHTML = '';
                    measurer.appendChild(el);
                }
            }
        }

        // 3. 添加最后一页的剩余内容
        if (measurer.innerHTML) {
            pages.push(measurer.innerHTML);
        }

        return pages;

    }, [content, displayMode, chapterTitle]);

    //调用分页分算， 缓存分页结果
    useEffect(() => {
        let isMounted = true;
        let timerId: NodeJS.Timeout | null = null;
        const container = containerRef.current;

        const performPagination = async () => {
            if (!container || !isMounted) return;

            const { clientWidth, clientHeight } = container;
            const cacheKey = `${chapterId}-${displayMode}-${clientWidth}x${clientHeight}`;

            if (paginationCache.current[cacheKey]) {
                // 使用缓存结果
                const cachedPages = paginationCache.current[cacheKey];
                setPaginatedContent(cachedPages);
                setTotalPages(cachedPages.length);
                setIsPaginating(false);
                return;
            }

            // 保存当前显示的内容，避免重新分页时的空白闪现
            if (paginatedContent.length > 0 && currentPage <= paginatedContent.length) {
                setPreviousContent(paginatedContent[currentPage - 1] || '');
            }

            // 设置loading状态
            setIsPaginating(true);

            // 执行分页计算
            timerId = setTimeout(async () => {
                if (!isMounted) return;

                const pages = await paginate();

                if (pages && isMounted) {
                    setPaginatedContent(pages);
                    setTotalPages(pages.length);
                    paginationCache.current[cacheKey] = pages;
                    setPreviousContent(''); // 清空临时内容
                    setIsPaginating(false);
                }
            }, 50);
        };

        performPagination();

        const resizeObserver = new ResizeObserver(performPagination);
        if (container) {
            resizeObserver.observe(container);
        }

        return () => {
            isMounted = false;
            resizeObserver.disconnect();
            if (timerId) {
                clearTimeout(timerId);
            }
        };
    }, [chapterId, displayMode, content, currentPage, paginatedContent, paginate, setTotalPages]); // 移除isPaginating和paginatedContent依赖

    //点击屏幕左右翻页, 待优化, 中间部分需要改造为popover
    const handleContainerClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (isPaginating) return;
        const { clientWidth } = e.currentTarget;
        const clickX = e.clientX - e.currentTarget.getBoundingClientRect().left;

        if (clickX > clientWidth / 3 * 2) {
            onGoToNext();
        }
        else if (clickX < clientWidth / 3) {
            onGoToPrev();
        }
        else {
            //弹出顶部导航栏和底部组件
            onToggleBars();
        }
    };

    return (
        <div className="w-full h-full flex flex-col relative font-serif">
            <div ref={containerRef} className="flex-grow w-full p-6 leading-relaxed text-base text-justify overflow-hidden cursor-pointer select-none" onClick={handleContainerClick}>
                {isPaginating ? (
                    // 分页计算期间，如果有之前的内容就显示，否则显示加载状态
                    previousContent ?
                        <div className="w-full h-full opacity-75" dangerouslySetInnerHTML={{ __html: previousContent }} /> :
                        <div className="w-full h-full flex items-center justify-center">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4 mx-auto"></div>
                                <p className="text-foreground text-base font-medium mb-2">正在加载章节内容</p>
                            </div>
                        </div>
                ) : (
                    <div className="w-full h-full" dangerouslySetInnerHTML={{ __html: paginatedContent[currentPage - 1] || '内容为空或加载失败' }} />
                )}
            </div>
            <div ref={measurerRef} className="absolute top-0 left-0 invisible -z-10 p-6 leading-relaxed text-base text-justify" aria-hidden="true" />
        </div>
    );
}