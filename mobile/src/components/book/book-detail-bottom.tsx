"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { ChapterInfo, DisplayMode } from "@/types/book"
import { ChevronDown, ChevronRight, Book, Copy, Languages, BookOpen } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectTrigger, SelectValue, SelectContent , SelectItem} from "@/components/ui/select"

const readingModeOptions = [
  { value: "original", label: "原文", icon: BookOpen },
  { value: "translation", label: "译文", icon: Languages },
  { value: "comparison", label: "对照", icon: Copy },
]

export default function BookDetailBottom({chapters, main_chapter_level }: {chapters: any[], main_chapter_level: number}) {
  const [isTableOfContentsOpen, setIsTableOfContentsOpen] = useState(false)
  const [readingMode, setReadingMode] = useState("original")
  const [progress, setProgress] = useState(1)


  const toggleTableOfContents = () => {
    setIsTableOfContentsOpen(!isTableOfContentsOpen)
  }

  const animals = [
    { key: "cat", label: "Cat" },
    { key: "dog", label: "Dog" },
    { key: "elephant", label: "Elephant" },
    { key: "lion", label: "Lion" },
    { key: "tiger", label: "Tiger" },
    { key: "giraffe", label: "Giraffe" },
    { key: "dolphin", label: "Dolphin" },
    { key: "penguin", label: "Penguin" },
    { key: "zebra", label: "Zebra" },
    { key: "shark", label: "Shark" },
    { key: "whale", label: "Whale" },
    { key: "otter", label: "Otter" },
    { key: "crocodile", label: "Crocodile" },
  ];
  console.log(chapters)
  let groupStart = false
  return (
    <>
      <Card className="flex pt-2 pb-4 w-full overflow-hidden border-0 bg-white/95 rounded-none backdrop-blur-xl shadow-2xl">
        <CardContent className="px-4 py-2 space-y-2">
          {/* 主要控制区域 - 一行布局 */}
          <div className="flex items-center gap-6 justify-between">
            <div className="">上一章</div>
            <Progress value={progress} className="flex-1" />
            <div className="">下一章</div>
          </div>
          <div className="flex items-center gap-3 w-full">
            {/* 目录按钮 */}
            <Select>
              <SelectTrigger className="flex-shrink-0 w-32 h-10 px-3 bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 transition-all duration-200">
                <Book className="h-4 w-4 mr-2" />
                <SelectValue placeholder="目录" />
              </SelectTrigger>
              <SelectContent>
                {chapters && chapters.map((chapter) => (
                  {chapter.level === main_chapter_level ? groupStart = true : null}
                  <SelectItem key={chapter.chapter_id} value={chapter.title}>
                    {chapter.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {/* <Button
              variant="outline"
              size="sm"
              onClick={toggleTableOfContents}
              className="flex-shrink-0 h-10 px-3 bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 transition-all duration-200"
            >
              <Book className="h-4 w-4 mr-2" />
              目录
              {isTableOfContentsOpen ? (
                <ChevronDown className="h-3 w-3 ml-1" />
              ) : (
                <ChevronRight className="h-3 w-3 ml-1" />
              )}
            </Button> */}

            {/* 阅读模式选择 */}
            <div className="flex-1">
              <RadioGroup value={readingMode} onValueChange={setReadingMode} className="flex gap-1">
                {readingModeOptions.map((option) => {
                  const Icon = option.icon
                  return (
                    <div key={option.value} className="flex-1">
                      <RadioGroupItem value={option.value} id={option.value} className="sr-only" />
                      <Label
                        htmlFor={option.value}
                        className={cn(
                          "flex flex-col items-center justify-center h-8 px-2 rounded-xl cursor-pointer transition-all duration-200 text-sm font-medium",
                          readingMode === option.value
                            ? "bg-primary text-white shadow-lg shadow-primary/25"
                            : "bg-gray-50/80 text-gray-600 hover:bg-gray-100/80",
                        )}
                      >
                        {option.label}
                      </Label>
                    </div>
                  )
                })}
              </RadioGroup>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}