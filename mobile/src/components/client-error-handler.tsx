"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { UnauthorizedError } from '@/lib/api/api-client'

/**
 * 客户端全局错误处理组件
 * 避免在 Server Component 中使用复杂的内联脚本
 */
export function ClientErrorHandler() {
  const router = useRouter()

  useEffect(() => {
    // 401错误处理函数
    const handle401Error = () => {
      // 获取当前页面路径作为回调URL
      const currentPath = window.location.pathname + window.location.search
      const callbackUrl = encodeURIComponent(currentPath)
      
      // 跳转到登录页面
      router.push(`/login?callbackUrl=${callbackUrl}`)
    }

    // 全局错误处理
    const handleError = (event: ErrorEvent) => {
      // 忽略 Next.js 的重定向错误
      if (event.error && event.error.message && event.error.message.includes('NEXT_REDIRECT')) {
        return; // 不处理重定向错误
      }
      
      console.error('Global error:', event.error)
      
      // 检查是否是UnauthorizedError
      if (event.error instanceof UnauthorizedError) {
        handle401Error()
        return
      }
      
      if (event.error && event.error.message) {
        const message = event.error.message
        
        if (message.includes('Unexpected token') || message.includes('SyntaxError')) {
          console.warn('Detected syntax error, possible file corruption')
          
          // 提示用户清除缓存
          const shouldClear = window.confirm(
            'Page encountered a syntax error, which is usually caused by cache issues.\n' +
            'Would you like to clear the cache and reload the page?'
          )
          
          if (shouldClear) {
            // 清除缓存
            localStorage.clear()
            sessionStorage.clear()
            
            // 清除 Service Worker
            if ('serviceWorker' in navigator) {
              navigator.serviceWorker.getRegistrations().then(registrations => {
                registrations.forEach(registration => registration.unregister())
              })
            }
            
            // 强制刷新
            window.location.reload()
          }
        }
      }
    }

    // 未处理的 Promise 错误
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      
      // 检查Promise rejection中是否包含UnauthorizedError
      if (event.reason instanceof UnauthorizedError) {
        handle401Error()
        event.preventDefault() // 阻止默认的错误处理
        return
      }
    }

    // 资源加载错误
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement
      
      if (target && target.tagName === 'LINK') {
        const link = target as HTMLLinkElement
        console.warn('CSS resource loading failed:', link.href)
        
        if (link.href.includes('font-awesome')) {
          console.warn('Font Awesome loading failed, using local fallback')
        } else if (link.href.includes('fonts.googleapis.com')) {
          console.warn('Google Fonts loading failed, using system fonts')
        }
      }
      
      if (target && target.tagName === 'SCRIPT') {
        const script = target as HTMLScriptElement
        console.warn('JavaScript resource loading failed:', script.src)
        
        // 如果是关键脚本加载失败，提示用户
        if (script.src.includes('layout') || script.src.includes('main')) {
          console.error('Critical script failed to load, this may cause page malfunction')
        }
      }
    }

    // 添加事件监听器
    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleResourceError, true)

    // 清理函数
    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleResourceError, true)
    }
  }, [router])

  // 这个组件不渲染任何内容
  return null
} 