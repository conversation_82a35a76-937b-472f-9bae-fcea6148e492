"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

const VTabs = TabsPrimitive.Root

const VTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "flex flex-col items-center justify-start art gap-2 w-48 h-fit py-4 px-4 bg-white",
      "rounded-lg shadow-xs top-8 sticky text-foreground",
      className
    )}
    {...props}
  />
))
VTabsList.displayName = TabsPrimitive.List.displayName

const VTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex justify-start  w-28 text-left h-auto min-h-6 py-2 px-4 whitespace-normal break-words", 
      "gap-6 text-base ",
      "hover:text-primary transition-all ",
      "data-[state=active]:underline-offset-4 data-[state=active]:bg-linear-to-r from-primary/20 to-transparent",
      "data-[state=active]:border-l-4 data-[state=active]:border-primary data-[state=active]:text-primary",
      className
    )}
    {...props}
  />
))
VTabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const VTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "w-full py-1 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
VTabsContent.displayName = TabsPrimitive.Content.displayName

export { VTabs, VTabsList, VTabsTrigger, VTabsContent }
