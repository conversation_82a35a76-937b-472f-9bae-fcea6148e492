"use client"

import { useEffect, useState } from 'react'

interface SyntaxError {
  timestamp: number
  message: string
  filename?: string
  line?: number
  column?: number
  stack?: string
}

/**
 * 语法错误检测器
 * 专门用于检测和报告语法错误，帮助诊断问题
 */
export function SyntaxErrorDetector() {
  const [errors, setErrors] = useState<SyntaxError[]>([])
  const [isDev] = useState(() => process.env.NODE_ENV === 'development')

  useEffect(() => {
    // 只在开发环境下显示
    if (!isDev) return

    const handleError = (event: ErrorEvent) => {
      const error = event.error
      
      if (error && error.message) {
        const message = error.message.toLowerCase()
        
        // 检测各种类型的语法错误
        if (
          message.includes('unexpected token') ||
          message.includes('syntaxerror') ||
          message.includes('invalid or unexpected token') ||
          message.includes('unexpected end of input')
        ) {
          const syntaxError: SyntaxError = {
            timestamp: Date.now(),
            message: error.message,
            filename: event.filename,
            line: event.lineno,
            column: event.colno,
            stack: error.stack
          }
          
          setErrors(prev => [...prev.slice(-4), syntaxError]) // 只保留最近5个错误
          
          // 详细日志
          console.group('🚨 Syntax Error Detected')
          console.error('Message:', error.message)
          console.error('File:', event.filename)
          console.error('Line:', event.lineno, 'Column:', event.colno)
          console.error('Stack:', error.stack)
          console.groupEnd()
          
          // 特殊处理 layout.js 错误
          if (event.filename && event.filename.includes('layout.js')) {
            console.warn('⚠️  Layout.js syntax error detected!')
            console.warn('This might be caused by:')
            console.warn('1. Inline script encoding issues')
            console.warn('2. Hot reload conflicts')
            console.warn('3. Build cache corruption')
            console.warn('4. Unicode characters in code')
          }
        }
      }
    }

    window.addEventListener('error', handleError)
    
    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [isDev])

  // 清除错误列表
  const clearErrors = () => {
    setErrors([])
  }

  // 生产环境下不显示
  if (!isDev || errors.length === 0) {
    return null
  }

  return (
    <div className="fixed top-0 right-0 z-[9999] bg-red-500 text-white p-4 max-w-md shadow-lg border border-red-600">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">🚨 Syntax Errors Detected</h3>
        <button 
          onClick={clearErrors}
          className="text-xs bg-red-600 px-2 py-1 rounded hover:bg-red-700"
        >
          Clear
        </button>
      </div>
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {errors.map((error, index) => (
          <div key={index} className="text-xs bg-red-600 p-2 rounded">
            <div className="font-semibold mb-1">
              {new Date(error.timestamp).toLocaleTimeString()}
            </div>
            <div className="mb-1">{error.message}</div>
            {error.filename && (
              <div className="text-red-200">
                File: {error.filename}
              </div>
            )}
            {error.line && (
              <div className="text-red-200">
                Line: {error.line}, Column: {error.column}
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-3 text-xs space-y-1">
        <div>💡 <strong>Quick fixes:</strong></div>
        <div>• Clear browser cache (Ctrl+Shift+R)</div>
        <div>• Clear Next.js cache (rm -rf .next)</div>
        <div>• Restart dev server</div>
        <div>• Check console for details</div>
      </div>
    </div>
  )
} 