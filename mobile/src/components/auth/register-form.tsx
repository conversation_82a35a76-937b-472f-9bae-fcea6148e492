"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Lock, Eye, EyeOff, UserRound } from "lucide-react"
import { cn } from "@/lib/utils"
import { AuthService } from "@/lib/auth"
import { registerSchema } from "@/lib/schemas"

function SubmitButton({ isSubmitting }: { isSubmitting: boolean }) {
  return (
    <Button
      type="submit"
      disabled={isSubmitting}
      className="w-full h-12 py-3 px-4 bg-linear-to-r text-base from-primary to-primary/80 hover:translate-y-[-2px] transition-all duration-300 shadow-lg hover:shadow-primary/30"
    >
      {isSubmitting ? "注册中..." : "注 册"}
    </Button>
  )
}

export function RegisterForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [contactValue, setContactValue] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [agreeToTerms, setAgreeToTerms] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string[]>>({})
  const [message, setMessage] = useState("")
  const router = useRouter()

  // 判断是否有任何字段错误
  const hasFieldErrors = () => {
    return errors?.contact || errors?.password || errors?.confirmPassword || errors?.agreeToTerms
  }

  // 判断输入是邮箱还是手机号
  const isEmail = (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  const isPhone = (value: string) => /^1[3-9]\d{9}$/.test(value)

  const getContactType = (value: string) => {
    if (isPhone(value)) return "phone"
    if (isEmail(value)) return "email"
    return null
  }

  const getContactPlaceholder = () => {
    if (!contactValue) return "请输入手机号或邮箱"
    if (isPhone(contactValue)) return "手机号格式正确"
    if (isEmail(contactValue)) return "邮箱格式正确"
    return "请输入正确的手机号或邮箱"
  }

  const getContactError = () => {
    if (!contactValue) return null
    if (getContactType(contactValue)) return null
    return "请输入正确的手机号或邮箱"
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})
    setMessage("")

    try {
      const formData = new FormData()
      formData.append('contact', contactValue)
      formData.append('password', password)
      formData.append('confirmPassword', confirmPassword)
      formData.append('agreeToTerms', agreeToTerms.toString())

      // 验证表单数据
      const validatedFields = registerSchema.safeParse(formData);
      if (!validatedFields.success) {
        return {
          message: '提交的数据有误',
          errors: validatedFields.error.flatten().fieldErrors,
        };
      }

      const contactType = getContactType(contactValue);
      if (!contactType) {
        return {
          message: '联系方式格式错误',
          errors: {
            contact: ['请输入正确的邮箱或手机号格式']
          }
        };
      }

       await AuthService.register({
        email: contactType === 'email' ? contactValue : undefined,
        phone: contactType === 'phone' ? contactValue : undefined,
        password: password,
      })

      // 注册成功，重定向到登录页面
      router.push('/login?message=注册成功，请登录')
    } catch (error) {
      console.error('注册失败:', error)
      setMessage('注册失败，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Label htmlFor="contact" className="block text-base font-medium text-foreground mb-2">
          手机号/邮箱
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <UserRound className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="contact"
            name="contact"
            value={contactValue}
            onChange={(e) => setContactValue(e.target.value)}
            type="text"
            placeholder={getContactPlaceholder()}
            className={cn(
              "h-12 pl-10 pr-4 border-gray-300 focus:border-primary/50 focus:ring-primary/50 placeholder:text-foreground/50 placeholder:text-base",
              (getContactError() || errors?.contact) ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
            )}
          />
        </div>
        {getContactError() && (
          <p className="mt-1 text-sm text-red-500">{getContactError()}</p>
        )}
        {errors?.contact && (
          <p className="mt-1 text-sm text-red-500">{errors.contact[0]}</p>
        )}
      </div>

      <div>
        <Label htmlFor="password" className="block text-base font-medium text-foreground mb-2">
          密码
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="password"
            name="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            type={showPassword ? "text" : "password"}
            placeholder="请输入密码"
            className={cn(
              "h-12 pl-10 pr-12 border-gray-300 focus:border-primary/50 focus:ring-primary/50 placeholder:text-foreground/50 placeholder:text-base",
              errors?.password ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
            )}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        </div>
        {errors?.password && (
          <p className="mt-1 text-sm text-red-500">{errors.password[0]}</p>
        )}
      </div>

      <div>
        <Label htmlFor="confirmPassword" className="block text-base font-medium text-foreground mb-2">
          确认密码
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            type={showConfirmPassword ? "text" : "password"}
            placeholder="请再次输入密码"
            className={cn(
              "h-12 pl-10 pr-12 border-gray-300 focus:border-primary/50 focus:ring-primary/50 placeholder:text-foreground/50 placeholder:text-base",
              errors?.confirmPassword ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
            )}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          >
            {showConfirmPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        </div>
        {errors?.confirmPassword && (
          <p className="mt-1 text-sm text-red-500">{errors.confirmPassword[0]}</p>
        )}
      </div>

      {/* 协议同意 */}
      <div className="space-y-2">
        <div className="flex items-center space-x-3">
          <Checkbox
            id="agreeToTerms"
            name="agreeToTerms"
            checked={agreeToTerms}
            onCheckedChange={(checked) => setAgreeToTerms(checked === true)}
            className={cn(
              "h-4 w-4 rounded border-gray-300 text-primary",
              errors?.agreeToTerms ? "border-red-500" : ""
            )}
          />
          <Label htmlFor="agreeToTerms" className="text-sm leading-5 cursor-pointer">
            我已阅读并同意{" "}
            <a href="/terms" target="_blank" className="text-primary hover:text-primary/80 underline">
              《用户协议》
            </a>
            {" "}和{" "}
            <a href="/privacy" target="_blank" className="text-primary hover:text-primary/80 underline">
              《隐私政策》
            </a>
          </Label>
        </div>
        {errors?.agreeToTerms && (
          <p className="text-sm text-red-500">{errors.agreeToTerms[0]}</p>
        )}
      </div>

      {message && !hasFieldErrors() && (
        <div className="text-sm text-destructive text-center">{message}</div>
      )}

      <SubmitButton isSubmitting={isSubmitting} />

      <div className="mt-6 text-center text-sm">
        <span className="text-gray-600">已有账号?</span>
        <a href="/login" className="text-primary hover:text-primary/80 font-medium ml-2">
          立即登录
        </a>
      </div>
    </form>
  )
} 