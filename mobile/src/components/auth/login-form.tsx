"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Lock, Eye, EyeOff, UserRound } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAuth } from "@/hooks/use-auth"

interface LoginFormProps {
  callbackUrl?: string
}

function SubmitButton({ isLoading }: { isLoading: boolean }) {
  return (
    <Button
      type="submit"
      disabled={isLoading}
      className="w-full h-12 py-3 px-4 bg-linear-to-r text-base from-primary to-primary/80 hover:translate-y-[-2px] transition-all duration-300 shadow-lg hover:shadow-primary/30"
    >
      {isLoading ? "登录中..." : "登 录"}
    </Button>
  )
}

export function LoginForm({ callbackUrl}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [contactValue, setContactValue] = useState("")
  const [password, setPassword] = useState("")
  const [errorMessage, setErrorMessage] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, isLoading } = useAuth()

  // 获取回调URL
  const finalCallbackUrl = callbackUrl || searchParams.get('callbackUrl') || '/'
  const decodedCallbackUrl = decodeURIComponent(finalCallbackUrl)

  // 判断输入是邮箱还是手机号
  const isEmail = (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  const isPhone = (value: string) => /^1[3-9]\d{9}$/.test(value)

  const getContactType = (value: string) => {
    if (isPhone(value)) return "phone"
    if (isEmail(value)) return "email"
    return null
  }

  const getContactPlaceholder = () => {
    if (!contactValue) return "请输入手机号或邮箱"
    if (isPhone(contactValue)) return "手机号格式正确"
    if (isEmail(contactValue)) return "邮箱格式正确"
    return "请输入正确的手机号或邮箱"
  }

  const getContactError = () => {
    if (!contactValue) return null
    if (getContactType(contactValue)) return null
    return "请输入正确的手机号或邮箱"
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 前端验证
    if (!contactValue) {
      setErrorMessage("请输入用户名")
      return
    }
    if (!password) {
      setErrorMessage("请输入密码")
      return
    }

    const loginType = getContactType(contactValue)
    if (!loginType) {
      setErrorMessage("请输入正确的邮箱或手机号格式")
      return
    }

    setErrorMessage("")

    try {
      await login({
        username: contactValue,
        password: password,
        login_type: loginType,
      })

      // 登录成功，跳转到回调URL
      console.log('登录成功，即将跳转到:', decodedCallbackUrl)
      router.push(decodedCallbackUrl)
    } catch (error: any) {
      console.error('Login error:', error)
      if (error.status === 401) {
        setErrorMessage('用户名或密码错误')
      } else if (error.status === 429) {
        setErrorMessage('登录请求过于频繁，请稍后再试')
      } else {
        setErrorMessage(error.message || '登录失败，请稍后再试')
      }
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Label htmlFor="loginId" className="block text-base font-medium text-foreground mb-2">
          用户名
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <UserRound className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="loginId"
            value={contactValue}
            onChange={(e) => setContactValue(e.target.value)}
            type="text"
            placeholder={getContactPlaceholder()}
            className={cn(
              "h-12 pl-10 pr-4 border-gray-300 focus:border-primary/50 placeholder:text-foreground/50 placeholder:text-base",
              getContactError() ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""
            )}
          />
        </div>
        {getContactError() && (
          <p className="mt-1 text-sm text-red-500">{getContactError()}</p>
        )}
      </div>

      <div>
        <div className="flex justify-between mb-2">
          <Label htmlFor="password" className="block text-base font-medium text-foreground">
            密码
          </Label>
          <a href="/forgot-password" className="text-sm text-primary hover:text-primary/80">
            忘记密码?
          </a>
        </div>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            type={showPassword ? "text" : "password"}
            placeholder="请输入密码"
            className="h-12 pl-10 pr-12 border-gray-300 focus:border-primary/50 focus:ring-primary/50 placeholder:text-foreground/50 placeholder:text-base"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      {/* <div className="flex items-center">
        <label className="relative inline-flex items-center cursor-pointer">
          <input type="checkbox" className="sr-only peer" />
          <div className="w-8 h-5 bg-gray-200 rounded-full peer peer-checked:bg-primary"></div>
        </label>
        <span className="ml-3 text-sm text-gray-600">记住我</span>
      </div> */}

      {errorMessage && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {errorMessage}
        </div>
      )}

      <SubmitButton isLoading={isLoading} />

      {/* <div className="mt-6 text-center text-sm">
        <span className="text-gray-600">还没有账号?</span>
        <a href="/register" className="text-primary font-medium ml-2 hover:text-primary/80">
          立即注册
        </a>
      </div> */}
    </form>
  )
} 