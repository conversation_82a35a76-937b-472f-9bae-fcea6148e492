"use client"

import { ReactNode } from 'react'
import { useAuthGuard } from '@/hooks/use-auth'

interface AuthGuardProps {
  children: ReactNode
  fallback?: ReactNode
  redirectTo?: string
}

export function AuthGuard({ 
  children, 
  fallback, 
  redirectTo = '/login' 
}: AuthGuardProps) {
  const { shouldRender, isLoading } = useAuthGuard(redirectTo)

  // 显示加载状态
  if (isLoading) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">正在验证身份...</p>
        </div>
      </div>
    )
  }

  // 如果应该渲染（已认证），显示子组件
  if (shouldRender) {
    return <>{children}</>
  }

  // 否则不渲染任何内容（会自动重定向）
  return null
} 