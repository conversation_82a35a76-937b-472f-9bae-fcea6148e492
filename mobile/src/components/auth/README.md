# 认证保护组件使用指南

## AuthGuard

`AuthGuard` 组件用于保护需要认证的页面，在页面渲染前检查用户的认证状态和token有效期。

### 主要功能

1. **认证状态检查** - 检查用户是否已登录
2. **Token有效期检查** - 提前30秒检查token是否过期
3. **自动重定向** - 未认证或token过期时自动跳转到登录页
4. **优雅的加载状态** - 显示认证检查进度

### 使用方式

#### 方式1: 直接使用AuthGuard

```tsx
"use client"

import { AuthGuard } from '@/components/auth/AuthGuard'

export default function ProtectedPage() {
  return (
    <AuthGuard>
      <div>受保护的页面内容</div>
    </AuthGuard>
  )
}
```

#### 方式2: 使用ProtectedLayout（推荐）

```tsx
"use client"

import { ProtectedLayout } from '@/components/layout/ProtectedLayout'

export default function MyPage() {
  return (
    <ProtectedLayout>
      <div>页面内容，只有认证保护，布局由根layout.tsx处理</div>
    </ProtectedLayout>
  )
}
```

#### 方式3: 使用AuthProtected（更明确的命名）

```tsx
"use client"

import { AuthProtected } from '@/components/auth/AuthProtected'

export default function MyPage() {
  return (
    <AuthProtected>
      <div>受保护的页面内容</div>
    </AuthProtected>
  )
}
```

### 自定义加载状态

```tsx
<AuthGuard 
  fallback={
    <div className="custom-loading">
      正在验证身份...
    </div>
  }
>
  <YourComponent />
</AuthGuard>
```

### 重要说明

1. **不重复布局**: `ProtectedLayout`只提供认证保护，不包含Header和Footer
2. **根布局**: Header和Footer已经在`app/layout.tsx`中定义，无需重复
3. **客户端组件**: AuthGuard只能在客户端组件中使用（需要"use client"）
4. **提前检查**: token过期检查提前30秒进行，确保API请求时token仍然有效
5. **自动清理**: token过期时会自动清理本地存储并跳转到登录页

### 与api-client的配合

AuthGuard负责在页面级别检查认证状态，api-client只负责基本的API请求：

- **AuthGuard**: 页面级认证检查、token过期处理
- **api-client**: API请求、基本错误处理

这样的分工避免了页面渲染后再跳转的不良体验。

### 架构说明

```
app/layout.tsx (根布局，包含Header和Footer)
├── AuthGuard/ProtectedLayout (认证保护)
│   └── 页面内容
└── Footer
```

这样确保了Header和Footer不会重复，同时提供了统一的认证保护。 