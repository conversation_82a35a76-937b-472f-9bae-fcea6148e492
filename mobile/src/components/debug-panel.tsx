"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { getCacheInfo, clearAllCache, forceRefresh, setupNetworkMonitoring } from '@/lib/cache-utils'

interface DebugInfo {
  userAgent: string
  url: string
  timestamp: string
  cacheInfo: {
    hasCache: boolean
    cacheSize: number
    lastCleared?: string
  }
  networkStatus: string
  errors: Array<{
    timestamp: string
    message: string
    stack?: string
  }>
}

export default function DebugPanel() {
  const [isVisible, setIsVisible] = useState(false)
  const [safeAreaValues, setSafeAreaValues] = useState({
    top: '0px',
    bottom: '0px',
    left: '0px',
    right: '0px'
  })
  const [actualBottomNavPadding, setActualBottomNavPadding] = useState('未检测')
  const [deviceInfo, setDeviceInfo] = useState({
    userAgent: '',
    innerHeight: 0,
    innerWidth: 0,
    devicePixelRatio: 0,
    isIOS: false,
    isSafari: false
  })
  const [safeAreaBottom, setSafeAreaBottom] = useState(0)
  const [safeAreaTop, setSafeAreaTop] = useState(0)
  const [viewportHeight, setViewportHeight] = useState(0)
  const [innerHeight, setInnerHeight] = useState(0)
  const [deviceInfoString, setDeviceInfoString] = useState('')
  const [bottomNavHeight, setBottomNavHeight] = useState(0)
  const [bottomNavPadding, setBottomNavPadding] = useState(0)
  const [hasKeyboard, setHasKeyboard] = useState(false)
  const [inputFocused, setInputFocused] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)

  useEffect(() => {
    // 检测安全区域值
    const updateSafeAreaValues = () => {
      // 创建临时元素来检测CSS env()值
      const testElement = document.createElement('div')
      testElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 1px;
        height: 1px;
        pointer-events: none;
        visibility: hidden;
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
      `
      document.body.appendChild(testElement)
      
      const computedStyle = window.getComputedStyle(testElement)
      setSafeAreaValues({
        top: computedStyle.paddingTop,
        bottom: computedStyle.paddingBottom,
        left: computedStyle.paddingLeft,
        right: computedStyle.paddingRight
      })
      
      document.body.removeChild(testElement)
    }

    // 检测设备信息
    const updateDeviceInfo = () => {
      const ua = navigator.userAgent
      setDeviceInfo({
        userAgent: ua,
        innerHeight: window.innerHeight,
        innerWidth: window.innerWidth,
        devicePixelRatio: window.devicePixelRatio,
        isIOS: /iPad|iPhone|iPod/.test(ua),
        isSafari: /Safari/.test(ua) && !/Chrome/.test(ua)
      })
    }

    const updateSafeArea = () => {
      // 获取CSS变量值
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)
      
      // 尝试获取safe-area-inset值
      const tempDiv = document.createElement('div')
      tempDiv.style.cssText = `
        position: fixed; 
        top: 0; 
        left: 0; 
        padding-top: env(safe-area-inset-top, 0px);
        padding-bottom: env(safe-area-inset-bottom, 0px);
        visibility: hidden;
        pointer-events: none;
      `
      document.body.appendChild(tempDiv)
      
      const topPadding = parseFloat(getComputedStyle(tempDiv).paddingTop) || 0
      const bottomPadding = parseFloat(getComputedStyle(tempDiv).paddingBottom) || 0
      
      setSafeAreaTop(topPadding)
      setSafeAreaBottom(bottomPadding)
      
      document.body.removeChild(tempDiv)
      
      // 获取视口信息
      setViewportHeight(window.visualViewport?.height || window.innerHeight)
      setInnerHeight(window.innerHeight)
      
      // 检测虚拟键盘
      const keyboardVisible = window.visualViewport ? 
        window.visualViewport.height < window.innerHeight * 0.75 : false
      setHasKeyboard(keyboardVisible)
      
      // 检测缩放
      const zoom = window.visualViewport ? 
        window.innerWidth / window.visualViewport.width : 1
      setZoomLevel(zoom)
      
      // 获取设备信息
      setDeviceInfoString(`${screen.width}×${screen.height}, UA: ${navigator.userAgent.includes('iPhone') ? 'iPhone' : 'Other'}`)
      
      // 获取底部导航栏信息
      const bottomNav = document.querySelector('.mobile-bottom-nav') as HTMLElement
      if (bottomNav) {
        const styles = getComputedStyle(bottomNav)
        setBottomNavHeight(parseFloat(styles.height) || 0)
        setBottomNavPadding(parseFloat(styles.paddingBottom) || 0)
      }
    }

    // 监听输入框焦点
    const handleFocusIn = (e: FocusEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        setInputFocused(true)
      }
    }

    const handleFocusOut = (e: FocusEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        setInputFocused(false)
      }
    }

         updateSafeAreaValues()
     updateDeviceInfo()
     updateSafeArea()

     // 检测底部导航栏实际间距和高度
     const updateBottomNavPadding = () => {
       // 增加延迟确保DOM完全渲染
       setTimeout(() => {
         const bottomNavElement = document.querySelector('.mobile-bottom-nav')
         if (bottomNavElement) {
           const computedStyle = window.getComputedStyle(bottomNavElement)
           const height = computedStyle.height
           const paddingBottom = computedStyle.paddingBottom
           setActualBottomNavPadding(`height:${height}, padding:${paddingBottom}`)
           console.log('底部导航栏元素:', bottomNavElement)
           console.log('高度:', height)
           console.log('底部内边距:', paddingBottom)
           console.log('总占用高度:', parseFloat(height) + parseFloat(paddingBottom) + 'px')
         } else {
           setActualBottomNavPadding('元素未找到')
           console.log('未找到 .mobile-bottom-nav 元素')
         }
       }, 100)
     }

     updateBottomNavPadding()

     // 监听窗口变化
     const handleResize = () => {
       updateSafeAreaValues()
       updateDeviceInfo()
       updateBottomNavPadding()
       updateSafeArea()
     }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    document.addEventListener('focusin', handleFocusIn)
    document.addEventListener('focusout', handleFocusOut)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
      document.removeEventListener('focusin', handleFocusIn)
      document.removeEventListener('focusout', handleFocusOut)
    }
  }, [])

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 z-50 bg-red-500 hover:bg-red-600"
        size="sm"
      >
        调试
      </Button>
    )
  }

  return (
    <Card className="fixed top-4 left-4 right-4 z-50 max-h-[80vh] overflow-auto bg-white/95 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex justify-between items-center">
          环境调试信息
          <Button
            onClick={() => setIsVisible(false)}
            variant="ghost"
            size="sm"
            className="text-red-500"
          >
            ✕
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="text-xs space-y-3">
                 {/* 安全区域信息 */}
         <div>
           <h4 className="font-medium text-primary mb-1">Safe Area Insets:</h4>
           <div className="grid grid-cols-2 gap-1 text-xs bg-gray-50 p-2 rounded">
             <div>Top: <span className="font-mono text-green-600">{safeAreaValues.top}</span></div>
             <div>Bottom: <span className="font-mono text-green-600">{safeAreaValues.bottom}</span></div>
             <div>Left: <span className="font-mono text-green-600">{safeAreaValues.left}</span></div>
             <div>Right: <span className="font-mono text-green-600">{safeAreaValues.right}</span></div>
           </div>
         </div>

         {/* 实际底部间距 */}
         <div>
           <h4 className="font-medium text-primary mb-1">导航栏布局检测:</h4>
           <div className="bg-green-50 p-2 rounded text-xs">
             <div>尺寸信息: <span className="font-mono text-blue-600 font-bold">{actualBottomNavPadding}</span></div>
             <div className="text-gray-600 mt-1">
               {actualBottomNavPadding.includes('height:86px') && actualBottomNavPadding.includes('padding:34px') && 
                 '✅ 完美！基础层生效，86px总高度 + 34px安全间距'}
               {actualBottomNavPadding.includes('height:52px') && actualBottomNavPadding.includes('padding:34px') && 
                 '⚠️ 部分生效：内边距正确但总高度不足'}
               {actualBottomNavPadding.includes('height:52px') && actualBottomNavPadding.includes('padding:0px') && 
                 '❌ CSS未生效：回退到原始高度，无安全间距'}
               {actualBottomNavPadding === '元素未找到' && '❌ 无法找到导航栏元素'}
               {!actualBottomNavPadding.includes('height:') && actualBottomNavPadding !== '元素未找到' && 
                 '🔄 正在检测中...'}
             </div>
           </div>
         </div>

        {/* 设备信息 */}
        <div>
          <h4 className="font-medium text-primary mb-1">设备信息:</h4>
          <div className="bg-gray-50 p-2 rounded space-y-1">
            <div>尺寸: {deviceInfo.innerWidth}×{deviceInfo.innerHeight}</div>
            <div>像素比: {deviceInfo.devicePixelRatio}</div>
            <div>iOS: <span className={deviceInfo.isIOS ? 'text-green-600' : 'text-red-500'}>
              {deviceInfo.isIOS ? '是' : '否'}
            </span></div>
            <div>Safari: <span className={deviceInfo.isSafari ? 'text-green-600' : 'text-red-500'}>
              {deviceInfo.isSafari ? '是' : '否'}
            </span></div>
          </div>
        </div>

                 {/* 检测结果 */}
         <div>
           <h4 className="font-medium text-primary mb-1">CSS策略状态:</h4>
           <div className="bg-blue-50 p-2 rounded text-xs">
             <div className="text-blue-600 mb-2">
               ✅ <strong>兼容性优先布局策略</strong>
             </div>
             <div className="text-gray-600">
               • 基础层: 固定86px高度 + 34px安全间距<br/>
               • 增强层: @supports + 全屏模式检测<br/>
               • env() = {safeAreaValues.bottom}<br/>
               • 设备尺寸: {deviceInfo.innerWidth}×{deviceInfo.innerHeight}
             </div>
             <div className="text-green-600 mt-2">
               🎯 <strong>当前环境使用基础层</strong><br/>
               总高度: 86px, 内容区: 52px, 安全间距: 34px
             </div>
           </div>
         </div>

        {/* 使用说明 */}
        <div>
          <h4 className="font-medium text-primary mb-1">使用说明:</h4>
          <div className="text-xs text-gray-600 bg-yellow-50 p-2 rounded">
            env(safe-area-inset-bottom) 仅在以下环境生效：<br/>
            1. iOS Safari 浏览器<br/>
            2. iPhone X 及以后的全面屏设备<br/>
            3. 网页处于全屏或PWA模式<br/>
            在其他环境下始终为0px
          </div>
        </div>

        {/* 用户代理字符串 */}
        <details>
          <summary className="font-medium text-primary cursor-pointer">User Agent</summary>
          <div className="mt-1 p-2 bg-gray-50 rounded text-xs font-mono break-all">
            {deviceInfo.userAgent}
          </div>
        </details>

        {/* 调试面板新内容 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="font-medium text-primary mb-1">调试面板新内容:</h4>
          <div className="bg-gray-50 p-2 rounded text-xs space-y-1">
            <div>Safe Area Top: {safeAreaTop}px</div>
            <div>Safe Area Bottom: {safeAreaBottom}px</div>
            <div>底部导航高度: {bottomNavHeight}px</div>
            <div>底部导航内边距: {bottomNavPadding}px</div>
            <div>视口高度: {viewportHeight}px</div>
            <div>窗口高度: {innerHeight}px</div>
            <div>缩放级别: {zoomLevel.toFixed(2)}</div>
            <div className={`${hasKeyboard ? 'text-red-400' : 'text-green-400'}`}>
              虚拟键盘: {hasKeyboard ? '显示' : '隐藏'}
            </div>
            <div className={`${inputFocused ? 'text-yellow-400' : 'text-gray-400'}`}>
              输入框焦点: {inputFocused ? '有' : '无'}
            </div>
            <div className="text-gray-300 text-xs mt-1 break-all">
              设备: {deviceInfoString}
            </div>
            
            {/* 状态指示器 */}
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="flex gap-2 text-xs">
                <span className={`px-1 rounded ${zoomLevel > 1.1 ? 'bg-red-600' : 'bg-green-600'}`}>
                  缩放{zoomLevel > 1.1 ? '异常' : '正常'}
                </span>
                <span className={`px-1 rounded ${hasKeyboard && !inputFocused ? 'bg-yellow-600' : 'bg-blue-600'}`}>
                  键盘状态
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 