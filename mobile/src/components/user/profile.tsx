"use client"

import { User } from "@/types/user"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/use-auth"

export function UserProfile() {
  const { user } = useAuth()

  if (!user) {
    return null
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex flex-col space-y-8">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">个人中心</h2>
          <p className="text-muted-foreground">
            管理您的账号信息和会员状态
          </p>
        </div>
        <div className="grid gap-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>您的账号基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户名</label>
                  <p className="text-base">{user.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">邮箱</label>
                  <p className="text-base">{user.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">手机号码</label>
                  <p className="text-base">{user.phone || "未绑定"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 会员信息 */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <CardTitle>会员信息</CardTitle>
                {user.role && (
                  <Badge variant="default" className="bg-primary">
                    {user.role === "premium" ? "会员" : " 非会员"}
                  </Badge>
                )}
              </div>
              <CardDescription>您的会员状态和权益信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {(user.role && user.role == "premium") ? (
                <>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">会员到期时间</label>
                    <p className="text-base">
                      {user.expiresAt ? new Date(user.expiresAt).toLocaleDateString() : "未开通会员"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">会员权益</label>
                    <ul className="list-disc list-inside text-base space-y-1 mt-1">
                      <li>无限次在线测算</li>
                      <li>高级课程学习权限</li>
                      <li>案例解析权限</li>
                      <li>专家一对一咨询</li>
                    </ul>
                  </div>
                </>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">您还不是会员</p>
                  <button className="mt-4 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                    开通会员
                  </button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 