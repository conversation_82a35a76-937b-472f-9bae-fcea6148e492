"use client"

import { authAPI } from './api/api-client'
import { User } from '@/types/user'

// 认证状态接口
export interface AuthState {
    user: User | null
    token: string | null
    isAuthenticated: boolean
    isLoading: boolean
}

// Token管理 - 包含所有token相关功能
export class TokenManager {
    static readonly TOKEN_KEY = 'auth_token'
    static readonly USER_KEY = 'auth_user'
    static readonly EXPIRY_KEY = 'auth_expiry'

    // 刷新状态管理
    private static refreshPromise: Promise<string | null> | null = null

    // 基础存储方法
    static getToken(): string | null {
        if (typeof window === 'undefined') return null
        return localStorage.getItem(this.TOKEN_KEY)
    }

    static setToken(token: string, expiresInSeconds?: number): void {
        if (typeof window === 'undefined') return

        localStorage.setItem(this.TOKEN_KEY, token)

        if (expiresInSeconds) {
            const expiryTime = Date.now() + (expiresInSeconds * 1000)
            localStorage.setItem(this.EXPIRY_KEY, expiryTime.toString())
        }
    }

    static getUser(): User | null {
        if (typeof window === 'undefined') return null

        const userStr = localStorage.getItem(this.USER_KEY)
        if (!userStr) return null

        try {
            return JSON.parse(userStr)
        } catch {
            return null
        }
    }

    static setUser(user: User): void {
        console.log("setUser with window:", window)
        if (typeof window === 'undefined') return
        console.log("设置用户:", user)
        localStorage.setItem(this.USER_KEY, JSON.stringify(user))
    }

    static isTokenExpired(): boolean {
        if (typeof window === 'undefined') return true

        const expiryStr = localStorage.getItem(this.EXPIRY_KEY)
        if (!expiryStr) return false // 如果没有过期时间，认为不过期

        const expiryTime = parseInt(expiryStr)
        return Date.now() >= expiryTime
    }

    static clearAuth(): void {
        if (typeof window === 'undefined') return

        localStorage.removeItem(this.TOKEN_KEY)
        localStorage.removeItem(this.USER_KEY)
        localStorage.removeItem(this.EXPIRY_KEY)
    }

    // 应用启动时检查并刷新token
    static async checkAndRefreshOnStartup() {
        if (typeof window === 'undefined') return
        await this.refreshToken()
    }

    // 刷新token（防止并发）
    static async refreshToken(): Promise<string | null> {
        if (this.refreshPromise) {
            console.log('Token刷新已在进行中，等待完成...')
            return this.refreshPromise
        }

        this.refreshPromise = this.performTokenRefresh()

        try {
            const result = await this.refreshPromise
            return result
        } finally {
            this.refreshPromise = null
        }
    }

    // 执行实际的token刷新
    private static async performTokenRefresh(): Promise<string | null> {
        const currentToken = this.getToken()
        if (!currentToken) return null


        try {
            if (!this.isTokenExpired()) {
                // 如果token未过期，则刷新
                console.log('开始刷新token...')
                //console.log("currentToken:", currentToken)
                const response = await authAPI.refreshToken(currentToken)
                // 解析新token的过期时间
                const tokenPayload = AuthService.parseJWT(response.access_token)
                const expiresIn = tokenPayload?.exp ? tokenPayload.exp - Math.floor(Date.now() / 1000) : undefined
                // 保存新token
                this.setToken(response.access_token, expiresIn)
                console.log('Token刷新成功')
                // 触发认证状态更新
                if (typeof window !== 'undefined' && (window as any).authStateListeners) {
                    const newState = AuthService.getAuthState()
                        ; (window as any).authStateListeners.forEach((listener: any) => listener(newState))
                }

                return response.access_token
            } else {
                // 如果token已过期，则清除认证状态
                this.clearAuth()
                if (typeof window !== 'undefined' && (window as any).authStateListeners) {
                    const newState = AuthService.getAuthState()
                        ; (window as any).authStateListeners.forEach((listener: any) => listener(newState))
                }

                console.log('Token状态已过期')
                return null
            }

        } catch (error) {
            console.error('Token刷新失败:', error)
            return null
        }
    }

    // 检查是否正在刷新
    static isRefreshing(): boolean {
        return this.refreshPromise !== null
    }
}

// 认证服务
export class AuthService {
    // 初始化状态控制
    private static initPromise: Promise<void> | null = null
    private static isInitialized = false

    // 登录
    static async login(credentials: {
        username: string
        password: string
        login_type: string
    }): Promise<{ user: User; token: string }> {
        try {
            const response = await authAPI.login(credentials)

            // 解析JWT获取过期时间
            const tokenPayload = this.parseJWT(response.access_token)
            const expiresIn = tokenPayload?.exp ? tokenPayload.exp - Math.floor(Date.now() / 1000) : undefined
            console.log("登录成功，token:", response.access_token, "user:", tokenPayload.sub, "expiresIn:", expiresIn)

            // 保存认证信息
            TokenManager.setToken(response.access_token, expiresIn)
            TokenManager.setUser(tokenPayload.sub)

            return {
                user: tokenPayload.sub,
                token: response.access_token
            }
        } catch (error) {
            TokenManager.clearAuth()
            throw error
        }
    }

    // 注册
    static async register(data: { email?: string; phone?: string; password: string }) {
        return authAPI.register(data)
    }

    // 登出
    static async logout(): Promise<void> {
        const token = TokenManager.getToken()
        // 当前登录只存在本地token，所以暂时只需要清除本地缓存
        TokenManager.clearAuth()
    }

    // 刷新Token - 委托给TokenManager
    static async refreshToken(): Promise<string | null> {
        return TokenManager.refreshToken()
    }

    // 获取当前认证状态
    static getAuthState(): AuthState {
        const token = TokenManager.getToken()
        const user = TokenManager.getUser()
        const isExpired = TokenManager.isTokenExpired()

        return {
            user: isExpired ? null : user,
            token: isExpired ? null : token,
            isAuthenticated: !!(token && user && !isExpired),
            isLoading: false
        }
    }

    // 初始化认证系统 - 确保只执行一次
    static async init(): Promise<void> {
        if (typeof window === 'undefined') return

        // 如果已经初始化，直接返回
        if (this.isInitialized) {
            console.log('认证系统已初始化，跳过')
            return
        }

        // 如果正在初始化，等待完成
        if (this.initPromise) {
            console.log('认证系统正在初始化，等待完成...')
            return this.initPromise
        }

        // 开始初始化
        this.initPromise = TokenManager.checkAndRefreshOnStartup()

        try {
            await this.initPromise
            this.isInitialized = true
            console.log('认证系统初始化完成')
        } finally {
            this.initPromise = null
        }
    }

    // 重置初始化状态（用于测试或特殊场景）
    static resetInitialization(): void {
        this.isInitialized = false
        this.initPromise = null
        console.log('认证系统初始化状态已重置')
    }

    // 检查是否已初始化
    static getInitializationStatus(): boolean {
        return this.isInitialized
    }

    // 解析JWT
    static parseJWT(token: string): any {
        try {
            const base64Url = token.split('.')[1]
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            )
            return JSON.parse(jsonPayload)
        } catch {
            return null
        }
    }
} 