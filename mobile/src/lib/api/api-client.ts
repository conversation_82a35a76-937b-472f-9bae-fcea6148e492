"use client"
import { logError } from '../error-handler';

// 增强的API错误类
export class ApiError extends Error {
    constructor(
        public status: number,
        message: string,
        public data?: any
    ) {
        super(message);
        this.name = 'ApiError';
    }
}

// 特殊的401错误类，用于触发全局处理
export class UnauthorizedError extends ApiError {
    constructor(message: string = '登录已过期，请重新登录', data?: any) {
        super(401, message, data);
        this.name = 'UnauthorizedError';
    }
}

// API客户端配置
const API_BASE_URL = process.env.INTERNAL_API_URL || 'http://localhost:8000/api';

// 获取本地存储的认证token
function getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
}

// 清除认证信息（不进行跳转）
function clearAuthData() {
    if (typeof window !== 'undefined') {
        // 清除本地存储的认证信息
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('auth_expiry');
        localStorage.removeItem('auth_user');
    }
}

// 通用请求函数 - 增强版，支持自动刷新和重试
async function request<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string | boolean
): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const context = `API请求: ${options.method || 'GET'} ${endpoint}`;
    
    const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(options.headers as Record<string, string> || {}),
    };

    // 智能token处理：支持传递token或自动获取
    let authToken: string | null = null;
    if (token === true) {
        // 自动获取token
        authToken = getAuthToken();
    } else if (typeof token === 'string') {
        // 使用传递的token
        authToken = token;
    }

    if (authToken) {
        headers.Authorization = `Bearer ${authToken}`;
    }

    try {
        const response = await fetch(url, {
            ...options,
            headers,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const message = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
            
            // 处理401未授权错误
            if (response.status === 401) {
                console.log('收到401错误，清除认证信息...');
                clearAuthData();
                // 抛出特殊的UnauthorizedError，让上层组件处理跳转
                throw new UnauthorizedError(message, errorData);
            }
            
            throw new ApiError(response.status, message, errorData);
        }

        // 处理不同类型的响应
        const contentType = response.headers.get('content-type');
        let responseData: any;
        
        if (contentType && contentType.includes('application/json')) {
            responseData = await response.json();
        } else {
            responseData = await response.text();
        }
        return responseData;
    } catch (error) {
        // 如果是ApiError，直接抛出
        if (error instanceof ApiError) {
            throw error;
        }
        
        // 使用统一错误处理进行日志记录
        logError(error, context);
        throw error;
    }
}

// HTTP方法函数 - 支持灵活的token传递
export async function get<T>(
    endpoint: string, 
    params?: Record<string, string>,
    token?: string | boolean
): Promise<T> {
    const searchParams = params ? `?${new URLSearchParams(params)}` : '';
    return request<T>(`${endpoint}${searchParams}`, { method: 'GET' }, token);
}

export async function post<T>(
    endpoint: string,
    data?: any,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
    }, token);
}

export async function put<T>(
    endpoint: string,
    data?: any,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
    }, token);
}

export async function del<T>(
    endpoint: string,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, { method: 'DELETE' }, token);
}

// SWR fetcher函数 - 支持认证和无认证
export const createFetcher = (token?: string | boolean) => (url: string) => get(url, undefined, token);

// ========================= API分组定义 =========================

// 认证相关API
export const authAPI = {
    // 登录
    async login(credentials: { username: string; password: string; login_type: string }) {
        return post<{
            access_token: string
            user: {
                id: string
                name: string
                email?: string
                phone?: string
                role?: string
                expiresAt?: number
            }
        }>('/auth/login/json', credentials);
    },

    // 注册  
    async register(data: { email?: string; phone?: string; password: string }) {
        return post<{ message: string }>('/auth/register', data);
    },

    // 刷新Token
    async refreshToken(token: string) {
        return post<{ access_token: string }>('/auth/refresh', {}, token);
    },

    // 登出
    async logout(token: string) {
        return post('/auth/logout', {}, token);
    }
};

// 书籍相关API - 统一命名
export const booksAPI = {
    // 获取书籍列表
    async getBooks(params: {
        page?: string
        size?: string
        is_completed?: string
        category?: string | null
    } = {}) {
        const requestParams = {
            page: params.page || '1',
            size: params.size || '10',
            is_completed: params.is_completed || 'true',
            ...(params.category && { category: params.category }),
        };
        return get<{
            items: any[]
            total: number
            page: number
            page_size: number
            total_pages: number
        }>('/book/list', requestParams);
    },

    // 获取书籍详情
    async getBookDetail(book_id: string | null, token?: string | boolean) {
        if (!book_id) {
            return null;
        }
        return get<{
            chapter_list: any[]
            content?: any
        }>(`/book/${book_id}`, undefined, token || true);
    },

    // 获取章节内容
    async getChapterContent(chapter_id: string | null, token?: string | boolean) {
        if (!chapter_id) {
            return null;
        }
        return get<{
            chapters: any
        }>(`/book/chapter/${chapter_id}`, undefined, token || true);
    }
};
