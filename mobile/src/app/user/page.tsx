"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { AuthProtected } from '@/components/auth/AuthProtected'
import { TokenManager, AuthService } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { booksAPI } from '@/lib/api/api-client'

export default function UserPage() {
  const { user, token, isAuthenticated, logout } = useAuth()
  const [tokenInfo, setTokenInfo] = useState<any>(null)
  const [testResult, setTestResult] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [initStatus, setInitStatus] = useState(false)

  // 解析当前token信息
  useEffect(() => {
    if (token) {
      const parsed = AuthService.parseJWT(token)
      setTokenInfo(parsed)
    } else {
      setTokenInfo(null)
    }
  }, [token])

  // 监听初始化状态
  useEffect(() => {
    const checkInitStatus = () => {
      setInitStatus(AuthService.getInitializationStatus())
    }
    
    checkInitStatus()
    
    // 定期检查初始化状态
    const timer = setInterval(checkInitStatus, 1000)
    
    return () => clearInterval(timer)
  }, [])

  // 测试API请求（验证401错误触发刷新）
  const testApiCall = async () => {
    setLoading(true)
    setTestResult('')
    
    try {
      const result = await booksAPI.getBooks({ page: '1', size: '5' })
      setTestResult(`✅ API调用成功: 获取到 ${result.items.length} 本书籍`)
    } catch (error: any) {
      setTestResult(`❌ API调用失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  // 手动触发token刷新
  const manualRefresh = async () => {
    setLoading(true)
    setTestResult('')
    
    try {
      const result = await AuthService.refreshToken()
      if (result) {
        setTestResult('✅ Token刷新成功')
      } else {
        setTestResult('❌ Token刷新失败')
      }
    } catch (error: any) {
      setTestResult(`❌ Token刷新失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  // 模拟token即将过期（将过期时间设置为5分钟后）
  const simulateNearExpiry = () => {
    if (typeof window !== 'undefined') {
      const fiveMinutesLater = Date.now() + (5 * 60 * 1000)
      localStorage.setItem('auth_expiry', fiveMinutesLater.toString())
      setTestResult('⚠️ 已模拟token将在5分钟后过期，下次启动时会自动刷新')
    }
  }

  // 模拟启动时刷新检查
  const simulateStartupCheck = async () => {
    setLoading(true)
    setTestResult('')
    
    try {
      // 直接调用启动检查方法
      await TokenManager.checkAndRefreshOnStartup()
      setTestResult('✅ 启动时检查完成，请查看控制台日志')
    } catch (error: any) {
      setTestResult(`❌ 启动检查失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  // 重新初始化认证系统（测试用）
  const reinitializeAuth = async () => {
    setLoading(true)
    setTestResult('')
    
    try {
      // 重置初始化状态
      AuthService.resetInitialization()
      setTestResult('🔄 已重置初始化状态，系统将重新初始化...')
      
      // 重新初始化
      setTimeout(async () => {
        try {
          await AuthService.init()
          setTestResult('✅ 认证系统重新初始化完成')
        } catch (error: any) {
          setTestResult(`❌ 重新初始化失败: ${error.message}`)
        } finally {
          setLoading(false)
        }
      }, 1000)
    } catch (error: any) {
      setTestResult(`❌ 重置失败: ${error.message}`)
      setLoading(false)
    }
  }

  // 获取token剩余时间
  const getTokenTimeLeft = () => {
    if (!tokenInfo?.exp) return '未知'
    
    const expiry = tokenInfo.exp * 1000 // JWT exp是秒，转换为毫秒
    const now = Date.now()
    const timeLeft = expiry - now
    
    if (timeLeft <= 0) return '已过期'
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60))
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
    
    return `${hours}小时${minutes}分钟`
  }

  return (
    <AuthProtected>
      <div className="flex flex-col mx-auto p-4 space-y-6 max-w-2xl h-full overflow-y-auto">
        <div className="text-center">
          <h1 className="text-2xl font-bold">用户中心</h1>
          <p className="text-muted-foreground">Token自动刷新测试页面</p>
        </div>

        {/* 用户信息 */}
        <Card>
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>用户名:</span>
              <span>{typeof user === 'string' ? user : user?.name || '未知'}</span>
            </div>
            <div className="flex justify-between">
              <span>邮箱:</span>
              <span>{user?.email || '未设置'}</span>
            </div>
            <div className="flex justify-between">
              <span>认证状态:</span>
              <Badge variant={isAuthenticated ? "default" : "destructive"}>
                {isAuthenticated ? "已认证" : "未认证"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* 系统状态信息 */}
        <Card>
          <CardHeader>
            <CardTitle>系统状态</CardTitle>
            <CardDescription>认证系统的初始化和运行状态</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>初始化状态:</span>
              <Badge variant={initStatus ? "default" : "secondary"}>
                {initStatus ? "已初始化" : "未初始化"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Token刷新状态:</span>
              <Badge variant={TokenManager.isRefreshing() ? "secondary" : "outline"}>
                {TokenManager.isRefreshing() ? "刷新中" : "空闲"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Token信息 */}
        <Card>
          <CardHeader>
            <CardTitle>Token信息</CardTitle>
            <CardDescription>当前访问令牌的详细信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Token状态:</span>
              <Badge variant={token ? "default" : "destructive"}>
                {token ? "有效" : "无效"}
              </Badge>
            </div>
            {tokenInfo && (
              <>
                <div className="flex justify-between">
                  <span>过期时间:</span>
                  <span>{new Date(tokenInfo.exp * 1000).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>剩余时间:</span>
                  <span>{getTokenTimeLeft()}</span>
                </div>
                <div className="flex justify-between">
                  <span>用户ID:</span>
                  <span>{tokenInfo.sub?.id || tokenInfo.user_id || '未知'}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* 测试功能 */}
        <Card>
          <CardHeader>
            <CardTitle>Token刷新测试</CardTitle>
            <CardDescription>测试移动端优化的刷新机制</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-2">
              <Button 
                onClick={testApiCall} 
                disabled={loading}
                variant="outline"
              >
                {loading ? '测试中...' : '测试API调用'}
              </Button>
              
              <Button 
                onClick={manualRefresh} 
                disabled={loading}
                variant="outline"
              >
                {loading ? '刷新中...' : '手动刷新Token'}
              </Button>
              
              <Button 
                onClick={simulateStartupCheck} 
                disabled={loading}
                variant="outline"
              >
                {loading ? '检查中...' : '模拟启动检查'}
              </Button>
              
              <Button 
                onClick={simulateNearExpiry} 
                disabled={loading}
                variant="outline"
              >
                模拟Token即将过期
              </Button>
              
              <Button 
                onClick={reinitializeAuth} 
                disabled={loading}
                variant="outline"
              >
                {loading ? '重置中...' : '重新初始化认证系统'}
              </Button>
              
              <Button 
                onClick={logout} 
                variant="destructive"
              >
                登出
              </Button>
            </div>
            
            {testResult && (
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm">{testResult}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 单例初始化说明 */}
        <Card>
          <CardHeader>
            <CardTitle>单例初始化机制</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-2">
            <p>• <strong>全局唯一</strong>: AuthService.init() 只会执行一次</p>
            <p>• <strong>并发安全</strong>: 多个 useAuth 调用不会触发重复初始化</p>
            <p>• <strong>状态检查</strong>: 通过 getInitializationStatus() 检查是否已初始化</p>
            <p>• <strong>Promise控制</strong>: 使用Promise确保并发调用等待同一个初始化过程</p>
          </CardContent>
        </Card>

        {/* 移动端刷新机制说明 */}
        <Card>
          <CardHeader>
            <CardTitle>移动端刷新机制说明</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-2">
            <p>• <strong>启动时检查</strong>: 应用启动时检查token状态，如需要则自动刷新</p>
            <p>• <strong>智能判断</strong>: 只有在token过期或5分钟内即将过期时才刷新</p>
            <p>• <strong>401拦截</strong>: API请求收到401错误时自动刷新token并重试</p>
            <p>• <strong>并发控制</strong>: 避免多个请求同时触发刷新</p>
            <p>• <strong>请求队列</strong>: 刷新进行中的请求会排队等待</p>
            <p>• <strong>省电优化</strong>: 移除定时检查，减少电池消耗</p>
          </CardContent>
        </Card>

        {/* 移动端优势说明 */}
        <Card>
          <CardHeader>
            <CardTitle>移动端优化优势</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-2">
            <p>✅ <strong>省电</strong>: 无定时器运行，减少电池消耗</p>
            <p>✅ <strong>省流量</strong>: 避免不必要的网络请求</p>
            <p>✅ <strong>高效</strong>: 只在真正需要时才刷新</p>
            <p>✅ <strong>可靠</strong>: 401错误兜底机制确保可用性</p>
            <p>✅ <strong>单例</strong>: 避免重复初始化，提升性能</p>
          </CardContent>
        </Card>
      </div>
    </AuthProtected>
  )
} 