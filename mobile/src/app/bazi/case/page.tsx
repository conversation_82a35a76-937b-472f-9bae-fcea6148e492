"use client"

import dynamic from 'next/dynamic'

// 动态导入以避免SSR问题
const TabContent = dynamic(() => import('@/components/layout/mobile/tab-content'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64">
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
        <span className="text-gray-600">加载中...</span>
      </div>
    </div>
  )
})

export default function BaziPage() {
  return <TabContent category="bazi" defaultTab="cases" />
} 