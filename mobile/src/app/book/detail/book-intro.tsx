// 添加书籍简介组件
export default function BookIntroduction({ 
    title, 
    author = "佚名", 
    description = "暂无简介" 
}: { 
    title: string, 
    author: string | null, 
    description: string | null 
}) {
    return (
        <div className="flex flex-col flex-1 gap-4 items-start">
            <div className="flex flex-col space-y-4 w-full border-b border-stone-200 pb-6 mb-3">
                <h1 className="text-3xl font-serif text-primary">{title}</h1>
                <p className="text-base text-muted-foreground/80 mt-6">[作者] {author} 著</p>
            </div>
            <div className="flex prose prose-stone dark:prose-invert text-foreground/80">
                <p className="text-foreground/80 leading-relaxed">简介: {description}</p>
            </div>
        </div>
    )
}
