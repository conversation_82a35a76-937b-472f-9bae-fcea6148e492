"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { ReturnButton } from "@/components/ui/return"
import { TabItem } from "@/components/ui/tab-item"
import { cn } from "@/lib/utils"
import { BookContent } from "@/app/book/detail/book-content"
import { BookInfo, ChapterContent, ChapterInfo, DisplayMode } from "@/types/book"
import { useBookDetail, useChapterContent } from "@/hooks/use-api"
import MobileNav from "@/components/layout/mobile/nav"
import { useRouter } from 'next/navigation'
import BookDetailBottom from "@/components/book/book-detail-bottom"

export default function BookDetailClient({
    book_id,
}: {
    book_id: string
}) {
    const searchParams = useSearchParams()
    const returnCategory = searchParams.get("category") || "bazi"
    const [bookInfo, setBookInfo] = useState<BookInfo | null>(null)
    const [currentChapter, setCurrentChapter] = useState<ChapterContent | null>(null)
    const [selectedChapterId, setSelectedChapterId] = useState<string>("")
    const [displayMode, setDisplayMode] = useState<DisplayMode>("both")
    const [error, setError] = useState<string | null>(null)
    const [showBottomDrawer, setShowBottomDrawer] = useState(false)
    const router = useRouter()

    // 使用SWR版本的Hook获取书籍详情和章节列表
    const { chapters, isLoading, isError, error: detailError } = useBookDetail(book_id)
    
    // 获取当前选中章节的内容
    const { content: chapterContent, isLoading: chapterLoading, isError: chapterIsError, error: chapterError } = useChapterContent(selectedChapterId)

    useEffect(() => {
        if (!book_id) {
            setError("缺少书籍ID")
            return
        }

        // 只在客户端访问localStorage
        if (typeof window !== 'undefined') {
            // 尝试从localStorage获取书籍信息
            const str = localStorage.getItem("currentBook")
            if (str) {
                const parsed = JSON.parse(str)
                if (parsed.book_id === book_id) {
                    setBookInfo(parsed)
                    return
                }
                else {
                    setError("请从书籍列表进入")
                }
            }
            
            // 如果localStorage中没有匹配的书籍信息，则需要从其他地方获取
            // 这里可以考虑添加获取书籍信息的API调用
            setError("请从书籍列表进入")
        }
    }, [book_id])

    // 当获取到章节内容时更新当前章节
    useEffect(() => {
        if (chapterContent) {
            setCurrentChapter(chapterContent)
        }
    }, [chapterContent])

    const handleSelectChapter = (chapter_id: string) => {
        setSelectedChapterId(chapter_id)
    }

    const handleContentClick = () => {
        // 只在移动端显示底部抽屉
        if (window.innerWidth < 768) {
            setShowBottomDrawer(true)
        }
    }

   

    // 处理各种错误状态
    if (isError || chapterIsError || error) {
        const errorMessage = error || detailError?.message || chapterError?.message || '未知错误'
        return <div className="text-red-500 text-center p-4">{errorMessage}</div>
    }

    // 等待章节列表加载完成
    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-full">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                    <div>加载中...</div>
                </div>
            </div>
        )
    }


    if (!bookInfo) {
        return <div className="text-center p-4">未获取到书籍信息，请从书籍列表进入</div>
    }

    return (
        <main className="mx-auto bg-paper-secondary min-h-full px-4 py-4 flex flex-1 justify-center">
            <div className="container flex gap-2 flex-col w-full">
                <ReturnButton content="返回书籍列表"/>
                {/* <MobileNav title={bookInfo?.title}/> */}
                <div className="flex flex-1 gap-8 w-full">
                    {/* 章节列表 */}
                    <div className="hidden md:block w-64 shrink-0 h-fit bg-white rounded-lg shadow-xs flex-col">
                        <div className="flex justify-start px-6 mt-6 mb-2">
                            <button
                                onClick={() => setCurrentChapter(null)}
                                className="font-serif text-xl text-primary hover:text-primary/80 transition-colors"
                            >
                                {bookInfo.title}
                            </button>
                        </div>
                        <div className={cn("flex flex-col items-start px-4 py-2 bg-white rounded-lg shadow-xs sticky top-8")}>
                            {chapters.map((chapter: ChapterInfo) => {
                                if (chapter.level > bookInfo.main_chapter_level) return null
                                if (chapter.level < bookInfo.main_chapter_level) {
                                    return <div key={chapter.chapter_id} className="px-2 text-lg">{chapter.title}</div>
                                }
                                return (
                                    <TabItem
                                        key={chapter.chapter_id}
                                        onClick={() => handleSelectChapter(chapter.chapter_id)}
                                        variant="primary"
                                        className="w-full px-4"
                                        isActive={chapter.chapter_id === currentChapter?.chapter_id}
                                    >
                                        {chapter.title}
                                    </TabItem>
                                )
                            })}
                        </div>
                    </div>

                    {/* 内容区域 - 添加点击事件 */}
                    <div className="flex-1" onClick={handleContentClick}>
                        <BookContent
                            title={bookInfo.title}
                            author={bookInfo.author || "佚名"}
                            description={bookInfo.description || "暂无简介"}
                            chapterList={chapters}
                            currentChapter={currentChapter}
                            displayMode={displayMode}
                            setDisplayMode={setDisplayMode}
                        />
                    </div>
                </div>
            </div>
        </main>
    )
}
