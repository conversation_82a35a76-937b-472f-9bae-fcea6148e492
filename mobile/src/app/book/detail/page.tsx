"use client"

import { useSearchParams } from "next/navigation"
import { AuthProtected } from "@/components/auth/AuthProtected"
import { BookReader } from "@/components/book/book-reader"

// 阅读位置记录接口
// 单独的组件来处理 useSearchParams
function BookDetailContent() {
  const searchParams = useSearchParams()
  const book_id = searchParams.get("book_id") || ""

  return (
    <BookReader bookId={book_id} />
  )
}

export default function BookDetailPage() {
  return (
    <AuthProtected>
      <BookDetailContent />
    </AuthProtected>
  )
}