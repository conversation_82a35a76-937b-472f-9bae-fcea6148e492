"use client"

import { useEffect, useState, useMemo, useRef, useCallback } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { AuthProtected } from "@/components/auth/AuthProtected"
import { BookInfo, ChapterInfo, ChapterContent, DisplayMode } from "@/types/book"
import { useBookDetail, useChapterContent } from "@/hooks/use-api"
import MobileNav from "@/components/layout/mobile/nav"
import BookDetailBottom from "@/components/book/book-detail-bottom"
import { cn } from "@/lib/utils"

// 阅读位置记录接口
interface ReadingPosition {
  book_id: string
  chapter_id: string
  page_index: number
  total_pages: number
  timestamp: number
}

// 分页内容接口
interface PageContent {
  chapter_id: string
  chapter_title: string
  content: string
  isFirstPage: boolean
  isLastPage: boolean
}

// 单独的组件来处理 useSearchParams
function BookDetailContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const book_id = searchParams.get("book_id") || ""
  
  // 基础状态
  const [bookInfo, setBookInfo] = useState<BookInfo | null>(null)
  const [currentChapter, setCurrentChapter] = useState<ChapterContent | null>(null)
  const [selectedChapterId, setSelectedChapterId] = useState<string>("")
  const [displayMode, setDisplayMode] = useState<DisplayMode>("original")
  const [error, setError] = useState<string | null>(null)
  
  // 分页和阅读状态
  const [pages, setPages] = useState<PageContent[]>([])
  const [currentPageIndex, setCurrentPageIndex] = useState(0)
  const [isShowingIntro, setIsShowingIntro] = useState(true)
  const [pendingPageIndex, setPendingPageIndex] = useState<number | null>(null) // 待设置的页面索引
  
  // UI控制状态
  const [showControls, setShowControls] = useState(false)
  
  // 引用
  const contentRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  // 使用SWR版本的Hook获取书籍详情和章节列表
  const { chapters, isLoading, isError, error: detailError } = useBookDetail(book_id)
  
  // 获取当前选中章节的内容
  const { content: chapterContent, isLoading: chapterLoading } = useChapterContent(selectedChapterId)

  // 初始化书籍信息
  useEffect(() => {
    if (!book_id) {
      setError("缺少书籍ID")
      return
    }

    if (typeof window !== 'undefined') {
      const str = localStorage.getItem("currentBook")
      if (str) {
        const parsed = JSON.parse(str)
        if (parsed.book_id === book_id) {
          setBookInfo(parsed)
          return
        }
      }
      setError("请从书籍列表进入")
    }
  }, [book_id])

  // 保存阅读位置
  const saveReadingPosition = useCallback((chapterId: string, pageIndex: number, totalPages: number) => {
    if (!book_id || !chapterId) return
    
    const position: ReadingPosition = {
      book_id,
      chapter_id: chapterId,
      page_index: pageIndex,
      total_pages: totalPages,
      timestamp: Date.now()
    }
    
    localStorage.setItem(`${book_id}_reading_position`, JSON.stringify(position))
  }, [book_id])

  // 获取阅读位置
  const getReadingPosition = useCallback((): ReadingPosition | null => {
    if (!book_id) return null
    
    const str = localStorage.getItem(`${book_id}_reading_position`)
    if (!str) return null
    
    try {
      return JSON.parse(str)
    } catch {
      return null
    }
  }, [book_id])

  // 计算文本高度
  const measureTextHeight = useCallback((text: string): number => {
    if (!measureRef.current || !contentRef.current) return 0
    
    // 确保测量元素有正确的宽度和样式
    const contentWidth = contentRef.current.clientWidth
    measureRef.current.style.width = `${contentWidth - 48}px` // 减去padding
    measureRef.current.innerHTML = text
    const height = measureRef.current.scrollHeight
    measureRef.current.innerHTML = ""
    console.log("contentWidth:", contentWidth)
    
    return height
  }, [])

  // 分页算法
  const paginateContent = useCallback((chapter: ChapterContent): PageContent[] => {
    console.log('开始分页:', chapter.title, '显示模式:', displayMode)
    
    if (!contentRef.current || !measureRef.current) {
      console.log('分页失败: 缺少必要的引用')
      return []
    }
    
    const containerHeight = contentRef.current.clientHeight - 100 // 留出一些边距
    console.log('容器高度:', containerHeight, "内容长度:", chapter.content.length)
    
    if (containerHeight <= 0) {
      console.log('分页失败: 容器高度无效')
      return [] // 容器高度无效时不进行分页
    }
    
    const pages: PageContent[] = []
    
    // 处理章节内容
    let currentPageContent = ""
    let currentHeight = 0
    
    // 添加章节标题
    const titleHtml = `<h2 class="text-xl font-bold mb-4">${chapter.title}</h2>`
    const titleHeight = measureTextHeight(titleHtml)
    
    currentPageContent += titleHtml
    currentHeight += titleHeight
    
    // 逐段添加内容
    for (const section of chapter.content) {
      let sectionContent = ""
      
      if (displayMode === "original" || displayMode === "both") {
        sectionContent += `<p class="mb-4 text-base leading-relaxed">${section.content}</p>`
        if (section.quote) {
          sectionContent += `<p class="mb-4 text-sm text-gray-600">${section.quote}</p>`
        }
      }
      
      if (displayMode === "translation" && section.translation) {
        sectionContent += `<p class="mb-4 text-base leading-relaxed">${section.translation}</p>`
      }
      
      if (displayMode === "both" && section.translation) {
        sectionContent += `<div class="mb-4 p-3 bg-gray-50 rounded"><p class="text-sm text-gray-700">${section.translation}</p></div>`
      }
      
      const sectionHeight = measureTextHeight(sectionContent)
      
      console.log('当前高度:', currentHeight, '段落高度:', sectionHeight, 'sectionContent:', sectionContent, '容器高度:', containerHeight, '当前内容:', currentPageContent)
      // 如果添加这段内容会超出页面高度，创建新页
      if (currentHeight + sectionHeight > containerHeight && currentPageContent.trim()) {
        pages.push({
          chapter_id: chapter.chapter_id,
          chapter_title: chapter.title,
          content: currentPageContent,
          isFirstPage: pages.length === 0,
          isLastPage: false
        })
        
        currentPageContent = sectionContent
        currentHeight = sectionHeight
      } else {
        currentPageContent += sectionContent
        currentHeight += sectionHeight
      }
    }
    
    // 添加最后一页
    if (currentPageContent.trim()) {
      pages.push({
        chapter_id: chapter.chapter_id,
        chapter_title: chapter.title,
        content: currentPageContent,
        isFirstPage: pages.length === 0,
        isLastPage: true
      })
    }
    
    console.log('分页完成:', chapter.title, '总页数:', pages.length, '内容段数:', chapter.content.length)
    
    // 确保至少有一页
    if (pages.length === 0) {
      console.log('警告: 分页结果为空，创建默认页面')
      pages.push({
        chapter_id: chapter.chapter_id,
        chapter_title: chapter.title,
        content: `<h2 class="text-xl font-bold mb-4">${chapter.title}</h2><p>内容加载中...</p>`,
        isFirstPage: true,
        isLastPage: true
      })
    }
    
    return pages
  }, [displayMode, measureTextHeight])

  // 当章节内容加载完成时进行分页
  useEffect(() => {
    if (chapterContent && contentRef.current) {
      console.log('开始分页处理:', chapterContent.title, '待设置页面:', pendingPageIndex)
      
      const newPages = paginateContent(chapterContent)
      setPages(newPages)
      setIsShowingIntro(false)
      
      // 处理页面索引
      let targetPageIndex = 0
      
      if (pendingPageIndex !== null) {
        // 有待设置的页面索引（比如切换到上一章的最后一页）
        if (pendingPageIndex >= 999999) {
          // 特殊值表示跳转到最后一页
          targetPageIndex = Math.max(0, newPages.length - 1)
          console.log('跳转到最后一页:', targetPageIndex, '总页数:', newPages.length)
        } else {
          targetPageIndex = Math.min(pendingPageIndex, newPages.length - 1)
          console.log('跳转到指定页面:', targetPageIndex, '总页数:', newPages.length)
        }
        setPendingPageIndex(null)
      } else {
        // 默认跳到第一页
        targetPageIndex = 0
        console.log('默认跳转到第一页')
      }
      // 确保页面索引在有效范围内
      const validPageIndex = Math.max(0, Math.min(targetPageIndex, newPages.length - 1))
      setCurrentPageIndex(validPageIndex)
      
      if (validPageIndex !== targetPageIndex) {
        console.log('页面索引被调整:', targetPageIndex, '->', validPageIndex, '总页数:', newPages.length)
      }
      
              // 保存阅读位置
        if (newPages.length > 0) {
          saveReadingPosition(chapterContent.chapter_id, validPageIndex, newPages.length)
          console.log('保存阅读位置:', chapterContent.title, '页面:', validPageIndex)
        }
    }
  }, [chapterContent, pendingPageIndex, paginateContent, saveReadingPosition])

  // 处理章节选择
  const handleSelectChapter = useCallback((chapterId: string) => {
    console.log('切换章节:', chapterId)
    
    // 清除当前状态，防止新章节加载期间显示旧内容
    setPages([])
    setCurrentPageIndex(0)
    setIsShowingIntro(false)
    
    // 设置新章节
    setSelectedChapterId(chapterId)
    setShowControls(false)
  }, [])

  // 翻页功能
  const goToNextPage = useCallback(() => {
    console.log('goToNextPage调用:', { isShowingIntro, currentPageIndex, pagesLength: pages.length, selectedChapterId })
    
    if (isShowingIntro) {
      // 从介绍页进入第一章
      if (chapters.length > 0 && bookInfo) {
        // 更健壮的查找第一章逻辑
        let firstChapter = null
        
        // 首先尝试按照 main_chapter_level 查找
        if (bookInfo.main_chapter_level) {
          firstChapter = chapters.find(c => c.level === bookInfo.main_chapter_level)
        }
        
        // 如果没找到，尝试查找最小级别的章节
        if (!firstChapter) {
          const minLevel = Math.min(...chapters.map(c => c.level))
          firstChapter = chapters.find(c => c.level === minLevel)
        }
        
        // 最后的备选方案：使用第一个章节
        if (!firstChapter) {
          firstChapter = chapters[0]
        }
        
        if (firstChapter) {
          console.log('从首页进入第一章:', firstChapter.title)
          setPendingPageIndex(0) // 确保从第一页开始
          handleSelectChapter(firstChapter.chapter_id)
        } else {
          console.error('无法找到第一章，chapters:', chapters, 'bookInfo:', bookInfo)
        }
      } else {
        console.log('chapters未加载或bookInfo不存在:', { chaptersLength: chapters.length, bookInfo })
      }
      return
    }
    
    // 检查当前状态是否有效
    if (pages.length === 0) {
      console.log('pages数组为空，无法翻页')
      return
    }
    
    if (currentPageIndex < pages.length - 1) {
      const newIndex = currentPageIndex + 1
      console.log('翻到下一页:', newIndex, '/', pages.length)
      setCurrentPageIndex(newIndex)
      saveReadingPosition(pages[0].chapter_id, newIndex, pages.length)
    } else {
      // 切换到下一章
      console.log('尝试切换到下一章')
      const currentChapterIndex = chapters.findIndex(c => c.chapter_id === selectedChapterId)
      console.log('当前章节索引:', currentChapterIndex, '总章节数:', chapters.length)
      
      const nextChapter = chapters.find((c, index) => 
        index > currentChapterIndex && c.level === bookInfo?.main_chapter_level
      )
      
      if (nextChapter) {
        console.log('找到下一章:', nextChapter.title)
        setPendingPageIndex(0) // 下一章从第一页开始
        handleSelectChapter(nextChapter.chapter_id)
      } else {
        console.log('没有下一章了')
      }
    }
  }, [isShowingIntro, currentPageIndex, pages, chapters, selectedChapterId, bookInfo, handleSelectChapter, saveReadingPosition])

  const goToPrevPage = useCallback(() => {
    console.log('goToPrevPage调用:', { isShowingIntro, currentPageIndex, pagesLength: pages.length, selectedChapterId })
    
    if (isShowingIntro) {
      console.log('在介绍页，无法翻页')
      return
    }
    
    // 检查当前状态是否有效
    if (pages.length === 0) {
      console.log('pages数组为空，无法翻页')
      return
    }
    
    if (currentPageIndex > 0) {
      const newIndex = currentPageIndex - 1
      console.log('翻到上一页:', newIndex, '/', pages.length)
      setCurrentPageIndex(newIndex)
      saveReadingPosition(pages[0].chapter_id, newIndex, pages.length)
    } else {
      // 切换到上一章的最后一页
      console.log('尝试切换到上一章')
      const currentChapterIndex = chapters.findIndex(c => c.chapter_id === selectedChapterId)
      console.log('当前章节索引:', currentChapterIndex)
      
      const prevChapter = chapters.slice(0, currentChapterIndex).reverse().find(c => 
        c.level === bookInfo?.main_chapter_level
      )
      
      if (prevChapter) {
        console.log('找到上一章:', prevChapter.title)
        // 设置待跳转的页面索引为最后一页（会在新章节加载完成后设置）
        setPendingPageIndex(999999) // 使用一个很大的数，会被限制为实际的最后一页
        handleSelectChapter(prevChapter.chapter_id)
      } else {
        // 回到介绍页
        console.log('没有上一章，回到介绍页')
        setIsShowingIntro(true)
        setCurrentChapter(null)
        setPages([])
        setCurrentPageIndex(0)
        setPendingPageIndex(null)
      }
    }
  }, [isShowingIntro, currentPageIndex, pages, chapters, selectedChapterId, bookInfo, handleSelectChapter, saveReadingPosition])

  // 恢复阅读位置
  useEffect(() => {
    if (chapters.length > 0 && bookInfo && !selectedChapterId && !isLoading) {
      const position = getReadingPosition()
      
      console.log('尝试恢复阅读位置:', { position, chaptersCount: chapters.length })
      
      if (position) {
        // 恢复到之前的阅读位置
        const chapter = chapters.find(c => c.chapter_id === position.chapter_id)
        if (chapter) {
          console.log('找到之前的阅读章节:', chapter.title, '页面:', position.page_index)
          setPendingPageIndex(position.page_index)
          setSelectedChapterId(position.chapter_id)
          return
        } else {
          console.log('之前的阅读章节不存在，显示介绍页')
        }
      } else {
        console.log('没有阅读位置记录，显示介绍页')
      }
      
      // 默认显示介绍页
      setIsShowingIntro(true)
    }
  }, [chapters, bookInfo, selectedChapterId, getReadingPosition, isLoading])

  // 开发环境下的状态监控
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('状态变化:', {
        isShowingIntro,
        selectedChapterId,
        currentPageIndex,
        pagesLength: pages.length,
        chaptersLength: chapters.length,
        isLoading
      })
    }
  }, [isShowingIntro, selectedChapterId, currentPageIndex, pages.length, chapters.length, isLoading])

  // 处理控制面板显示
  const handleContentClick = () => {
    setShowControls(!showControls)
  }

  const handleCloseControls = () => {
    setShowControls(false)
  }

  // 渲染介绍页
  const renderIntroPage = () => {
    if (!bookInfo) return null
    
    const canStartReading = chapters.length > 0 && !isLoading
    
    return (
      <div className="flex flex-col items-center justify-center h-full text-center px-8">
        <h1 className="text-3xl font-bold mb-4 text-foreground">{bookInfo.title}</h1>
        {bookInfo.author && (
          <p className="text-lg text-secondary mb-6">作者：{bookInfo.author}</p>
        )}
        {bookInfo.description && (
          <p className="text-base text-muted-foreground leading-relaxed mb-8 max-w-md">
            {bookInfo.description}
          </p>
        )}
        
        {/* 状态提示 */}
        <div className="text-sm text-muted-foreground">
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span>正在加载章节...</span>
            </div>
          ) : canStartReading ? (
            <span>点击右侧开始阅读</span>
          ) : (
            <span className="text-orange-500">章节加载失败，请刷新重试</span>
          )}
        </div>
        
        {/* 调试信息（开发时显示） */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 text-xs text-gray-400">
            <div>章节数量: {chapters.length}</div>
            <div>主章节级别: {bookInfo.main_chapter_level}</div>
            <div>加载状态: {isLoading ? '加载中' : '已完成'}</div>
          </div>
        )}
      </div>
    )
  }

  // 渲染当前页内容
  const renderCurrentPage = () => {
    if (isShowingIntro) {
      return renderIntroPage()
    }
    
    if (pages.length === 0) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2 mx-auto"></div>
            <div>加载中...</div>
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 text-xs text-gray-400">
                <div>selectedChapterId: {selectedChapterId}</div>
                <div>chapterLoading: {chapterLoading ? '是' : '否'}</div>
              </div>
            )}
          </div>
        </div>
      )
    }
    
    const currentPage = pages[currentPageIndex]
    if (!currentPage) {
      console.error('页面索引错误:', { currentPageIndex, pagesLength: pages.length })
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-red-500">
            页面加载错误，请刷新重试
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 text-xs">
                <div>currentPageIndex: {currentPageIndex}</div>
                <div>pages.length: {pages.length}</div>
              </div>
            )}
          </div>
        </div>
      )
    }
    
    return (
      <div className="h-full overflow-hidden p-6">
        <div 
          className="h-full overflow-hidden text-foreground leading-relaxed"
          dangerouslySetInnerHTML={{ __html: currentPage.content }}
        />
      </div>
    )
  }

  // 错误处理
  if (isError || error) {
    const errorMessage = error || detailError?.message || '未知错误'
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-red-500 text-center p-4">{errorMessage}</div>
      </div>
    )
  }

  // 加载状态
  if (isLoading || !bookInfo) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2 mx-auto"></div>
          <div>加载中...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-screen w-full flex flex-col bg-white">
      {/* 顶部安全区域 */}
      <div className="h-mobile-top-nav bg-white" />
      
      {/* 主要阅读区域 */}
      <div className="flex-1 flex relative">
        {/* 左侧翻页区域 (20%) */}
        <div 
          className="w-1/5 h-full cursor-pointer flex items-center justify-center"
          onClick={goToPrevPage}
        >
          <div className="w-full h-full" />
        </div>
        
        {/* 中间内容区域 (60%) */}
        <div 
          ref={contentRef}
          className="w-3/5 h-full cursor-pointer relative"
          onClick={handleContentClick}
        >
          {renderCurrentPage()}
          
          {/* 页码指示 */}
          {!isShowingIntro && pages.length > 0 && (
            <div className="absolute bottom-4 right-4 text-sm text-muted-foreground">
              {currentPageIndex + 1} / {pages.length}
            </div>
          )}
        </div>
        
        {/* 右侧翻页区域 (20%) */}
        <div 
          className={cn(
            "w-1/5 h-full flex items-center justify-center",
            (isShowingIntro && (isLoading || chapters.length === 0)) ? 
              "cursor-not-allowed opacity-50" : "cursor-pointer"
          )}
          onClick={() => {
            // 首页状态下，只有在数据准备好时才允许翻页
            if (isShowingIntro && (isLoading || chapters.length === 0)) {
              console.log('数据未准备好，无法翻页')
              return
            }
            goToNextPage()
          }}
        >
          <div className="w-full h-full" />
        </div>
      </div>
      
      {/* 底部安全区域 */}
      <div className="pb-safe-bottom bg-white" />
      
      {/* 隐藏的测量元素 */}
      <div 
        ref={measureRef}
        className="absolute -top-full left-0 invisible pointer-events-none overflow-hidden"
        style={{ 
          fontSize: '16px',
          lineHeight: '1.5',
          fontFamily: 'inherit',
          padding: '24px',
          boxSizing: 'border-box'
        }}
      />
      
      {/* 顶部控制栏 */}
      {showControls && (
        <>
          {/* 遮罩层 */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-20 z-40"
            onClick={handleCloseControls}
          />
          
          {/* 顶部导航 */}
          <div className={cn(
            "fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform transition-transform duration-300",
            showControls ? "translate-y-0" : "-translate-y-full"
          )}>
            <div className="h-safe-top" />
            <MobileNav title={bookInfo.title} />
          </div>
          
          {/* 底部控制栏 */}
          <div className={cn(
            "fixed bottom-0 left-0 right-0 z-50 transform transition-transform duration-300",
            showControls ? "translate-y-0" : "translate-y-full"
          )}>
            <BookDetailBottom />
            <div className="pb-safe-bottom bg-white" />
          </div>
        </>
      )}
    </div>
  )
}

export default function BookDetailPage() {
  return (
    <AuthProtected>
      <BookDetailContent />
    </AuthProtected>
  )
}