"use client"

import { useState, FormEvent } from "react"
// import { baziAPI } from "@/lib/api" // 正式API
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"

interface BirthData {
  name: string
  gender: "male" | "female"
  birthDate: string
  birthTime: string
  isLunar: boolean
  hasDST: boolean
  birthplace: string
}

interface BaziResult {
  yearPillar: { heavenlyStem: string; earthlyBranch: string }
  monthPillar: { heavenlyStem: string; earthlyBranch: string }
  dayPillar: { heavenlyStem: string; earthlyBranch: string }
  hourPillar: { heavenlyStem: string; earthlyBranch: string }
  lunarDate: string
  solarDate: string
  nayin: string[]
  dayMaster: string
  wuxing: {
    wood: number
    fire: number
    earth: number
    metal: number
    water: number
  }
  analysis: {
    summary: string
    luck: string
    personality: string
    career: string
    relationships: string
    health: string
  }
}

export default function BaziDivinate() {
  const [birthData, setBirthData] = useState<BirthData>({
    name: "",
    gender: "male",
    birthDate: "",
    birthTime: "",
    isLunar: false,
    hasDST: false,
    birthplace: "",
  })

  const [result, setResult] = useState<BaziResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setBirthData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleRadioChange = (name: string, value: string) => {
    setBirthData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      //const data = await baziAPI.calculate(birthData)
      //setResult(data)
    } catch (err: any) {
      setError(err.response?.data?.detail || "测算失败，请稍后再试")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid gap-8 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>输入信息</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 姓名 */}
            <div className="space-y-2">
              <label
                htmlFor="name"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                姓名（选填）
              </label>
              <Input
                id="name"
                name="name"
                value={birthData.name}
                onChange={handleChange}
                placeholder="请输入姓名"
              />
            </div>

            {/* 性别 */}
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none">性别</label>
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="gender"
                    value="male"
                    checked={birthData.gender === "male"}
                    onChange={() => handleRadioChange("gender", "male")}
                    className="h-4 w-4 text-primary"
                  />
                  <span>男</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="gender"
                    value="female"
                    checked={birthData.gender === "female"}
                    onChange={() => handleRadioChange("gender", "female")}
                    className="h-4 w-4 text-primary"
                  />
                  <span>女</span>
                </label>
              </div>
            </div>

            {/* 出生日期 */}
            <div className="space-y-2">
              <label
                htmlFor="birthDate"
                className="text-sm font-medium leading-none"
              >
                出生日期
              </label>
              <Input
                id="birthDate"
                name="birthDate"
                type="date"
                value={birthData.birthDate}
                onChange={handleChange}
                required
              />
            </div>

            {/* 出生时间 */}
            <div className="space-y-2">
              <label
                htmlFor="birthTime"
                className="text-sm font-medium leading-none"
              >
                出生时间
              </label>
              <Input
                id="birthTime"
                name="birthTime"
                type="time"
                value={birthData.birthTime}
                onChange={handleChange}
                required
              />
            </div>

            {/* 是否农历 */}
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none">历法选择</label>
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="isLunar"
                    checked={!birthData.isLunar}
                    onChange={() => handleRadioChange("isLunar", "false")}
                    className="h-4 w-4 text-primary"
                  />
                  <span>公历</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="isLunar"
                    checked={birthData.isLunar}
                    onChange={() => handleRadioChange("isLunar", "true")}
                    className="h-4 w-4 text-primary"
                  />
                  <span>农历</span>
                </label>
              </div>
            </div>

            {/* 是否夏令时 */}
            <div className="flex items-center gap-2">
              <input
                id="hasDST"
                name="hasDST"
                type="checkbox"
                checked={birthData.hasDST}
                onChange={handleChange}
                className="h-4 w-4 text-primary"
              />
              <label
                htmlFor="hasDST"
                className="text-sm font-medium leading-none"
              >
                出生地是否实行夏令时
              </label>
            </div>

            {/* 出生地点 */}
            <div className="space-y-2">
              <label
                htmlFor="birthplace"
                className="text-sm font-medium leading-none"
              >
                出生地点
              </label>
              <Input
                id="birthplace"
                name="birthplace"
                value={birthData.birthplace}
                onChange={handleChange}
                placeholder="例如：北京市朝阳区"
                required
              />
            </div>

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? "计算中..." : "开始测算"}
            </Button>

            {error && <p className="text-sm text-destructive">{error}</p>}
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>测算结果</CardTitle>
        </CardHeader>
        <CardContent>
          {!result ? (
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <p className="text-muted-foreground">填写完整信息并点击按钮</p>
              <p className="text-muted-foreground">命盘结果将在这里显示</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="space-y-2">
                <h3 className="font-medium">基本信息</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>公历: {result.solarDate}</div>
                  <div>农历: {result.lunarDate}</div>
                </div>
              </div>

              {/* 八字命盘 */}
              <div className="space-y-2">
                <h3 className="font-medium">八字命盘</h3>
                <div className="grid grid-cols-4 gap-2 text-center">
                  <div className="flex flex-col rounded-md border p-2">
                    <div className="font-bold">{result.yearPillar.heavenlyStem}</div>
                    <div>{result.yearPillar.earthlyBranch}</div>
                  </div>
                  <div className="flex flex-col rounded-md border p-2">
                    <div className="font-bold">{result.monthPillar.heavenlyStem}</div>
                    <div>{result.monthPillar.earthlyBranch}</div>
                  </div>
                  <div className="flex flex-col rounded-md border p-2 bg-fate-100 dark:bg-fate-900">
                    <div className="font-bold">{result.dayPillar.heavenlyStem}</div>
                    <div>{result.dayPillar.earthlyBranch}</div>
                  </div>
                  <div className="flex flex-col rounded-md border p-2">
                    <div className="font-bold">{result.hourPillar.heavenlyStem}</div>
                    <div>{result.hourPillar.earthlyBranch}</div>
                  </div>
                  <div className="text-xs">年柱</div>
                  <div className="text-xs">月柱</div>
                  <div className="text-xs">日柱</div>
                  <div className="text-xs">时柱</div>
                </div>
                <div className="mt-2">
                  <span className="text-sm">日主：</span>
                  <span className="font-bold">{result.dayMaster}</span>
                </div>
              </div>

              {/* 纳音 */}
              <div className="space-y-2">
                <h3 className="font-medium">纳音五行</h3>
                <div className="flex flex-wrap gap-2 text-sm">
                  {result.nayin.map((nayin, index) => (
                    <div key={index} className="rounded-md bg-muted px-2 py-1">
                      {nayin}
                    </div>
                  ))}
                </div>
              </div>

              {/* 五行统计 */}
              <div className="space-y-2">
                <h3 className="font-medium">五行统计</h3>
                <div className="grid grid-cols-5 gap-2 text-center">
                  <div className="rounded-md bg-green-100 dark:bg-green-900 px-2 py-1">
                    <div>木</div>
                    <div className="font-bold">{result.wuxing.wood}</div>
                  </div>
                  <div className="rounded-md bg-red-100 dark:bg-red-900 px-2 py-1">
                    <div>火</div>
                    <div className="font-bold">{result.wuxing.fire}</div>
                  </div>
                  <div className="rounded-md bg-yellow-100 dark:bg-yellow-900 px-2 py-1">
                    <div>土</div>
                    <div className="font-bold">{result.wuxing.earth}</div>
                  </div>
                  <div className="rounded-md bg-gray-100 dark:bg-gray-700 px-2 py-1">
                    <div>金</div>
                    <div className="font-bold">{result.wuxing.metal}</div>
                  </div>
                  <div className="rounded-md bg-blue-100 dark:bg-blue-900 px-2 py-1">
                    <div>水</div>
                    <div className="font-bold">{result.wuxing.water}</div>
                  </div>
                </div>
              </div>

              {/* 命盘分析 */}
              <div className="space-y-2">
                <h3 className="font-medium">命盘分析</h3>
                <div className="space-y-3 text-sm">
                  <p>{result.analysis.summary}</p>
                  <div>
                    <h4 className="font-medium">性格特点</h4>
                    <p>{result.analysis.personality}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">事业发展</h4>
                    <p>{result.analysis.career}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">人际关系</h4>
                    <p>{result.analysis.relationships}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">健康状况</h4>
                    <p>{result.analysis.health}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 