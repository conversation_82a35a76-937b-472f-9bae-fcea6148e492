"use client"

import { AuthGuard } from "@/components/auth/AuthGuard"
import ZiweiDivinate from "../../../components/ziwei/ziwei-divinate"

export default function ZiweiDivinatePage() {
  return (
    <AuthGuard>
      <div className="flex flex-1 w-full bg-background justify-center items-center ">
        <div className="flex px-4 py-4 md:py-8 items-center justify-center w-full">
          <ZiweiDivinate />
        </div>
      </div>
    </AuthGuard>
  )
}