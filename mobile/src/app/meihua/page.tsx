"use client"

import dynamic from 'next/dynamic'
//import { Toast } from 'antd-mobile'
//import { Button } from "@/components/ui/button"
import { useState } from 'react'
import { Button, Space, Toast } from 'antd-mobile'
import { UploadOutline } from 'antd-mobile-icons'





export default function MeihuaPage() {
  const [showError, setShowError] = useState(false)

  return (
    <div className="h-screen flex flex-col items-center justify-center">
         <Space wrap>
        <Button
            onClick={() => {
              Toast.show({
                icon: "fail",
                content: '名称已存在',
              })
            }}
          >
        触发错误提示
      </Button></Space>

    </div>
  )
} 