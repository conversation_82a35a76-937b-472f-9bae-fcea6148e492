"use client"
import React from 'react';

export default function MeihuaDivinatePage() {
  //const lunar = Lunar.fromDate(new Date())
  // console.log("lunar", lunar.getTimeZhi())
  return (
    <div className="bg-crepe w-full min-h-screen px-6 py-12 flex flex-col items-center justify-center">
      {/* Card 1 - Light Purple */}
      <div className="w-full max-w-md mb-8 backdrop-blur-md bg-lavender/70 rounded-3xl shadow-lg p-8 border border-lavender/40 transition-transform hover:scale-105">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">优雅设计</h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              每一个细节都经过深思熟虑，为您的视觉体验打造极致享受。
            </p>
          </div>
          <div className="text-purple-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Card 2 - White */}
      <div className="w-full max-w-md mb-8 backdrop-blur-md bg-white/80 rounded-3xl shadow-lg p-8 border border-gray-200/40 transition-transform hover:scale-105">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">极简风格</h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              简洁而不失美感的设计语言，让内容成为焦点。
            </p>
          </div>
          <div className="text-indigo-500">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </div>
        </div>
      </div>

      {/* Card 3 - Light Blue */}
      <div className="w-full max-w-md backdrop-blur-md bg-skyblue/70 rounded-3xl shadow-lg p-8 border border-skyblue/40 transition-transform hover:scale-105">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">轻盈悬浮</h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              轻柔的玻璃拟态效果与微妙阴影，营造出一种漂浮的视觉感受。
            </p>
          </div>
          <div className="text-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2v2m0 16v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2m16 0h2M6.34 17.66l1.41-1.41M16.25 6.34l1.41-1.41"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}