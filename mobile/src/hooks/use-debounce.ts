import { useState, useEffect } from 'react'

/**
 * 防抖Hook，用于优化搜索性能
 * @param value 需要防抖的值
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的值
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    // 设置定时器
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    // 清理函数：如果value在delay时间内再次改变，则清除上一个定时器
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
} 