"use client"

import useS<PERSON> from 'swr'
import useSWRMutation from 'swr/mutation'
import { booksAPI, createFetcher } from '@/lib/api/api-client'
import { useAuth } from './use-auth'

// 通用SWR配置
const defaultConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  shouldRetryOnError: false,
  errorRetryCount: 2,
}

// 书籍相关hooks
export function useBookList(params: {
  page?: string
  size?: string
  is_completed?: string
  category?: string | null
} = {}) {
  const key = ['/book/list', params]
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => booksAPI.getBooks(params),
    defaultConfig
  )

  return {
    books: data?.items || [],
    total: data?.total || 0,
    totalPages: data?.total_pages || 0,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useBookDetail(bookId: string | null) {
  const { token } = useAuth()
  const key = token ? ['/book', bookId] : null
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => booksAPI.getBookDetail(bookId, token || undefined),
    defaultConfig
  )

  return {
    chapters: data?.chapter_list || [],
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useChapterContent(chapterId: string | null) {
  const { token } = useAuth()
  const key = token && chapterId ? ['/book/chapter', chapterId] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => booksAPI.getChapterContent(chapterId, token || undefined),
    defaultConfig
  )

  return {
    content: data?.chapters,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 通用数据获取hook
export function useData<T>(
  endpoint: string | null,
  requireAuth: boolean = false
) {
  const { token } = useAuth()
  const fetcher = createFetcher(requireAuth ? token || undefined : undefined)
  
  // 如果需要认证但没有token，不发起请求
  const key = requireAuth && !token ? null : endpoint
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    fetcher,
    defaultConfig
  )

  return {
    data,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
} 