// hooks/useTopTabActive.ts
'use client';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const TABS = ['case', 'course', 'book'] as const;
type TabKey = (typeof TABS)[number];

export function useTopTabActive(category: string) {
  const pathname = usePathname();
  const router = useRouter();

  const [pendingTab, setPendingTab] = useState<TabKey | null>(null);
  const [currentTab, setCurrentTab] = useState<TabKey | null>(() => {
    const parts = pathname.split('/');
    const tab = parts[2];
    return TABS.includes(tab as Tab<PERSON>ey) ? (tab as TabKey) : 'case'; 
  });

  useEffect(() => {
    const tab = pathname.split('/')[2];
    if (TABS.includes(tab as TabKey)) {
      setCurrentTab(tab as Tab<PERSON>ey);
      setPendingTab(null);
    }
  }, [pathname]);

  const handleTabClick = (tab: TabKey) => {
    if (tab === currentTab) return;
    setPendingTab(tab);
    router.push(`/${category}/${tab}`);
  };

  const activeTab = pendingTab || currentTab;

  return { activeTab, handleTabClick };
}
