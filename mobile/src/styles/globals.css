@import 'tailwindcss';

/* 引入备用样式 */
@import './fallback.css';

@config '../../tailwind.config.js';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility writing-mode-vertical-rl {
  writing-mode: vertical-rl;
  text-orientation: upright;
}

@utility font-song {
  font-family: 'Songti SC', 'STSong', '华文宋体', serif;
}

@utility font-kai {
  font-family: 'Kaiti SC', 'STKaiti', '华文楷体', serif;
}

@layer base {
  :root {
    /* 基本背景色和字体颜色 */
    --background: 40 23% 97%;
    /* --background: 210 40% 98%; */
    /*--background: 45 22% 96%;*/
    --foreground: 215 28% 27%; 

    /* 主色调 blue-600/white */
    /* --primary: 221 83% 53%; */
    --primary: 136 24% 38%;
    --primary-foreground: 0,0%,100%;
 
    /* 次要色调 - 金色/gray-100 */
    --secondary: 46 65% 45%;
    /* --secondary: 40 36% 53%; */
    --secondary-dark: 46 65% 30%;
    --secondary-foreground: 220 14% 96%;
 
    /* 辅助色：blue-100/黑色， Muted backgrounds such as <TabsList />, <Skeleton /> and <Switch /> */
    /* --muted: 214 95% 93%;  */
    --muted: 138 25% 88%;
    --muted-foreground: 215 14% 15%;
   
    /* Zinc-50/Zinc-800 Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
    --popover: 0 0% 98%;
    --popover-foreground: 240 4% 16%;

    /* blue-800/gray-100 Used for accents such as hover effects on <DropdownMenuItem>, <SelectItem>...etc */
    /* --accent: 226 71% 40%; */
    --accent: 136 24% 30%;
    --accent-foreground: 220 14% 96%;

    /* Zinc-50/Zinc-800 Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
    --card: 214 100% 99%; 
    --card-foreground: 240 4% 16%;

    /*blue-100*/
    /* --border: 214 95% 83%; */
    --border: 40 21% 89%;
    --input: 40 21% 89%;

    /* 警告/错误色 红色*/
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    /* 书本背景色 */
    /* --book-background: 36 71% 98%; */
    --book-background: 46 65% 30%;
    --book-foreground: 215 28% 27%;
    --book-border: 36 71% 80%;
    
    /* focus ring */
    --ring: 40 38% 80%;

    /* 纸张色 */
    --paper: 38 36% 96%;
    --paper-secondary: 40 38% 94%;

    --tertiary: 136 24% 38%;
    --tertiary-light: 138 25% 90%;
    --tertiary-foreground: 215 28% 27%;

    --radius: 0.5rem;
    
    /* 字体配置：Next.js优化优先，备用中文字体 */
    --font-sans: -apple-system, "PingFang SC", BlinkMacSystemFont, "Noto Sans SC", "Microsoft YaHei", "Heiti SC", sans-serif;

    --content-width: 1440px;
    
    /* 移动端导航栏高度变量 */
    --mobile-top-nav-height: 3rem;
    --mobile-bottom-nav-height: 4.5rem;

    --safe-bottom-height: 14px;

  }
}
 
@layer base {
  /* html {
    scrollbar-gutter: stable both-edges;
  }  */
  body {
    overflow-y: scroll;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: var(--font-sans);
    }
    
}

@layer utilities {
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 15px;
    height: 8px;
    background: transparent;
  }
  @media (pointer: coarse) {
    ::-webkit-scrollbar {
      display: none;
    }
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/20 hover:bg-primary/40 rounded-full transition-colors;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary;
  }

  /* 中国传统元素样式 */
  .chinese-pattern {
    @apply bg-primary;
    background-image:
      radial-gradient(
        circle at 100% 150%,
        #f5f5f5 24%,
        #fff 24%,
        #fff 28%,
        #f5f5f5 28%,
        #f5f5f5 36%,
        #fff 36%,
        #fff 40%,
        transparent 40%,
        transparent
      ),
      radial-gradient(
        circle at 0 150%,
        #f5f5f5 24%,
        #fff 24%,
        #fff 28%,
        #f5f5f5 28%,
        #f5f5f5 36%,
        #fff 36%,
        #fff 40%,
        transparent 40%,
        transparent
      ),
      radial-gradient(
        circle at 50% 100%,
        #fff 10%,
        #f5f5f5 10%,
        #f5f5f5 23%,
        #fff 23%,
        #fff 30%,
        #f5f5f5 30%,
        #f5f5f5 43%,
        #fff 43%,
        #fff 50%,
        #f5f5f5 50%,
        #f5f5f5 63%,
        #fff 63%,
        #fff 71%,
        transparent 71%,
        transparent
      ),
      radial-gradient(
        circle at 100% 50%,
        #fff 5%,
        #f5f5f5 5%,
        #f5f5f5 15%,
        #fff 15%,
        #fff 20%,
        #f5f5f5 20%,
        #f5f5f5 29%,
        #fff 29%,
        #fff 34%,
        #f5f5f5 34%,
        #f5f5f5 44%,
        #fff 44%,
        #fff 49%,
        transparent 49%,
        transparent
      ),
      radial-gradient(
        circle at 0 50%,
        #fff 5%,
        #f5f5f5 5%,
        #f5f5f5 15%,
        #fff 15%,
        #fff 20%,
        #f5f5f5 20%,
        #f5f5f5 29%,
        #fff 29%,
        #fff 34%,
        #f5f5f5 34%,
        #f5f5f5 44%,
        #fff 44%,
        #fff 49%,
        transparent 49%,
        transparent
      );
    background-size: 100px 50px;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
} 

/* 毛玻璃效果 */
.glass-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  /* background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2); */
}

/* 移动端安全区域支持 - 基础定义 */
.safe-area-inset-top {
  padding-top: max(14px, env(safe-area-inset-top));
}

.safe-area-inset-bottom {
  /* iOS Safari 中会被 env() 覆盖（如果有值） */
  padding-bottom: max(var(--safe-bottom-height), env(safe-area-inset-bottom));
}

.safe-area-inset-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-inset-right {
  padding-right: env(safe-area-inset-right);
}

/* 移动端底部导航栏高度定义 - 使用CSS变量确保一致性 */
.mobile-bottom-nav {
  /* 基础高度：导航内容高度 + 默认安全间距（适用于所有环境）*/
  height: calc(var(--mobile-bottom-nav-height) + max(var(--safe-bottom-height), env(safe-area-inset-bottom)));
  /* 基础内边距：默认安全间距 */
  padding-bottom: max(var(--safe-bottom-height), env(safe-area-inset-bottom));
}
/* 顶部导航栏高度定义 */ 
.mobile-top-nav {
  /* 基础高度：导航内容高度 + 默认安全间距（适用于所有环境）*/
  height: calc(var(--mobile-top-nav-height) + env(safe-area-inset-top));
  /* 基础内边距：默认安全间距 */
  padding-top: env(safe-area-inset-top);
}

.pb-safe-bottom {
  padding-bottom: max(var(--safe-bottom-height), env(safe-area-inset-bottom));
}

.pb-bottom-nav {
  /* 基础预留：导航栏高度 */
  padding-bottom: var(--mobile-bottom-nav-height);
}

.h-safe-top {
    height: max(44px, env(safe-area-inset-top));
}

/* iOS模拟器和真实设备的特殊处理 */
@media screen and (max-width: 428px) {
  /* iPhone 14 Pro Max 和类似设备 */
  .h-safe-top {
    height: max(44px, env(safe-area-inset-top));
  }
  
  .safe-area-inset-top {
    padding-top: max(44px, env(safe-area-inset-top));
  }
}

/* 针对不同iPhone型号的精确适配 */
/* iPhone 16 Pro (402x874) */
@media screen and (device-width: 402px) and (device-height: 874px) {
  .h-safe-top {
    height: max(59px, env(safe-area-inset-top));
  }
  .safe-area-inset-top {
    padding-top: max(59px, env(safe-area-inset-top));
  }
  /* .safe-area-inset-bottom {
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  } */
}

/* iPhone 14 Pro Max (430x932) */
@media screen and (device-width: 430px) and (device-height: 932px) {
  .h-safe-top {
    height: max(59px, env(safe-area-inset-top));
  }
  .safe-area-inset-top {
    padding-top: max(59px, env(safe-area-inset-top));
  }
  /* .safe-area-inset-bottom {
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  } */
}

/* iPhone 14 Pro (393x852) */
@media screen and (device-width: 393px) and (device-height: 852px) {
  .h-safe-top {
    height: max(59px, env(safe-area-inset-top));
  }
  .safe-area-inset-top {
    padding-top: max(59px, env(safe-area-inset-top));
  }
  /* .safe-area-inset-bottom {
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  } */
}

/* iPhone 13/12 Pro (390x844) */
@media screen and (device-width: 390px) and (device-height: 844px) {
  .h-safe-top {
    height: max(47px, env(safe-area-inset-top));
  }
  .safe-area-inset-top {
    padding-top: max(47px, env(safe-area-inset-top));
  }
  /* .safe-area-inset-bottom {
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  } */
}

/* iPhone 12 mini (375x812) */
@media screen and (device-width: 375px) and (device-height: 812px) {
  .h-safe-top {
    height: max(44px, env(safe-area-inset-top));
  }
  .safe-area-inset-top {
    padding-top: max(44px, env(safe-area-inset-top));
  }
  /* .safe-area-inset-bottom {
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  } */
}

/* 文本截断样式 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  button, a {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* 防止页面在输入框聚焦时滚动 */
  .input-container {
    -webkit-overflow-scrolling: touch;
  }
}

.adm-toast-icon svg {
  display: inline-block;
  vertical-align: middle;
}
