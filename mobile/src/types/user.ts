export interface User {
  id: string
  name: string
  email?: string
  phone?: string
  role?: string
  expiresAt?: number
}

export interface LoginCredentials {
  loginType: "email" | "phone"
  loginId: string
  password: string
} 

export interface ZiweiDivinateRequest {
  name: string
  gender: "male" | "female"
  // 用于测算的公历时间：北京时间还是真太阳时由用户的选择决定
  divinateTime: string
  // 真太阳时
  trueSolarTime: string
  //公历时间
  solarTime: string
  // 阴历时间
  lunarTime: string
  // 时辰
  timeIndex: number 
}

export interface LocationItem {
  value: string
  label: string
}

export interface BirthData {
  name: string
  gender: "male" | "female"
  birthTime: string  // 格式: "YYYY-MM-DD HH:mm"
  // 是否是农历
  isLunar: boolean
  // 是否使用真太阳时
  useTrueSolarTime: boolean
  // 是否是夏令时
  isDST: boolean
  // 是否是早晚子时
  isEarlyOrLateNight: boolean
  // 出生地
  birthplace: string
  // 经度
  longitude: number
  // 关系
  relationship: "family" | "classmate" | "friend" | "colleague" | "other"
}