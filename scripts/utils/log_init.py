import os
import logging.config
from pathlib import Path

def setup_logging():
    """初始化日志配置"""
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 获取配置文件路径
    config_path = Path(__file__).parent.parent / "logging.conf"
    
    if config_path.exists():
        logging.config.fileConfig(
            config_path,
            disable_existing_loggers=False
        )
    else:
        # 如果配置文件不存在，使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(
                    log_dir / "llm.log",
                    encoding='utf-8'
                )
            ]
        )
        logging.warning("Logging config file not found, using basic configuration") 