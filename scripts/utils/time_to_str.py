from datetime import datetime

def timestamp_to_string(timestamp, format_string='%Y-%m-%d %H:%M:%S'):
    """
    将时间戳转换为指定格式的字符串。

    参数:
        timestamp (float or int): 时间戳（例如：1712323200.0 或 1712323200）
        format_string (str):      输出的时间格式，默认是 'YYYY-MM-DD HH:MM:SS'

    返回:
        str: 格式化后的时间字符串
    """
    # 将时间戳转换为 datetime 对象
    dt = datetime.fromtimestamp(timestamp)
    # 将 datetime 对象格式化为字符串
    return dt.strftime(format_string)

import sys

result = timestamp_to_string(int(sys.argv[1]))
print(result)
