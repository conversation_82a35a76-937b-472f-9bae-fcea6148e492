from enum import Enum
from typing import Dict, Optional

class ModelType(Enum):
    """支持的模型类型"""
    QWEN = "qwen"
    GPT = "gpt"
    DOUBAO = "doubao"
    DEEPSEEK = "deepseek"

class ModelCapability(Enum):
    """模型能力类型"""
    MAIN = "main"        # 通用大模型
    VL = "vl"           # 视觉语言模型
    REASON = "reason"   # 推理增强模型
    CODE = "code"       # 代码模型

# API URL 配置
API_URLS: Dict[ModelType, str] = {
    # 通义千问
    ModelType.QWEN: "https://dashscope.aliyuncs.com/compatible-mode/v1",
    # 用第三方的接口
    ModelType.GPT: "https://api2.road2all.com/v1",
    #ModelType.GPT: "https://api.openai.com/v1",
    # 豆包
    ModelType.DOUBAO: "https://ark.cn-beijing.volces.com/api/v3",
    # 这里用百炼平台的Deepseek接口
    ModelType.DEEPSEEK: "https://dashscope.aliyuncs.com/compatible-mode/v1",
}

# 模型api key name
API_KEY_NAME: Dict[ModelType, str] = {
    ModelType.GPT: 'OPENAI_API_KEY',
    ModelType.QWEN: 'DASHSCOPE_API_KEY',
    ModelType.DOUBAO: 'ARK_API_KEY',
    # 这里用千问百炼平台的接口
    ModelType.DEEPSEEK: "DASHSCOPE_API_KEY"
}

# 定义每种模型类型对应的可用模型名称及其API KEY Name
MODEL_NAMES: Dict[ModelType, Dict[ModelCapability, str]] = {
    ModelType.QWEN: {
        ModelCapability.MAIN: "qwen-max",
        ModelCapability.VL: "qwen-vl-max-latest"
    },
    ModelType.GPT: {
        ModelCapability.MAIN: "gpt-4o",
        ModelCapability.REASON: "gpt-o1"
    },
    ModelType.DOUBAO: {
        ModelCapability.VL: "ep-20241217120600-bjcgb",     # DOUBAO_VISION_PRO
        ModelCapability.MAIN: "ep-20241217122341-9h55r"    # DOUBAO_PRO
    },
    ModelType.DEEPSEEK: {
        ModelCapability.REASON: "deepseek-r1",
        ModelCapability.MAIN: "deepseek-v3"
    }
}

def get_available_models(model_type: ModelType) -> Dict[ModelCapability, str]:
    """获取指定类型的所有可用模型名称及其能力"""
    return MODEL_NAMES.get(model_type, {})

def get_model_by_capability(model_type: ModelType, capability: ModelCapability) -> Optional[str]:
    """获取指定类型和能力的模型名称"""
    models = MODEL_NAMES.get(model_type, {})
    return models.get(capability)

def get_api_url(model_type: ModelType) -> Optional[str]:
    """获取指定模型类型的 API URL"""
    return API_URLS.get(model_type)

def get_api_key_name(model_type: ModelType) -> Optional[str]:
    """获取指定模型类型的 API Key Name"""
    return API_KEY_NAME.get(model_type)

def validate_model(model_type: ModelType, model_name: str) -> bool:
    """验证模型名称是否属于指定的模型类型"""
    models = MODEL_NAMES.get(model_type, {})
    return model_name in models.values()