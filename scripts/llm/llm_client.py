import os
from typing import Optional, Dict, List
import logging.config
from pathlib import Path
import base64
from openai import OpenAI
from .llm_type import ModelType, ModelCapability, get_model_by_capability, get_api_url, get_api_key_name
from utils import setup_logging

logger = logging.getLogger(__name__)


class LLMClient:
    def __init__(self, model_type: ModelType, is_vl=False):
        # 模型类型
        self._model_type = model_type
        # 是否视觉识别
        self._is_vl = is_vl
        api_key_arg = get_api_key_name(model_type)
        if api_key_arg is None:
            logger.error("error model type: %s", model_type)
            raise ValueError(f"Unsupported model type: {model_type}")

        if is_vl:
            self._model_name = get_model_by_capability(
                self._model_type, ModelCapability.VL)
        else:
            self._model_name = get_model_by_capability(
                self._model_type, ModelCapability.MAIN)

        self._client = OpenAI(
            api_key=os.getenv(api_key_arg),
            base_url=get_api_url(self._model_type)
        )
        logger.info("model: %s client created", self._model_name)

    def _is_valid_file_path(self, path: str) -> bool:
        """
        判断字符串是否是一个有效的文件路径（文件必须存在）
        Args:
            path: 要检查的文件路径字符串
        Returns:
            bool: 如果是有效的文件路径且文件存在返回 True，否则返回 False
        """
        if not path:
            return False

        try:
            # 规范化路径（处理 ../ 和 ./ 等）
            abs_path = os.path.abspath(path)
            # 检查是否是文件且存在
            return os.path.isfile(abs_path)

        except Exception as e:
            logger.error("Error checking file path %s: %s", path, str(e))
            return False

    def _encode_image(self, image_path: str) -> str:
        """将图片转换为 base64 编码"""
        with open(image_path, 'rb') as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def _get_user_content(self, prompt: str, img: Optional[str] = None) -> List[Dict[str, object]]:
        """生成prompt中的用户提示词

        Args:
            prompt (str): user prompt
            img (Optional[str], optional): 需要处理的图片文件地址,  Defaults to None.

        Returns:
            _type_: List[Dict[str, obj]]
        """
        if img:
            if self._is_valid_file_path(img):
                # 有效的图片文件
                img_base64 = self._encode_image(img)
                file_format = Path(img).suffix.strip('.')
                user_content = [
                    {"type": "image_url",
                     "image_url": {"url": f"data:image/{file_format};base64,{img_base64}"}},
                    {"type": "text", "text": prompt},
                ]
            else:
                # img作为url传入
                user_content = [
                    {"type": "image_url",
                     "image_url": {"url": img}},
                    {"type": "text", "text": prompt},
                ]
        else:
            user_content = [{"type": "text", "text": prompt}]
        return user_content

    def get_from_llm(self, user_prompt: str, system_prompt: Optional[str] = None, img: Optional[str] = None):
        """
        从大模型获取响应
        Args:
            user_prompt: 用户提示词
            system_prompt: 系统提示词（可选）
            img: 图片路径或URL（可选）
        Returns:
            str: 模型响应
        Raises:
            SystemExit: 当遇到 HTTP 错误时退出程序
        """
        try:
            user_content = self._get_user_content(user_prompt, img)
            if system_prompt:
                completion = self._client.chat.completions.create(
                    model=self._model_name,
                    messages=[
                        {'role': 'system', 'content': system_prompt},
                        {'role': 'user', 'content': user_content}],
                )
            else:
                completion = self._client.chat.completions.create(
                    model=self._model_name,
                    messages=[{'role': 'user', 'content': user_content}],
                )

            response_token = completion.usage.completion_tokens
            input_token = completion.usage.prompt_tokens
            total_token = completion.usage.total_tokens
            logger.info(
                "model: %s, response token: %s, prompt token: %s, total token: %s",
                self._model_name, response_token, input_token, total_token)
            logger.debug(completion.choices[0].message)
            return completion.choices[0].message.content

        except Exception as e:
            logger.error("Error in get_from_llm: %s", str(e))
            raise e


def llm_test():
    llm_client = LLMClient(ModelType.QWEN, is_vl=True)
    user_prompt = """这张图片的内容是中文的紫微斗数全书里的部分内容。
    请识别图片中出现的文字， 根据图片原有的格式生成对应格式的Markdown文本。
    注意：请保持和图片一致，不要增加或者删改文字。保留标题和章节格式，最终以Markdown格式输出, 不要额外添加非Markdown格式的字符"""
    # system_prompt = "识别图片中出现的文字，根据图片原有的格式生成对应格式的Markdown文本"
    img_file = "/Users/<USER>/Project/auto_screenshot/screenshot/2.png"
    response = llm_client.get_from_llm(user_prompt=user_prompt, img=img_file)
    # print(response.choices[0])
    output_file = "test2.md"
    with open(output_file, "w", encoding="utf-8") as fw:
        fw.write(response)

    # response2 = client.chat.completions.create(
    #    model="ep-20241217120600-bjcgb",
    #    messages=[
    #        {
    #            "role": "user",
    #            "content": [
    #                {"type": "text", "text":  text_prompt + '\n\n' + response.choices[0].message.content},
    #            ],
    #        }
    #    ],
    # )
    # print(response2.choices[0])


if __name__ == '__main__':
    # config = configparser.ConfigParser()
    # config.read('.conf')
    # logging.config.fileConfig(BASE_DIR + 'logging.ini', disable_existing_loggers=False, defaults={'logpath': '..\log'})
    setup_logging()
    llm_test()
    # c = LLMClient(ModelType.DOUBAO)
    # print(c._is_valid_file_path('..\data\小红书\亦铭文化_names_1.json'))
